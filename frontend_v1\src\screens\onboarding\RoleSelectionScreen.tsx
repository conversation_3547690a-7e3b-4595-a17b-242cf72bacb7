import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
  Dimensions,
} from 'react-native';

import { ModernStaticGradientBackgroundDark } from '../../components/ui/ModernStaticGradientBackground';
import { SafeAreaScreen } from '../../components/ui/SafeAreaWrapper';
import { Colors } from '../../constants/Colors';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';

interface RoleSelectionScreenProps {
  onRoleSelected: (role: 'customer' | 'service_provider') => void;
  onBack: () => void;
}

const { width, height } = Dimensions.get('window');

export const RoleSelectionScreen: React.FC<RoleSelectionScreenProps> = ({
  onRoleSelected,
  onBack,
}) => {
  const [selectedRole, setSelectedRole] = useState<
    'customer' | 'service_provider' | null
  >(null);

  // Animation refs
  const containerOpacity = useRef(new Animated.Value(0)).current;
  const containerTranslateY = useRef(new Animated.Value(50)).current;
  const titleOpacity = useRef(new Animated.Value(0)).current;
  const titleTranslateY = useRef(new Animated.Value(30)).current;
  const cardsOpacity = useRef(new Animated.Value(0)).current;
  const cardsTranslateY = useRef(new Animated.Value(40)).current;

  useEffect(() => {
    const animationDuration = 600;
    const sequenceDelay = 200;

    // Start container animation
    Animated.parallel([
      Animated.timing(containerOpacity, {
        toValue: 1,
        duration: animationDuration,
        useNativeDriver: true,
      }),
      Animated.spring(containerTranslateY, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
    ]).start();

    // Start title animation
    setTimeout(() => {
      Animated.parallel([
        Animated.timing(titleOpacity, {
          toValue: 1,
          duration: animationDuration,
          useNativeDriver: true,
        }),
        Animated.spring(titleTranslateY, {
          toValue: 0,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
      ]).start();
    }, sequenceDelay);

    // Start cards animation
    setTimeout(() => {
      Animated.parallel([
        Animated.timing(cardsOpacity, {
          toValue: 1,
          duration: animationDuration,
          useNativeDriver: true,
        }),
        Animated.spring(cardsTranslateY, {
          toValue: 0,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
      ]).start();
    }, sequenceDelay * 2);
  }, []);

  const handleRoleSelection = (role: 'customer' | 'service_provider') => {
    setSelectedRole(role);
    // Small delay for visual feedback
    setTimeout(() => {
      onRoleSelected(role);
    }, 150);
  };

  return (
    <SafeAreaScreen
      backgroundColor={Colors.sage900}
      statusBarStyle="light-content"
      respectNotch={true}
      respectGestures={true}
      testID="role-selection-screen">
      <ModernStaticGradientBackgroundDark variant="onboarding">
        <Animated.View
          style={[
            styles.container,
            {
              opacity: containerOpacity,
              transform: [{ translateY: containerTranslateY }],
            },
          ]}>
          {/* Back Button */}
          <TouchableOpacity
            style={styles.backButton}
            onPress={onBack}
            accessibilityRole="button"
            accessibilityLabel="Go back"
            testID="back-button">
            <Ionicons name="chevron-back" size={24} color={Colors.white} />
          </TouchableOpacity>

          {/* Title Section */}
          <Animated.View
            style={[
              styles.titleContainer,
              {
                opacity: titleOpacity,
                transform: [{ translateY: titleTranslateY }],
              },
            ]}>
            <Text style={styles.titleText}>What brings you to Vierla?</Text>
            <Text style={styles.subtitleText}>
              Choose your account type to get a personalized experience
            </Text>
          </Animated.View>

          {/* Role Cards */}
          <Animated.View
            style={[
              styles.cardsContainer,
              {
                opacity: cardsOpacity,
                transform: [{ translateY: cardsTranslateY }],
              },
            ]}>
            {/* Customer Card */}
            <TouchableOpacity
              style={[
                styles.roleCard,
                selectedRole === 'customer' && styles.selectedCard,
              ]}
              onPress={() => handleRoleSelection('customer')}
              accessibilityRole="button"
              accessibilityLabel="I'm looking for services"
              testID="customer-role-button">
              <View style={styles.cardIconContainer}>
                <Ionicons
                  name="search-outline"
                  size={48}
                  color={
                    selectedRole === 'customer' ? Colors.white : Colors.sage400
                  }
                />
              </View>

              <Text
                style={[
                  styles.cardTitle,
                  selectedRole === 'customer' && styles.selectedCardTitle,
                ]}>
                I'm looking for services
              </Text>

              <Text
                style={[
                  styles.cardDescription,
                  selectedRole === 'customer' && styles.selectedCardDescription,
                ]}>
                Find and book beauty services from trusted professionals
              </Text>

              <View style={styles.cardFeatures}>
                <Text
                  style={[
                    styles.cardFeature,
                    selectedRole === 'customer' && styles.selectedCardFeature,
                  ]}>
                  • Discover local providers
                </Text>
                <Text
                  style={[
                    styles.cardFeature,
                    selectedRole === 'customer' && styles.selectedCardFeature,
                  ]}>
                  • Book appointments instantly
                </Text>
                <Text
                  style={[
                    styles.cardFeature,
                    selectedRole === 'customer' && styles.selectedCardFeature,
                  ]}>
                  • Manage your bookings
                </Text>
              </View>
            </TouchableOpacity>

            {/* Service Provider Card */}
            <TouchableOpacity
              style={[
                styles.roleCard,
                selectedRole === 'service_provider' && styles.selectedCard,
              ]}
              onPress={() => handleRoleSelection('service_provider')}
              accessibilityRole="button"
              accessibilityLabel="I offer services"
              testID="provider-role-button">
              <View style={styles.cardIconContainer}>
                <Ionicons
                  name="briefcase-outline"
                  size={48}
                  color={
                    selectedRole === 'service_provider'
                      ? Colors.white
                      : Colors.sage400
                  }
                />
              </View>

              <Text
                style={[
                  styles.cardTitle,
                  selectedRole === 'service_provider' &&
                    styles.selectedCardTitle,
                ]}>
                I offer services
              </Text>

              <Text
                style={[
                  styles.cardDescription,
                  selectedRole === 'service_provider' &&
                    styles.selectedCardDescription,
                ]}>
                Grow your beauty business and reach new clients
              </Text>

              <View style={styles.cardFeatures}>
                <Text
                  style={[
                    styles.cardFeature,
                    selectedRole === 'service_provider' &&
                      styles.selectedCardFeature,
                  ]}>
                  • Manage appointments
                </Text>
                <Text
                  style={[
                    styles.cardFeature,
                    selectedRole === 'service_provider' &&
                      styles.selectedCardFeature,
                  ]}>
                  • Showcase services
                </Text>
                <Text
                  style={[
                    styles.cardFeature,
                    selectedRole === 'service_provider' &&
                      styles.selectedCardFeature,
                  ]}>
                  • Grow your business
                </Text>
              </View>
            </TouchableOpacity>
          </Animated.View>
        </Animated.View>
      </ModernStaticGradientBackgroundDark>
    </SafeAreaScreen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: getResponsiveSpacing(24),
    paddingTop: getResponsiveSpacing(20),
  },
  backButton: {
    alignSelf: 'flex-start',
    padding: getResponsiveSpacing(8),
    marginBottom: getResponsiveSpacing(20),
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(40),
  },
  titleText: {
    fontSize: getResponsiveFontSize(28),
    fontWeight: '700',
    color: Colors.white,
    textAlign: 'center',
    marginBottom: getResponsiveSpacing(12),
    lineHeight: getResponsiveFontSize(34),
  },
  subtitleText: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '400',
    color: Colors.sage100,
    textAlign: 'center',
    lineHeight: getResponsiveFontSize(22),
    opacity: 0.9,
  },
  cardsContainer: {
    flex: 1,
    gap: getResponsiveSpacing(16),
    paddingBottom: getResponsiveSpacing(20), // Ensure bottom spacing
    justifyContent: height < 700 ? 'flex-start' : 'center', // Adjust for smaller screens
  },
  roleCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: getResponsiveSpacing(16),
    padding: getResponsiveSpacing(height < 700 ? 18 : 24), // Adjust padding for smaller screens
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    minHeight: getResponsiveSpacing(height < 700 ? 160 : 180), // Ensure minimum height
    justifyContent: 'center',
  },
  selectedCard: {
    backgroundColor: Colors.sage400,
    borderColor: Colors.sage300,
  },
  cardIconContainer: {
    marginBottom: getResponsiveSpacing(16),
  },
  cardTitle: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '600',
    color: Colors.white,
    textAlign: 'center',
    marginBottom: getResponsiveSpacing(8),
  },
  selectedCardTitle: {
    color: Colors.white,
  },
  cardDescription: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '400',
    color: Colors.sage100,
    textAlign: 'center',
    marginBottom: getResponsiveSpacing(16),
    lineHeight: getResponsiveFontSize(20),
  },
  selectedCardDescription: {
    color: Colors.white,
    opacity: 0.9,
  },
  cardFeatures: {
    alignItems: 'flex-start',
    width: '100%',
  },
  cardFeature: {
    fontSize: getResponsiveFontSize(14),
    color: Colors.sage200,
    marginBottom: getResponsiveSpacing(4),
  },
  selectedCardFeature: {
    color: Colors.white,
    opacity: 0.9,
  },
});
