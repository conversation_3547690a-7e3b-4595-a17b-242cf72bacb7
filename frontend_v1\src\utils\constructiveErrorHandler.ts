/**
 * Constructive Error Handler
 * 
 * Transforms generic error messages into constructive, solution-oriented messages
 * that help users understand what went wrong and how to fix it.
 * 
 * Message Structure: [ACKNOWLEDGMENT] + [PROBLEM] + [SOLUTION] + [ALTERNATIVE]
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { <PERSON><PERSON>, AlertButton } from 'react-native';
import { VALIDATION_ERRORS, NETWORK_ERRORS, BOOKING_ERRORS, SYSTEM_ERRORS } from '../constants/errorMessages';

// Error context interface
export interface ErrorContext {
  action?: string; // What the user was trying to do
  component?: string; // Where the error occurred
  userInput?: any; // What the user entered
  networkStatus?: 'online' | 'offline' | 'slow';
  retryCount?: number;
  metadata?: Record<string, any>;
}

// Enhanced error message interface
export interface ConstructiveErrorMessage {
  title: string;
  message: string;
  primaryAction: AlertButton;
  secondaryAction?: AlertButton;
  additionalActions?: AlertButton[];
  helpUrl?: string;
  reportable?: boolean;
}

// Error pattern matching
const ERROR_PATTERNS = {
  // Network errors
  network: {
    patterns: [
      /network|connection|timeout|fetch|request failed/i,
      /ERR_NETWORK|ERR_INTERNET_DISCONNECTED/i,
      /Unable to resolve host|getaddrinfo failed/i,
    ],
    handler: (error: Error, context: ErrorContext): ConstructiveErrorMessage => ({
      title: "Connection Issue",
      message: context.networkStatus === 'offline' 
        ? "You're currently offline. Please check your internet connection and try again."
        : "We're having trouble connecting to our servers. This usually resolves quickly.",
      primaryAction: {
        text: "Try Again",
        onPress: () => context.metadata?.retryAction?.(),
      },
      secondaryAction: {
        text: "Check Connection",
        onPress: () => {
          // Open device network settings or show connection help
          Alert.alert(
            "Check Your Connection",
            "1. Make sure Wi-Fi or mobile data is enabled\n2. Try opening a website in your browser\n3. Move closer to your Wi-Fi router if using Wi-Fi",
            [{ text: "Got it" }]
          );
        },
      },
      helpUrl: "https://help.vierla.com/connection-issues",
      reportable: false,
    }),
  },

  // Validation errors
  validation: {
    patterns: [
      /validation|invalid|required|format/i,
      /must be|should be|cannot be empty/i,
    ],
    handler: (error: Error, context: ErrorContext): ConstructiveErrorMessage => {
      const fieldName = context.metadata?.fieldName || 'field';
      return {
        title: "Please Check Your Information",
        message: `There's an issue with your ${fieldName}. ${error.message}`,
        primaryAction: {
          text: "Fix It",
          onPress: () => {
            // Focus the problematic field if available
            context.metadata?.focusField?.();
          },
        },
        secondaryAction: {
          text: "Need Help?",
          onPress: () => {
            Alert.alert(
              `Help with ${fieldName}`,
              context.metadata?.helpText || "Please ensure all required information is filled in correctly.",
              [{ text: "OK" }]
            );
          },
        },
        reportable: false,
      };
    },
  },

  // Authentication errors
  auth: {
    patterns: [
      /unauthorized|authentication|login|token|session/i,
      /401|403/i,
    ],
    handler: (error: Error, context: ErrorContext): ConstructiveErrorMessage => ({
      title: "Please Sign In Again",
      message: "Your session has expired for security. Please sign in again to continue.",
      primaryAction: {
        text: "Sign In",
        onPress: () => context.metadata?.navigateToLogin?.(),
      },
      secondaryAction: {
        text: "Cancel",
        style: "cancel",
      },
      reportable: false,
    }),
  },

  // Server errors
  server: {
    patterns: [
      /500|502|503|504|server error|internal error/i,
      /service unavailable|bad gateway/i,
    ],
    handler: (error: Error, context: ErrorContext): ConstructiveErrorMessage => ({
      title: "Service Temporarily Unavailable",
      message: "Our servers are experiencing high demand. This usually resolves within a few minutes.",
      primaryAction: {
        text: "Try Again",
        onPress: () => context.metadata?.retryAction?.(),
      },
      secondaryAction: {
        text: "Check Status",
        onPress: () => {
          // Open status page or show status info
          Alert.alert(
            "Service Status",
            "You can check our service status at status.vierla.com or try again in a few minutes.",
            [{ text: "OK" }]
          );
        },
      },
      helpUrl: "https://status.vierla.com",
      reportable: true,
    }),
  },

  // Booking specific errors
  booking: {
    patterns: [
      /booking|appointment|schedule|availability/i,
      /time slot|provider|service/i,
    ],
    handler: (error: Error, context: ErrorContext): ConstructiveErrorMessage => {
      if (error.message.includes('unavailable')) {
        return {
          title: "Time Slot No Longer Available",
          message: "Someone else just booked this time slot. Let's find you another great option.",
          primaryAction: {
            text: "See Other Times",
            onPress: () => context.metadata?.showAlternatives?.(),
          },
          secondaryAction: {
            text: "Choose Different Date",
            onPress: () => context.metadata?.showCalendar?.(),
          },
          reportable: false,
        };
      }
      
      return {
        title: "Booking Issue",
        message: "We couldn't complete your booking right now. Your information is saved and we can try again.",
        primaryAction: {
          text: "Try Again",
          onPress: () => context.metadata?.retryBooking?.(),
        },
        secondaryAction: {
          text: "Save for Later",
          onPress: () => context.metadata?.saveDraft?.(),
        },
        reportable: true,
      };
    },
  },
};

/**
 * Transform a generic error into a constructive, solution-oriented message
 */
export const createConstructiveError = (
  error: Error | string,
  context: ErrorContext = {}
): ConstructiveErrorMessage => {
  const errorMessage = typeof error === 'string' ? error : error.message;
  const errorObj = typeof error === 'string' ? new Error(error) : error;

  // Try to match error patterns
  for (const [category, config] of Object.entries(ERROR_PATTERNS)) {
    for (const pattern of config.patterns) {
      if (pattern.test(errorMessage)) {
        return config.handler(errorObj, context);
      }
    }
  }

  // Default constructive error for unmatched patterns
  return {
    title: "Something Unexpected Happened",
    message: context.action 
      ? `We couldn't complete "${context.action}" right now. This is usually temporary and resolves quickly.`
      : "We encountered an unexpected issue. This is usually temporary and resolves quickly.",
    primaryAction: {
      text: "Try Again",
      onPress: () => context.metadata?.retryAction?.(),
    },
    secondaryAction: {
      text: "Contact Support",
      onPress: () => {
        Alert.alert(
          "Get Help",
          "Our support team is here to help. You can reach us through the app's help section <NAME_EMAIL>",
          [{ text: "OK" }]
        );
      },
    },
    helpUrl: "https://help.vierla.com",
    reportable: true,
  };
};

/**
 * Show a constructive error alert
 */
export const showConstructiveError = (
  error: Error | string,
  context: ErrorContext = {}
): void => {
  const constructiveError = createConstructiveError(error, context);
  
  const buttons: AlertButton[] = [constructiveError.primaryAction];
  
  if (constructiveError.secondaryAction) {
    buttons.push(constructiveError.secondaryAction);
  }
  
  if (constructiveError.additionalActions) {
    buttons.push(...constructiveError.additionalActions);
  }

  Alert.alert(
    constructiveError.title,
    constructiveError.message,
    buttons,
    { cancelable: true }
  );
};

/**
 * Enhanced Alert.alert replacement with constructive messaging
 */
export const constructiveAlert = {
  error: (error: Error | string, context?: ErrorContext) => {
    showConstructiveError(error, context);
  },
  
  success: (message: string, onContinue?: () => void) => {
    Alert.alert(
      "Success!",
      message,
      [{ text: "Continue", onPress: onContinue }]
    );
  },
  
  confirm: (
    title: string,
    message: string,
    onConfirm: () => void,
    onCancel?: () => void
  ) => {
    Alert.alert(
      title,
      message,
      [
        { text: "Cancel", style: "cancel", onPress: onCancel },
        { text: "Confirm", onPress: onConfirm },
      ]
    );
  },
  
  info: (title: string, message: string, onOk?: () => void) => {
    Alert.alert(title, message, [{ text: "Got it", onPress: onOk }]);
  },
};

/**
 * Utility to replace generic Alert.alert calls
 */
export const replaceGenericAlert = (
  title: string,
  message: string,
  context: ErrorContext = {}
): void => {
  // Check if this is a generic error message that should be improved
  const genericPatterns = [
    /error|something went wrong|unexpected|failed/i,
    /try again|please try again later/i,
  ];
  
  const isGeneric = genericPatterns.some(pattern => 
    pattern.test(title) || pattern.test(message)
  );
  
  if (isGeneric) {
    showConstructiveError(message, context);
  } else {
    Alert.alert(title, message);
  }
};
