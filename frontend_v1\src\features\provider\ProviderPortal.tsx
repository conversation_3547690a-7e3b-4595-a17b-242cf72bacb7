/**
 * Provider Portal - Main Entry Point
 * 
 * Comprehensive service provider portal with modular architecture
 * for independent development and deployment.
 * 
 * Features:
 * - Modular microfrontend architecture
 * - Independent routing and state management
 * - Real-time dashboard updates
 * - Comprehensive job management
 * - Earnings analytics
 * - Availability management
 * - Profile optimization tools
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  Text,
  TouchableOpacity,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../contexts/ThemeContext';
import { useI18n } from '../../contexts/I18nContext';
import { ProviderDashboard } from './dashboard/ProviderDashboard';
import { ProviderJobManagement } from './jobs/ProviderJobManagement';
import { ProviderAvailabilityManagement } from './availability/ProviderAvailabilityManagement';
import { ProviderEarningsAnalytics } from './earnings/ProviderEarningsAnalytics';
import { ProviderProfileManagement } from './profile/ProviderProfileManagement';
import { ProviderSettings } from './settings/ProviderSettings';
import { ProviderNavigation } from './navigation/ProviderNavigation';
import { ProviderHeader } from './components/ProviderHeader';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { useProviderStore } from '../../store/providerSlice';
import { useProviderAuth } from '../../hooks/useProviderAuth';

// Provider portal sections
export type ProviderSection = 
  | 'dashboard' 
  | 'jobs' 
  | 'availability' 
  | 'earnings' 
  | 'profile' 
  | 'settings';

export interface ProviderPortalProps {
  initialSection?: ProviderSection;
  embedded?: boolean; // For microfrontend embedding
}

/**
 * Main Provider Portal Component
 */
export const ProviderPortal: React.FC<ProviderPortalProps> = ({
  initialSection = 'dashboard',
  embedded = false,
}) => {
  const { colors } = useTheme();
  const { t } = useI18n();
  const navigation = useNavigation();
  
  // State management
  const [activeSection, setActiveSection] = useState<ProviderSection>(initialSection);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Provider authentication and data
  const { provider, isAuthenticated, isLoading } = useProviderAuth();
  const { 
    dashboard,
    jobs,
    earnings,
    availability,
    profile,
    refreshDashboard,
    refreshJobs,
    refreshEarnings,
  } = useProviderStore();

  // Initialize provider portal
  useEffect(() => {
    if (isAuthenticated && provider) {
      initializePortal();
    }
  }, [isAuthenticated, provider]);

  // Initialize portal data
  const initializePortal = async () => {
    try {
      await Promise.all([
        refreshDashboard(),
        refreshJobs(),
        refreshEarnings(),
      ]);
    } catch (error) {
      console.error('Failed to initialize provider portal:', error);
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await initializePortal();
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle section change
  const handleSectionChange = (section: ProviderSection) => {
    setActiveSection(section);
    
    // Track navigation for analytics
    if (!embedded) {
      // Analytics tracking would go here
      console.log(`Provider navigated to: ${section}`);
    }
  };

  // Render section content
  const renderSectionContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return <ProviderDashboard />;
      case 'jobs':
        return <ProviderJobManagement />;
      case 'availability':
        return <ProviderAvailabilityManagement />;
      case 'earnings':
        return <ProviderEarningsAnalytics />;
      case 'profile':
        return <ProviderProfileManagement />;
      case 'settings':
        return <ProviderSettings />;
      default:
        return <ProviderDashboard />;
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <LoadingSpinner size="large" />
        <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
          {t('provider.loading')}
        </Text>
      </View>
    );
  }

  // Authentication required
  if (!isAuthenticated) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ProviderAuthRequired />
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      {/* Header */}
      {!embedded && (
        <ProviderHeader 
          provider={provider}
          activeSection={activeSection}
          onSectionChange={handleSectionChange}
        />
      )}

      {/* Navigation */}
      <ProviderNavigation
        activeSection={activeSection}
        onSectionChange={handleSectionChange}
        embedded={embedded}
      />

      {/* Main Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderSectionContent()}
      </ScrollView>
    </View>
  );
};

/**
 * Provider Job Management Component
 */
const ProviderJobManagement: React.FC = () => {
  const { t } = useI18n();
  
  return (
    <View style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>{t('provider.jobs.title')}</Text>
      {/* Job management content will be implemented in separate component */}
    </View>
  );
};

/**
 * Provider Availability Management Component
 */
const ProviderAvailabilityManagement: React.FC = () => {
  const { t } = useI18n();
  
  return (
    <View style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>{t('provider.availability.title')}</Text>
      {/* Availability management content will be implemented in separate component */}
    </View>
  );
};

/**
 * Provider Earnings Analytics Component
 */
const ProviderEarningsAnalytics: React.FC = () => {
  const { t } = useI18n();
  
  return (
    <View style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>{t('provider.earnings.title')}</Text>
      {/* Earnings analytics content will be implemented in separate component */}
    </View>
  );
};

/**
 * Provider Profile Management Component
 */
const ProviderProfileManagement: React.FC = () => {
  const { t } = useI18n();
  
  return (
    <View style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>{t('provider.profile.title')}</Text>
      {/* Profile management content will be implemented in separate component */}
    </View>
  );
};

/**
 * Provider Settings Component
 */
const ProviderSettings: React.FC = () => {
  const { t } = useI18n();
  
  return (
    <View style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>{t('provider.settings.title')}</Text>
      {/* Settings content will be implemented in separate component */}
    </View>
  );
};

/**
 * Loading Spinner Component
 */
const LoadingSpinner: React.FC<{ size?: 'small' | 'large' }> = ({ size = 'small' }) => {
  const { colors } = useTheme();
  
  return (
    <View style={[
      styles.spinner,
      { 
        width: size === 'large' ? 40 : 20,
        height: size === 'large' ? 40 : 20,
        borderColor: colors.primary[500],
      }
    ]} />
  );
};

/**
 * Provider Authentication Required Component
 */
const ProviderAuthRequired: React.FC = () => {
  const { t } = useI18n();
  const { colors } = useTheme();
  const navigation = useNavigation();
  
  const handleSignIn = () => {
    navigation.navigate('ProviderAuth' as never);
  };
  
  return (
    <View style={styles.authRequired}>
      <Text style={[styles.authTitle, { color: colors.text.primary }]}>
        {t('provider.auth.required')}
      </Text>
      <Text style={[styles.authMessage, { color: colors.text.secondary }]}>
        {t('provider.auth.message')}
      </Text>
      <TouchableOpacity
        style={[styles.authButton, { backgroundColor: colors.primary[500] }]}
        onPress={handleSignIn}
      >
        <Text style={[styles.authButtonText, { color: colors.white }]}>
          {t('provider.auth.signIn')}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  sectionContainer: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  spinner: {
    borderWidth: 2,
    borderTopColor: 'transparent',
    borderRadius: 20,
    // Add rotation animation
  },
  authRequired: {
    padding: 40,
    alignItems: 'center',
  },
  authTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  authMessage: {
    fontSize: 16,
    marginBottom: 32,
    textAlign: 'center',
    lineHeight: 24,
  },
  authButton: {
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 8,
    minWidth: 200,
    alignItems: 'center',
  },
  authButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ProviderPortal;
