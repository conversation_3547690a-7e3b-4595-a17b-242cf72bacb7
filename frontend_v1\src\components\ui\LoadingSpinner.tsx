/**
 * LoadingSpinner Component - Loading Spinner UI Component
 * 
 * A simple loading spinner component with customizable size and color.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import {
  ActivityIndicator,
  View,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface LoadingSpinnerProps {
  size?: 'small' | 'large' | number;
  color?: string;
  style?: ViewStyle;
  testID?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'large',
  color,
  style,
  testID,
}) => {
  const { colors } = useTheme();

  const spinnerColor = color || colors.primary[500];

  return (
    <View style={[styles.container, style]}>
      <ActivityIndicator
        size={size}
        color={spinnerColor}
        testID={testID}
        accessibilityLabel="Loading"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});
