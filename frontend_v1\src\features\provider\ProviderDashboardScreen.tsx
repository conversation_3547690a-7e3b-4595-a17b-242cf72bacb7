/**
 * Provider Dashboard Screen - Main dashboard for service providers
 *
 * Screen Contract:
 * - Displays comprehensive business analytics and metrics
 * - Shows upcoming bookings and recent activity
 * - Provides quick access to key provider functions
 * - Real-time updates for bookings and revenue
 * - Performance insights and business recommendations
 * - Responsive design for all screen sizes
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Dimensions,
  Alert,
} from 'react-native';
import { SafeAreaWrapper } from '../../components/ui/SafeAreaWrapper';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import styled from 'styled-components/native';

import { useProviderStore } from '../../store/providerSlice';
import { useAuthStore } from '../../store/authSlice';
import { Colors } from '../../constants/Colors';
import { LoadingSpinner } from '../../components/common/LoadingSpinner';
import { ErrorMessage } from '../../components/common/ErrorMessage';

const { width } = Dimensions.get('window');

interface DashboardCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: string;
  color: string;
  onPress?: () => void;
}

const ProviderDashboardScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAuthStore();
  const {
    profile,
    isLoading,
    error,
    analyticsLoading,
    loadProfile,
    refreshAnalytics,
    clearError,
  } = useProviderStore();

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (user?.id) {
      loadProfile(user.id);
    }
  }, [user?.id, loadProfile]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      if (user?.id) {
        await Promise.all([
          loadProfile(user.id),
          refreshAnalytics(),
        ]);
      }
    } catch (error) {
      console.error('Failed to refresh dashboard:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  const formatPercentage = (value: number): string => {
    return `${value.toFixed(1)}%`;
  };

  const DashboardCard: React.FC<DashboardCardProps> = ({
    title,
    value,
    subtitle,
    icon,
    color,
    onPress,
  }) => (
    <CardContainer onPress={onPress} disabled={!onPress}>
      <CardHeader>
        <CardIcon name={icon as any} size={24} color={color} />
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardValue>{value}</CardValue>
      {subtitle && <CardSubtitle>{subtitle}</CardSubtitle>}
    </CardContainer>
  );

  if (isLoading && !profile) {
    return (
      <Container>
        <LoadingSpinner size="large" />
      </Container>
    );
  }

  if (error && !profile) {
    return (
      <Container>
        <ErrorMessage
          message={error}
          onRetry={() => user?.id && loadProfile(user.id)}
          onDismiss={clearError}
        />
      </Container>
    );
  }

  if (!profile) {
    return (
      <Container>
        <EmptyStateContainer>
          <EmptyStateIcon name="business-outline" size={64} color={Colors.light.text} />
          <EmptyStateTitle>Complete Your Profile</EmptyStateTitle>
          <EmptyStateText>
            Set up your business profile to start receiving bookings
          </EmptyStateText>
          <SetupButton onPress={() => navigation.navigate('ProviderSetup' as never)}>
            <SetupButtonText>Setup Profile</SetupButtonText>
          </SetupButton>
        </EmptyStateContainer>
      </Container>
    );
  }

  const analytics = profile.analytics;

  return (
    <Container>
      <SafeAreaWrapper style={{ flex: 1 }}>
        <Header>
          <HeaderContent>
            <WelcomeText>Welcome back,</WelcomeText>
            <BusinessName>{profile.businessName}</BusinessName>
          </HeaderContent>
          <HeaderActions>
            <HeaderButton onPress={() => navigation.navigate('ProviderSettings' as never)}>
              <Ionicons name="settings-outline" size={24} color={Colors.light.text} />
            </HeaderButton>
            <HeaderButton onPress={() => navigation.navigate('ProviderProfile' as never)}>
              <Ionicons name="person-outline" size={24} color={Colors.light.text} />
            </HeaderButton>
          </HeaderActions>
        </Header>

        <ScrollView
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[Colors.light.primary]}
            />
          }
        >
          {/* Quick Stats */}
          <Section>
            <SectionTitle>Today's Overview</SectionTitle>
            <StatsGrid>
              <DashboardCard
                title="Total Revenue"
                value={formatCurrency(analytics.totalRevenue)}
                subtitle="All time"
                icon="cash-outline"
                color={Colors.light.success}
                onPress={() => navigation.navigate('RevenueAnalytics' as never)}
              />
              <DashboardCard
                title="Bookings"
                value={analytics.totalBookings}
                subtitle={`${analytics.completedBookings} completed`}
                icon="calendar-outline"
                color={Colors.light.primary}
                onPress={() => navigation.navigate('BookingManagement' as never)}
              />
              <DashboardCard
                title="Rating"
                value={analytics.averageRating.toFixed(1)}
                subtitle={`${analytics.totalReviews} reviews`}
                icon="star-outline"
                color={Colors.light.warning}
                onPress={() => navigation.navigate('ReviewsManagement' as never)}
              />
              <DashboardCard
                title="Completion Rate"
                value={formatPercentage(analytics.completionRate)}
                subtitle="This month"
                icon="checkmark-circle-outline"
                color={Colors.light.info}
              />
            </StatsGrid>
          </Section>

          {/* Quick Actions */}
          <Section>
            <SectionTitle>Quick Actions</SectionTitle>
            <ActionsGrid>
              <ActionButton onPress={() => navigation.navigate('ServiceManagement' as never)}>
                <ActionIcon name="list-outline" size={32} color={Colors.light.primary} />
                <ActionText>Manage Services</ActionText>
              </ActionButton>
              <ActionButton onPress={() => navigation.navigate('AvailabilitySettings' as never)}>
                <ActionIcon name="time-outline" size={32} color={Colors.light.primary} />
                <ActionText>Set Availability</ActionText>
              </ActionButton>
              <ActionButton onPress={() => navigation.navigate('PortfolioManagement' as never)}>
                <ActionIcon name="images-outline" size={32} color={Colors.light.primary} />
                <ActionText>Portfolio</ActionText>
              </ActionButton>
              <ActionButton onPress={() => navigation.navigate('ProviderMessaging' as never)}>
                <ActionIcon name="chatbubbles-outline" size={32} color={Colors.light.primary} />
                <ActionText>Messages</ActionText>
              </ActionButton>
            </ActionsGrid>
          </Section>

          {/* Recent Activity */}
          <Section>
            <SectionHeader>
              <SectionTitle>Recent Activity</SectionTitle>
              <ViewAllButton onPress={() => navigation.navigate('ActivityHistory' as never)}>
                <ViewAllText>View All</ViewAllText>
              </ViewAllButton>
            </SectionHeader>
            <ActivityContainer>
              <ActivityItem>
                <ActivityIcon name="calendar" size={20} color={Colors.light.success} />
                <ActivityContent>
                  <ActivityTitle>New booking from Sarah Johnson</ActivityTitle>
                  <ActivityTime>2 hours ago</ActivityTime>
                </ActivityContent>
              </ActivityItem>
              <ActivityItem>
                <ActivityIcon name="star" size={20} color={Colors.light.warning} />
                <ActivityContent>
                  <ActivityTitle>New 5-star review received</ActivityTitle>
                  <ActivityTime>4 hours ago</ActivityTime>
                </ActivityContent>
              </ActivityItem>
              <ActivityItem>
                <ActivityIcon name="cash" size={20} color={Colors.light.success} />
                <ActivityContent>
                  <ActivityTitle>Payment received - $85.00</ActivityTitle>
                  <ActivityTime>6 hours ago</ActivityTime>
                </ActivityContent>
              </ActivityItem>
            </ActivityContainer>
          </Section>

          {/* Performance Insights */}
          <Section>
            <SectionTitle>Performance Insights</SectionTitle>
            <InsightCard>
              <InsightHeader>
                <InsightIcon name="trending-up" size={24} color={Colors.light.success} />
                <InsightTitle>Business Growth</InsightTitle>
              </InsightHeader>
              <InsightText>
                Your bookings increased by 23% this month compared to last month.
                Keep up the great work!
              </InsightText>
            </InsightCard>
            <InsightCard>
              <InsightHeader>
                <InsightIcon name="time" size={24} color={Colors.light.info} />
                <InsightTitle>Peak Hours</InsightTitle>
              </InsightHeader>
              <InsightText>
                Most bookings occur between 2-4 PM. Consider adjusting your availability
                to maximize revenue.
              </InsightText>
            </InsightCard>
          </Section>

          {/* Verification Status */}
          {profile.verification.status !== 'verified' && (
            <Section>
              <VerificationCard>
                <VerificationHeader>
                  <VerificationIcon 
                    name="shield-checkmark-outline" 
                    size={24} 
                    color={Colors.light.warning} 
                  />
                  <VerificationTitle>Complete Verification</VerificationTitle>
                </VerificationHeader>
                <VerificationText>
                  Complete your business verification to build trust with customers
                  and unlock premium features.
                </VerificationText>
                <VerificationButton onPress={() => navigation.navigate('ProviderVerification' as never)}>
                  <VerificationButtonText>Complete Verification</VerificationButtonText>
                </VerificationButton>
              </VerificationCard>
            </Section>
          )}
        </ScrollView>
      </SafeAreaWrapper>
    </Container>
  );
};

const Container = styled.View`
  flex: 1;
  background-color: ${Colors.light.background};
`;

const Header = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: ${Colors.light.surface};
  border-bottom-width: 1px;
  border-bottom-color: ${Colors.light.border};
`;

const HeaderContent = styled.View`
  flex: 1;
`;

const WelcomeText = styled.Text`
  font-size: 14px;
  color: ${Colors.light.textSecondary};
  margin-bottom: 4px;
`;

const BusinessName = styled.Text`
  font-size: 20px;
  font-weight: bold;
  color: ${Colors.light.text};
`;

const HeaderActions = styled.View`
  flex-direction: row;
  gap: 12px;
`;

const HeaderButton = styled.TouchableOpacity`
  padding: 8px;
  border-radius: 8px;
  background-color: ${Colors.light.background};
`;

const Section = styled.View`
  padding: 16px;
`;

const SectionHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const SectionTitle = styled.Text`
  font-size: 18px;
  font-weight: bold;
  color: ${Colors.light.text};
  margin-bottom: 16px;
`;

const ViewAllButton = styled.TouchableOpacity``;

const ViewAllText = styled.Text`
  color: ${Colors.light.primary};
  font-weight: 600;
`;

const StatsGrid = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 12px;
`;

const CardContainer = styled.TouchableOpacity`
  background-color: ${Colors.light.surface};
  border-radius: 12px;
  padding: 16px;
  width: ${(width - 48) / 2}px;
  border-width: 1px;
  border-color: ${Colors.light.border};
`;

const CardHeader = styled.View`
  flex-direction: row;
  align-items: center;
  margin-bottom: 12px;
`;

const CardIcon = styled(Ionicons)`
  margin-right: 8px;
`;

const CardTitle = styled.Text`
  font-size: 14px;
  color: ${Colors.light.textSecondary};
  font-weight: 500;
`;

const CardValue = styled.Text`
  font-size: 24px;
  font-weight: bold;
  color: ${Colors.light.text};
  margin-bottom: 4px;
`;

const CardSubtitle = styled.Text`
  font-size: 12px;
  color: ${Colors.light.textSecondary};
`;

const ActionsGrid = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 12px;
`;

const ActionButton = styled.TouchableOpacity`
  background-color: ${Colors.light.surface};
  border-radius: 12px;
  padding: 20px;
  width: ${(width - 48) / 2}px;
  align-items: center;
  border-width: 1px;
  border-color: ${Colors.light.border};
`;

const ActionIcon = styled(Ionicons)`
  margin-bottom: 8px;
`;

const ActionText = styled.Text`
  font-size: 14px;
  font-weight: 600;
  color: ${Colors.light.text};
  text-align: center;
`;

const ActivityContainer = styled.View`
  background-color: ${Colors.light.surface};
  border-radius: 12px;
  border-width: 1px;
  border-color: ${Colors.light.border};
`;

const ActivityItem = styled.View`
  flex-direction: row;
  align-items: center;
  padding: 16px;
  border-bottom-width: 1px;
  border-bottom-color: ${Colors.light.border};
`;

const ActivityIcon = styled(Ionicons)`
  margin-right: 12px;
`;

const ActivityContent = styled.View`
  flex: 1;
`;

const ActivityTitle = styled.Text`
  font-size: 14px;
  font-weight: 500;
  color: ${Colors.light.text};
  margin-bottom: 4px;
`;

const ActivityTime = styled.Text`
  font-size: 12px;
  color: ${Colors.light.textSecondary};
`;

const InsightCard = styled.View`
  background-color: ${Colors.light.surface};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  border-width: 1px;
  border-color: ${Colors.light.border};
`;

const InsightHeader = styled.View`
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
`;

const InsightIcon = styled(Ionicons)`
  margin-right: 8px;
`;

const InsightTitle = styled.Text`
  font-size: 16px;
  font-weight: 600;
  color: ${Colors.light.text};
`;

const InsightText = styled.Text`
  font-size: 14px;
  color: ${Colors.light.textSecondary};
  line-height: 20px;
`;

const VerificationCard = styled.View`
  background-color: ${Colors.light.warning}20;
  border-radius: 12px;
  padding: 16px;
  border-width: 1px;
  border-color: ${Colors.light.warning};
`;

const VerificationHeader = styled.View`
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
`;

const VerificationIcon = styled(Ionicons)`
  margin-right: 8px;
`;

const VerificationTitle = styled.Text`
  font-size: 16px;
  font-weight: 600;
  color: ${Colors.light.text};
`;

const VerificationText = styled.Text`
  font-size: 14px;
  color: ${Colors.light.textSecondary};
  line-height: 20px;
  margin-bottom: 12px;
`;

const VerificationButton = styled.TouchableOpacity`
  background-color: ${Colors.light.warning};
  border-radius: 8px;
  padding: 12px;
  align-items: center;
`;

const VerificationButtonText = styled.Text`
  color: white;
  font-weight: 600;
  font-size: 14px;
`;

const EmptyStateContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 32px;
`;

const EmptyStateIcon = styled(Ionicons)`
  margin-bottom: 16px;
`;

const EmptyStateTitle = styled.Text`
  font-size: 20px;
  font-weight: bold;
  color: ${Colors.light.text};
  margin-bottom: 8px;
  text-align: center;
`;

const EmptyStateText = styled.Text`
  font-size: 16px;
  color: ${Colors.light.textSecondary};
  text-align: center;
  line-height: 24px;
  margin-bottom: 24px;
`;

const SetupButton = styled.TouchableOpacity`
  background-color: ${Colors.light.primary};
  border-radius: 8px;
  padding: 16px 32px;
`;

const SetupButtonText = styled.Text`
  color: white;
  font-weight: 600;
  font-size: 16px;
`;

export default ProviderDashboardScreen;
