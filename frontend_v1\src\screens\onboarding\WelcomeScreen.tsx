import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
  Dimensions,
} from 'react-native';

import { Logo } from '../../components/atoms/Logo';
import { ModernStaticGradientBackgroundDark } from '../../components/ui/ModernStaticGradientBackground';
import { SafeAreaScreen } from '../../components/ui/SafeAreaWrapper';
import { useSafeThemeWithMediumFix } from '../../hooks/useSafeThemeWithMediumFix';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';

interface WelcomeScreenProps {
  onGetStarted: () => void;
  onSignIn: () => void;
}

const { width, height } = Dimensions.get('window');

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  onGetStarted,
  onSignIn,
}) => {
  const { colors, safeGet, isEmergencyMode } = useSafeThemeWithMediumFix();
  const styles = createStyles(colors, safeGet);

  // Log if we're in emergency mode
  if (isEmergencyMode) {
    console.warn('[WelcomeScreen] Running in emergency theme mode');
  }

  // Animation refs
  const brandOpacity = useRef(new Animated.Value(0)).current;
  const brandTranslateY = useRef(new Animated.Value(30)).current;
  const brandScale = useRef(new Animated.Value(0.9)).current;
  const taglineOpacity = useRef(new Animated.Value(0)).current;
  const taglineTranslateY = useRef(new Animated.Value(20)).current;
  const buttonsOpacity = useRef(new Animated.Value(0)).current;
  const buttonsTranslateY = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    const animationDuration = 800;
    const sequenceDelay = 350;

    // Start brand animation
    setTimeout(() => {
      Animated.parallel([
        Animated.timing(brandOpacity, {
          toValue: 1,
          duration: animationDuration,
          useNativeDriver: true,
        }),
        Animated.spring(brandTranslateY, {
          toValue: 0,
          useNativeDriver: true,
          tension: 120,
          friction: 8,
        }),
        Animated.spring(brandScale, {
          toValue: 1,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
      ]).start();
    }, sequenceDelay);

    // Start tagline animation
    setTimeout(() => {
      Animated.parallel([
        Animated.timing(taglineOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(taglineTranslateY, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]).start();
    }, sequenceDelay * 2);

    // Start buttons animation
    setTimeout(() => {
      Animated.parallel([
        Animated.timing(buttonsOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.spring(buttonsTranslateY, {
          toValue: 0,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
      ]).start();
    }, sequenceDelay * 3);
  }, []);

  return (
    <SafeAreaScreen
      backgroundColor={safeGet('sage900', '#0A2A12')}
      statusBarStyle="light-content"
      respectNotch={true}
      respectGestures={true}
      testID="welcome-screen">
      <ModernStaticGradientBackgroundDark variant="onboarding">
        <View style={styles.container}>
          <View style={styles.contentContainer}>
            {/* Brand Logo with Animation */}
            <Animated.View
              style={[
                styles.brandContainer,
                {
                  opacity: brandOpacity,
                  transform: [
                    { translateY: brandTranslateY },
                    { scale: brandScale },
                  ],
                },
              ]}>
              <Logo size="large" textStyle={{ color: '#FFFFFF' }} />
            </Animated.View>

            {/* Headline and Tagline */}
            <Animated.View
              style={[
                styles.textContainer,
                {
                  opacity: taglineOpacity,
                  transform: [{ translateY: taglineTranslateY }],
                },
              ]}>
              <Text style={styles.headlineText}>
                Your self-care, simplified
              </Text>
              <Text style={styles.subHeadlineText}>
                Find and book trusted beauty experts in your community
              </Text>
            </Animated.View>

            {/* Action Buttons */}
            <Animated.View
              style={[
                styles.actionButtonsContainer,
                {
                  opacity: buttonsOpacity,
                  transform: [{ translateY: buttonsTranslateY }],
                },
              ]}>
              <TouchableOpacity
                style={styles.primaryButton}
                onPress={onGetStarted}
                accessibilityRole="button"
                accessibilityLabel="Get Started with Vierla"
                accessibilityHint="Start the onboarding process"
                testID="get-started-button">
                <Text style={styles.primaryButtonText}>Get Started</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.secondaryButton}
                onPress={onSignIn}
                accessibilityRole="button"
                accessibilityLabel="Sign In to Vierla"
                accessibilityHint="Sign in to your existing account"
                testID="sign-in-button">
                <Text style={styles.secondaryButtonText}>Sign In</Text>
              </TouchableOpacity>
            </Animated.View>
          </View>
        </View>
      </ModernStaticGradientBackgroundDark>
    </SafeAreaScreen>
  );
};

const createStyles = (colors: any, safeGet: (path: string, fallback?: any) => any) => StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(32),
    width: '100%',
  },
  brandContainer: {
    marginBottom: getResponsiveSpacing(60),
    alignItems: 'center',
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(80),
  },
  headlineText: {
    fontSize: getResponsiveFontSize(32),
    fontWeight: '700',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: getResponsiveSpacing(16),
    lineHeight: getResponsiveFontSize(38),
  },
  subHeadlineText: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '400',
    color: safeGet('sage100', '#E1EDE4'),
    textAlign: 'center',
    lineHeight: getResponsiveFontSize(24),
    opacity: 0.9,
  },
  actionButtonsContainer: {
    width: '100%',
    alignItems: 'center',
    gap: getResponsiveSpacing(16),
  },
  primaryButton: {
    backgroundColor: safeGet('sage400', '#5A7A63'),
    paddingVertical: getResponsiveSpacing(16),
    paddingHorizontal: getResponsiveSpacing(32),
    borderRadius: getResponsiveSpacing(12),
    width: '100%',
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  primaryButtonText: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: '#FFFFFF',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    paddingVertical: getResponsiveSpacing(16),
    paddingHorizontal: getResponsiveSpacing(32),
    borderRadius: getResponsiveSpacing(12),
    width: '100%',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: safeGet('sage200', '#C3DAC8'),
  },
  secondaryButtonText: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: safeGet('sage100', '#E1EDE4'),
  },
});
