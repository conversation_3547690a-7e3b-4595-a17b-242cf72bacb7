/**
 * Bottom Navigation Component
 *
 * Implements thumb-friendly bottom navigation for mobile devices
 * replacing traditional hamburger menus with accessible touch targets.
 *
 * Features:
 * - Thumb-friendly positioning and sizing
 * - Proper touch targets (minimum 44x44px)
 * - Accessibility compliance with ARIA labels
 * - Smooth animations and haptic feedback
 * - Badge support for notifications
 * - Safe area handling for modern devices
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useCallback, useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Platform,
  Vibration,
  AccessibilityInfo,
} from 'react-native';
import { getSafeAreaInsets } from '../../utils/responsiveUtils';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';

import { Box } from '../atoms/Box';
import { Text } from '../atoms/Text';
import { useTheme } from '../../contexts/ThemeContext';
import { useSafeTheme } from '../../hooks/useSafeTheme';

// Navigation item interface
export interface BottomNavItem {
  id: string;
  label: string;
  icon: string;
  activeIcon?: string;
  badge?: number | string;
  disabled?: boolean;
  accessibilityLabel?: string;
  accessibilityHint?: string;
}

// Component props
export interface BottomNavigationProps {
  items: BottomNavItem[];
  activeItemId: string;
  onItemPress: (item: BottomNavItem) => void;
  showLabels?: boolean;
  hapticFeedback?: boolean;
  style?: any;
  testID?: string;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

export const BottomNavigation: React.FC<BottomNavigationProps> = ({
  items,
  activeItemId,
  onItemPress,
  showLabels = true,
  hapticFeedback = true,
  style,
  testID = 'bottom-navigation',
}) => {
  const { isDark } = useTheme();
  const { theme, colors, spacing, typography, safeGet } = useSafeTheme();
  const insets = getSafeAreaInsets();
  
  // Animation values
  const activeIndex = items.findIndex(item => item.id === activeItemId);
  const indicatorPosition = useSharedValue(activeIndex);
  const itemAnimations = items.map(() => useSharedValue(0));
  
  // State
  const [screenWidth, setScreenWidth] = useState(Dimensions.get('window').width);

  // Update screen width on orientation change
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenWidth(window.width);
    });

    return () => subscription?.remove();
  }, []);

  // Update indicator position when active item changes
  useEffect(() => {
    const newIndex = items.findIndex(item => item.id === activeItemId);
    indicatorPosition.value = withSpring(newIndex, {
      damping: 15,
      stiffness: 150,
    });
  }, [activeItemId, items, indicatorPosition]);

  // Handle item press
  const handleItemPress = useCallback((item: BottomNavItem, index: number) => {
    if (item.disabled) return;

    // Haptic feedback
    if (hapticFeedback && Platform.OS === 'ios') {
      Vibration.vibrate(10);
    }

    // Animation
    itemAnimations[index].value = withTiming(1, { duration: 150 }, () => {
      itemAnimations[index].value = withTiming(0, { duration: 150 });
    });

    // Accessibility announcement
    AccessibilityInfo.announceForAccessibility(
      `Navigated to ${item.label}`
    );

    onItemPress(item);
  }, [hapticFeedback, itemAnimations, onItemPress]);

  // Calculate item width
  const itemWidth = screenWidth / items.length;

  // Indicator animation style
  const indicatorStyle = useAnimatedStyle(() => {
    const translateX = interpolate(
      indicatorPosition.value,
      [0, items.length - 1],
      [0, screenWidth - itemWidth]
    );

    return {
      transform: [{ translateX }],
    };
  });

  // Render navigation item
  const renderNavItem = (item: BottomNavItem, index: number) => {
    const isActive = item.id === activeItemId;
    
    // Item animation style
    const itemAnimationStyle = useAnimatedStyle(() => {
      const scale = interpolate(
        itemAnimations[index].value,
        [0, 1],
        [1, 0.95]
      );

      return {
        transform: [{ scale }],
      };
    });

    return (
      <AnimatedTouchableOpacity
        key={item.id}
        style={[
          styles.navItem,
          { width: itemWidth },
          itemAnimationStyle,
          item.disabled && styles.disabledItem,
        ]}
        onPress={() => handleItemPress(item, index)}
        disabled={item.disabled}
        accessibilityRole="tab"
        accessibilityLabel={item.accessibilityLabel || item.label}
        accessibilityHint={item.accessibilityHint || `Navigate to ${item.label}`}
        accessibilityState={{
          selected: isActive,
          disabled: item.disabled,
        }}
        testID={`${testID}-item-${item.id}`}
      >
        {/* Icon Container */}
        <Box style={styles.iconContainer}>
          <Ionicons
            name={isActive && item.activeIcon ? item.activeIcon : item.icon}
            size={24}
            color={
              item.disabled
                ? safeGet('colors.text.disabled', '#9CA3AF')
                : isActive
                  ? safeGet('colors.primary.default', '#4A6B52')
                  : safeGet('colors.text.secondary', '#6B7280')
            }
          />
          
          {/* Badge */}
          {item.badge && (
            <Box style={styles.badge}>
              <Text
                variant="body"
                size="xs"
                weight="medium"
                color={safeGet('colors.white', '#FFFFFF')}
                style={styles.badgeText}
              >
                {typeof item.badge === 'number' && item.badge > 99 ? '99+' : item.badge}
              </Text>
            </Box>
          )}
        </Box>

        {/* Label */}
        {showLabels && (
          <Text
            variant="body"
            size="xs"
            weight={isActive ? 'medium' : 'normal'}
            color={
              item.disabled
                ? safeGet('colors.text.disabled', '#9CA3AF')
                : isActive
                  ? safeGet('colors.primary.default', '#4A6B52')
                  : safeGet('colors.text.secondary', '#6B7280')
            }
            style={styles.label}
            numberOfLines={1}
          >
            {item.label}
          </Text>
        )}
      </AnimatedTouchableOpacity>
    );
  };

  return (
    <Box
      style={[
        styles.container,
        {
          paddingBottom: Math.max(insets.bottom, 16),
          backgroundColor: isDark
            ? safeGet('colors.background.secondary', '#1F2937')
            : safeGet('colors.background.primary', '#FFFFFF'),
        },
        style,
      ]}
      testID={testID}
    >
      {/* Active Indicator */}
      <Animated.View
        style={[
          styles.activeIndicator,
          {
            width: itemWidth,
            backgroundColor: safeGet('colors.primary.default', '#4A6B52'),
          },
          indicatorStyle,
        ]}
      />

      {/* Navigation Items */}
      <Box style={styles.itemsContainer}>
        {items.map(renderNavItem)}
      </Box>
    </Box>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  activeIndicator: {
    position: 'absolute',
    top: 0,
    height: 3,
    borderRadius: 1.5,
  },
  itemsContainer: {
    flexDirection: 'row',
    paddingTop: 8,
    paddingHorizontal: 4,
  },
  navItem: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
    minHeight: 56, // Minimum touch target height
  },
  disabledItem: {
    opacity: 0.5,
  },
  iconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    width: 32,
    height: 32,
  },
  badge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: '#EF4444',
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    fontSize: 10,
    lineHeight: 12,
  },
  label: {
    marginTop: 4,
    textAlign: 'center',
  },
});
