/**
 * Internationalization (i18n) System
 * 
 * Comprehensive internationalization system for Canadian market localization
 * with support for English Canadian (en-CA) and French Canadian (fr-CA).
 * 
 * Features:
 * - Dynamic locale switching
 * - Nested translation keys
 * - Pluralization support
 * - Date/time formatting
 * - Currency formatting
 * - Number formatting
 * - RTL support preparation
 * - Fallback handling
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Platform, NativeModules } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import locale files
import enCA from '../locales/en-CA.json';
import frCA from '../locales/fr-CA.json';

// Supported locales
export type SupportedLocale = 'en-CA' | 'fr-CA';

// Translation resources
const translations = {
  'en-CA': enCA,
  'fr-CA': frCA,
};

// Current locale state
let currentLocale: SupportedLocale = 'en-CA';
let currentTranslations = translations[currentLocale];

// Storage key for persisting locale preference
const LOCALE_STORAGE_KEY = '@vierla_locale';

/**
 * Get device locale with Canadian fallback
 */
function getDeviceLocale(): SupportedLocale {
  let deviceLocale = 'en-CA';
  
  if (Platform.OS === 'ios') {
    deviceLocale = NativeModules.SettingsManager?.settings?.AppleLocale ||
                   NativeModules.SettingsManager?.settings?.AppleLanguages?.[0] ||
                   'en-CA';
  } else if (Platform.OS === 'android') {
    deviceLocale = NativeModules.I18nManager?.localeIdentifier || 'en-CA';
  } else if (Platform.OS === 'web') {
    deviceLocale = navigator.language || 'en-CA';
  }
  
  // Map common locales to Canadian variants
  if (deviceLocale.startsWith('fr')) {
    return 'fr-CA';
  } else {
    return 'en-CA';
  }
}

/**
 * Initialize i18n system
 */
export async function initializeI18n(): Promise<SupportedLocale> {
  try {
    // Try to load saved locale preference
    const savedLocale = await AsyncStorage.getItem(LOCALE_STORAGE_KEY);
    
    if (savedLocale && (savedLocale === 'en-CA' || savedLocale === 'fr-CA')) {
      currentLocale = savedLocale as SupportedLocale;
    } else {
      // Use device locale as fallback
      currentLocale = getDeviceLocale();
    }
    
    currentTranslations = translations[currentLocale];
    return currentLocale;
  } catch (error) {
    console.warn('Failed to initialize i18n:', error);
    currentLocale = 'en-CA';
    currentTranslations = translations[currentLocale];
    return currentLocale;
  }
}

/**
 * Change current locale
 */
export async function setLocale(locale: SupportedLocale): Promise<void> {
  try {
    currentLocale = locale;
    currentTranslations = translations[locale];
    
    // Persist locale preference
    await AsyncStorage.setItem(LOCALE_STORAGE_KEY, locale);
    
    // Notify listeners about locale change
    localeChangeListeners.forEach(listener => listener(locale));
  } catch (error) {
    console.error('Failed to set locale:', error);
  }
}

/**
 * Get current locale
 */
export function getCurrentLocale(): SupportedLocale {
  return currentLocale;
}

/**
 * Check if current locale is French Canadian
 */
export function isFrenchCanadian(): boolean {
  return currentLocale === 'fr-CA';
}

/**
 * Get translation for a key with nested object support
 */
export function t(key: string, params?: Record<string, string | number>): string {
  const keys = key.split('.');
  let value: any = currentTranslations;
  
  // Navigate through nested object
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      // Fallback to English if key not found in current locale
      if (currentLocale !== 'en-CA') {
        const fallbackKeys = key.split('.');
        let fallbackValue: any = translations['en-CA'];
        
        for (const fk of fallbackKeys) {
          if (fallbackValue && typeof fallbackValue === 'object' && fk in fallbackValue) {
            fallbackValue = fallbackValue[fk];
          } else {
            return `[Missing: ${key}]`;
          }
        }
        
        value = fallbackValue;
      } else {
        return `[Missing: ${key}]`;
      }
      break;
    }
  }
  
  if (typeof value !== 'string') {
    return `[Invalid: ${key}]`;
  }
  
  // Replace parameters
  if (params) {
    return value.replace(/\{\{(\w+)\}\}/g, (match, paramKey) => {
      return params[paramKey]?.toString() || match;
    });
  }
  
  return value;
}

/**
 * Pluralization support
 */
export function tp(key: string, count: number, params?: Record<string, string | number>): string {
  const pluralKey = count === 1 ? `${key}.singular` : `${key}.plural`;
  const fallbackKey = key;
  
  // Try plural key first, then fallback to base key
  let translation = t(pluralKey);
  if (translation.startsWith('[Missing:') || translation.startsWith('[Invalid:')) {
    translation = t(fallbackKey);
  }
  
  // Add count to parameters
  const allParams = { ...params, count };
  
  return t(translation, allParams);
}

/**
 * Format currency for Canadian market
 */
export function formatCurrency(amount: number, options?: {
  showCents?: boolean;
  locale?: SupportedLocale;
}): string {
  const locale = options?.locale || currentLocale;
  const showCents = options?.showCents !== false;
  
  const formatter = new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: 'CAD',
    minimumFractionDigits: showCents ? 2 : 0,
    maximumFractionDigits: showCents ? 2 : 0,
  });
  
  return formatter.format(amount);
}

/**
 * Format date for Canadian market
 */
export function formatDate(date: Date, options?: {
  style?: 'short' | 'medium' | 'long' | 'full';
  locale?: SupportedLocale;
}): string {
  const locale = options?.locale || currentLocale;
  const style = options?.style || 'medium';
  
  const formatOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: style === 'short' ? 'numeric' : style === 'medium' ? 'short' : 'long',
    day: 'numeric',
  };
  
  if (style === 'full') {
    formatOptions.weekday = 'long';
  }
  
  return new Intl.DateTimeFormat(locale, formatOptions).format(date);
}

/**
 * Format time for Canadian market
 */
export function formatTime(date: Date, options?: {
  format?: '12h' | '24h';
  locale?: SupportedLocale;
}): string {
  const locale = options?.locale || currentLocale;
  const format = options?.format || '12h';
  
  const formatOptions: Intl.DateTimeFormatOptions = {
    hour: 'numeric',
    minute: '2-digit',
    hour12: format === '12h',
  };
  
  return new Intl.DateTimeFormat(locale, formatOptions).format(date);
}

/**
 * Format phone number for Canadian format
 */
export function formatPhoneNumber(phoneNumber: string): string {
  // Remove all non-digits
  const digits = phoneNumber.replace(/\D/g, '');
  
  // Canadian phone number format: (XXX) XXX-XXXX
  if (digits.length === 10) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
  } else if (digits.length === 11 && digits.startsWith('1')) {
    return `+1 (${digits.slice(1, 4)}) ${digits.slice(4, 7)}-${digits.slice(7)}`;
  }
  
  return phoneNumber; // Return original if not a valid Canadian number
}

/**
 * Format postal code for Canadian format
 */
export function formatPostalCode(postalCode: string): string {
  // Remove spaces and convert to uppercase
  const cleaned = postalCode.replace(/\s/g, '').toUpperCase();
  
  // Canadian postal code format: A1A 1A1
  if (cleaned.length === 6 && /^[A-Z]\d[A-Z]\d[A-Z]\d$/.test(cleaned)) {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
  }
  
  return postalCode; // Return original if not a valid Canadian postal code
}

/**
 * Get localized province names
 */
export function getProvinces(): Array<{ code: string; name: string }> {
  const provinces = [
    { code: 'AB', nameKey: 'provinces.alberta' },
    { code: 'BC', nameKey: 'provinces.britishColumbia' },
    { code: 'MB', nameKey: 'provinces.manitoba' },
    { code: 'NB', nameKey: 'provinces.newBrunswick' },
    { code: 'NL', nameKey: 'provinces.newfoundlandLabrador' },
    { code: 'NS', nameKey: 'provinces.novaScotia' },
    { code: 'ON', nameKey: 'provinces.ontario' },
    { code: 'PE', nameKey: 'provinces.princeEdwardIsland' },
    { code: 'QC', nameKey: 'provinces.quebec' },
    { code: 'SK', nameKey: 'provinces.saskatchewan' },
    { code: 'NT', nameKey: 'provinces.northwestTerritories' },
    { code: 'NU', nameKey: 'provinces.nunavut' },
    { code: 'YT', nameKey: 'provinces.yukon' },
  ];
  
  return provinces.map(province => ({
    code: province.code,
    name: t(province.nameKey) || province.code,
  }));
}

/**
 * Locale change listeners
 */
type LocaleChangeListener = (locale: SupportedLocale) => void;
const localeChangeListeners: LocaleChangeListener[] = [];

/**
 * Subscribe to locale changes
 */
export function onLocaleChange(listener: LocaleChangeListener): () => void {
  localeChangeListeners.push(listener);
  
  // Return unsubscribe function
  return () => {
    const index = localeChangeListeners.indexOf(listener);
    if (index > -1) {
      localeChangeListeners.splice(index, 1);
    }
  };
}

/**
 * Get available locales
 */
export function getAvailableLocales(): Array<{ code: SupportedLocale; name: string; nativeName: string }> {
  return [
    {
      code: 'en-CA',
      name: 'English (Canada)',
      nativeName: 'English (Canada)',
    },
    {
      code: 'fr-CA',
      name: 'French (Canada)',
      nativeName: 'Français (Canada)',
    },
  ];
}

/**
 * Validate translation completeness
 */
export function validateTranslations(): {
  missing: string[];
  extra: string[];
  complete: boolean;
} {
  const enKeys = getAllKeys(translations['en-CA']);
  const frKeys = getAllKeys(translations['fr-CA']);
  
  const missing = enKeys.filter(key => !frKeys.includes(key));
  const extra = frKeys.filter(key => !enKeys.includes(key));
  
  return {
    missing,
    extra,
    complete: missing.length === 0 && extra.length === 0,
  };
}

/**
 * Helper function to get all nested keys from an object
 */
function getAllKeys(obj: any, prefix = ''): string[] {
  let keys: string[] = [];
  
  for (const key in obj) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      keys = keys.concat(getAllKeys(obj[key], fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys;
}

// Export commonly used functions with shorter names
export { t as translate };
export { tp as translatePlural };
export { formatCurrency as currency };
export { formatDate as date };
export { formatTime as time };

// Default export for easy importing
export default {
  t,
  tp,
  setLocale,
  getCurrentLocale,
  isFrenchCanadian,
  formatCurrency,
  formatDate,
  formatTime,
  formatPhoneNumber,
  formatPostalCode,
  getProvinces,
  onLocaleChange,
  getAvailableLocales,
  initializeI18n,
};
