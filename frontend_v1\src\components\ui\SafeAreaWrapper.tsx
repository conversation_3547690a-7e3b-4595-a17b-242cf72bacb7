/**
 * SafeAreaWrapper - Enhanced Safe Area Management
 *
 * Component Contract:
 * - Provides consistent safe area handling across iOS and Android
 * - Respects device notches, status bars, and navigation bars
 * - Offers flexible configuration for different screen types
 * - Integrates with responsive utilities for optimal spacing
 * - Supports custom edge configurations
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import { View, ViewStyle, StyleSheet, StatusBar, Platform } from 'react-native';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';

import { safePlatformSelect } from '../../utils/platformUtils';
import {
  getSafeAreaInsets,
  getSafeAreaTop,
  getSafeAreaBottom,
  isIPhoneWithNotch,
  isAndroidWithGestures,
  hasNotch,
  hasDynamicIsland,
} from '../../utils/responsiveUtils';

interface SafeAreaWrapperProps {
  children: React.ReactNode;
  style?: ViewStyle;
  edges?: ('top' | 'bottom' | 'left' | 'right')[];
  mode?: 'padding' | 'margin';
  backgroundColor?: string;
  forceInsets?: {
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  };
  testID?: string;
  // Enhanced device compatibility props
  respectNotch?: boolean;
  respectGestures?: boolean;
  statusBarStyle?: 'light-content' | 'dark-content';
  statusBarBackgroundColor?: string;
  customTopPadding?: number;
  customBottomPadding?: number;
}

export const SafeAreaWrapper: React.FC<SafeAreaWrapperProps> = ({
  children,
  style,
  edges = ['top', 'bottom'],
  mode = 'padding',
  backgroundColor = 'transparent',
  forceInsets,
  testID,
  respectNotch = true,
  respectGestures = true,
  statusBarStyle = 'dark-content',
  statusBarBackgroundColor,
  customTopPadding,
  customBottomPadding,
}) => {
  // Get safe area insets with enhanced device detection
  const contextInsets = useSafeAreaInsets();
  const fallbackInsets = getSafeAreaInsets();
  const insets = contextInsets.top > 0 ? contextInsets : fallbackInsets;

  // Enhanced device-specific calculations
  const getEnhancedTopInset = () => {
    if (customTopPadding !== undefined) return customTopPadding;

    if (respectNotch && isIPhoneWithNotch()) {
      // Ensure minimum padding for iPhone notch/Dynamic Island
      if (hasDynamicIsland()) return Math.max(insets.top, 59);
      if (hasNotch()) return Math.max(insets.top, 44);
    }

    return insets.top;
  };

  const getEnhancedBottomInset = () => {
    if (customBottomPadding !== undefined) return customBottomPadding;

    if (respectGestures) {
      if (isIPhoneWithNotch()) return Math.max(insets.bottom, 34);
      if (isAndroidWithGestures()) return Math.max(insets.bottom, 16);
    }

    return insets.bottom;
  };

  // Apply forced insets if provided, otherwise use enhanced calculations
  const finalInsets = {
    top: forceInsets?.top ?? getEnhancedTopInset(),
    bottom: forceInsets?.bottom ?? getEnhancedBottomInset(),
    left: forceInsets?.left ?? insets.left,
    right: forceInsets?.right ?? insets.right,
  };

  // Create dynamic styles based on edges and mode
  const dynamicStyle: ViewStyle = {};

  edges.forEach(edge => {
    const insetValue = finalInsets[edge];
    if (insetValue > 0) {
      if (mode === 'padding') {
        dynamicStyle[
          `padding${edge.charAt(0).toUpperCase() + edge.slice(1)}` as keyof ViewStyle
        ] = insetValue;
      } else {
        dynamicStyle[
          `margin${edge.charAt(0).toUpperCase() + edge.slice(1)}` as keyof ViewStyle
        ] = insetValue;
      }
    }
  });

  return (
    <View
      style={[styles.container, { backgroundColor }, dynamicStyle, style]}
      testID={testID}>
      {Platform.OS !== 'web' && (
        <StatusBar
          barStyle={statusBarStyle}
          backgroundColor={statusBarBackgroundColor || backgroundColor}
          translucent={Platform.OS === 'android'}
        />
      )}
      {children}
    </View>
  );
};

// Alternative component using react-native-safe-area-context
export const EnhancedSafeAreaView: React.FC<SafeAreaWrapperProps> = ({
  children,
  style,
  edges = ['top', 'bottom'],
  backgroundColor = 'transparent',
  testID,
}) => {
  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor }, style]}
      edges={edges}
      testID={testID}>
      {children}
    </SafeAreaView>
  );
};

// Specialized components for common use cases
export const SafeAreaScreen: React.FC<{
  children: React.ReactNode;
  style?: ViewStyle;
  backgroundColor?: string;
  includeStatusBar?: boolean;
  includeTabBar?: boolean;
  statusBarStyle?: 'light-content' | 'dark-content';
  respectNotch?: boolean;
  respectGestures?: boolean;
  testID?: string;
}> = ({
  children,
  style,
  backgroundColor = '#FFFFFF',
  includeStatusBar = true,
  includeTabBar = true,
  statusBarStyle = 'dark-content',
  respectNotch = true,
  respectGestures = true,
  testID,
}) => {
  const edges: ('top' | 'bottom' | 'left' | 'right')[] = [];

  if (includeStatusBar) edges.push('top');
  if (includeTabBar) edges.push('bottom');

  return (
    <SafeAreaWrapper
      edges={edges}
      backgroundColor={backgroundColor}
      statusBarStyle={statusBarStyle}
      respectNotch={respectNotch}
      respectGestures={respectGestures}
      style={[styles.screen, style]}
      testID={testID}>
      {children}
    </SafeAreaWrapper>
  );
};

// Modal safe area component
export const SafeAreaModal: React.FC<{
  children: React.ReactNode;
  style?: ViewStyle;
  backgroundColor?: string;
  testID?: string;
}> = ({ children, style, backgroundColor = '#FFFFFF', testID }) => {
  return (
    <SafeAreaWrapper
      edges={['top', 'bottom']}
      backgroundColor={backgroundColor}
      style={[styles.modal, style]}
      testID={testID}>
      {children}
    </SafeAreaWrapper>
  );
};

// Header safe area component
export const SafeAreaHeader: React.FC<{
  children: React.ReactNode;
  style?: ViewStyle;
  backgroundColor?: string;
  testID?: string;
}> = ({ children, style, backgroundColor = '#FFFFFF', testID }) => {
  return (
    <SafeAreaWrapper
      edges={['top']}
      backgroundColor={backgroundColor}
      style={[styles.header, style]}
      testID={testID}>
      {children}
    </SafeAreaWrapper>
  );
};

// Utility hooks for safe area values
export const useCustomSafeAreaInsets = () => {
  return getSafeAreaInsets();
};

export const useSafeAreaTop = () => {
  return getSafeAreaTop();
};

export const useSafeAreaBottom = () => {
  return getSafeAreaBottom();
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  screen: {
    flex: 1,
  },
  modal: {
    flex: 1,
    ...safePlatformSelect({
      ios: {
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
      },
      android: {
        borderTopLeftRadius: 8,
        borderTopRightRadius: 8,
      },
      default: {},
    }),
  },
  header: {
    ...safePlatformSelect({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
      default: {},
    }),
  },
});
