import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
  Dimensions,
  ScrollView,
} from 'react-native';

import { ModernStaticGradientBackgroundDark } from '../../components/ui/ModernStaticGradientBackground';
import { SafeAreaScreen } from '../../components/ui/SafeAreaWrapper';
import { Colors } from '../../constants/Colors';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';

interface OnboardingSlide {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
}

interface ProviderOnboardingCarouselProps {
  onComplete: () => void;
  onBack: () => void;
}

const { width, height } = Dimensions.get('window');

const providerOnboardingSlides: OnboardingSlide[] = [
  {
    id: 'reach',
    title: 'Reach New Clients',
    subtitle: 'Expand Your Customer Base',
    description:
      'Connect with customers actively searching for your services. Get discovered by clients who value quality and professionalism.',
    icon: 'people-outline',
  },
  {
    id: 'manage',
    title: 'Manage Your Business',
    subtitle: 'Streamlined Operations',
    description:
      'Effortlessly manage appointments, track your schedule, and organize your services. Focus on what you do best while we handle the rest.',
    icon: 'calendar-outline',
  },
  {
    id: 'earn',
    title: 'Get Paid Securely',
    subtitle: 'Reliable Payment System',
    description:
      'Receive payments instantly and securely. Track your earnings, manage your finances, and grow your business with confidence.',
    icon: 'card-outline',
  },
];

export const ProviderOnboardingCarousel: React.FC<
  ProviderOnboardingCarouselProps
> = ({ onComplete, onBack }) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);

  // Animation refs
  const containerOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Initial animation
    Animated.timing(containerOpacity, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, []);

  const handleNextSlide = () => {
    if (currentSlide < providerOnboardingSlides.length - 1) {
      const nextSlide = currentSlide + 1;
      setCurrentSlide(nextSlide);
      scrollViewRef.current?.scrollTo({
        x: nextSlide * width,
        animated: true,
      });
    } else {
      onComplete();
    }
  };

  const handlePreviousSlide = () => {
    if (currentSlide > 0) {
      const prevSlide = currentSlide - 1;
      setCurrentSlide(prevSlide);
      scrollViewRef.current?.scrollTo({
        x: prevSlide * width,
        animated: true,
      });
    }
  };

  const handleScroll = (event: any) => {
    const slideIndex = Math.round(event.nativeEvent.contentOffset.x / width);
    if (slideIndex !== currentSlide) {
      setCurrentSlide(slideIndex);
    }
  };

  return (
    <SafeAreaScreen
      backgroundColor={Colors.sage900}
      statusBarStyle="light-content"
      respectNotch={true}
      respectGestures={true}
      testID="provider-onboarding-carousel">
      <ModernStaticGradientBackgroundDark variant="onboarding">
        <Animated.View
          style={[styles.container, { opacity: containerOpacity }]}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={onBack}
              accessibilityRole="button"
              accessibilityLabel="Go back"
              testID="back-button">
              <Ionicons name="chevron-back" size={24} color={Colors.white} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.skipButton}
              onPress={onComplete}
              accessibilityRole="button"
              accessibilityLabel="Skip onboarding"
              testID="skip-button">
              <Text style={styles.skipButtonText}>Skip</Text>
            </TouchableOpacity>
          </View>

          {/* Slide Content */}
          <ScrollView
            ref={scrollViewRef}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onScroll={handleScroll}
            scrollEventThrottle={16}
            style={styles.scrollView}>
            {providerOnboardingSlides.map((slide, index) => (
              <View key={slide.id} style={styles.slideContainer}>
                <View style={styles.slideContent}>
                  {/* Icon */}
                  <View style={styles.iconContainer}>
                    <Ionicons
                      name={slide.icon}
                      size={80}
                      color={Colors.sage400}
                    />
                  </View>

                  {/* Text Content */}
                  <View style={styles.textContainer}>
                    <Text style={styles.slideTitle}>{slide.title}</Text>
                    <Text style={styles.slideSubtitle}>{slide.subtitle}</Text>
                    <Text style={styles.slideDescription}>
                      {slide.description}
                    </Text>
                  </View>
                </View>
              </View>
            ))}
          </ScrollView>

          {/* Page Indicators */}
          <View style={styles.indicatorContainer}>
            {providerOnboardingSlides.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.indicator,
                  index === currentSlide && styles.activeIndicator,
                ]}
              />
            ))}
          </View>

          {/* Navigation Buttons */}
          <View style={styles.navigationContainer}>
            {currentSlide > 0 && (
              <TouchableOpacity
                onPress={handlePreviousSlide}
                style={styles.navButton}
                accessibilityLabel="Previous slide"
                testID="previous-button">
                <Ionicons
                  name="chevron-back"
                  size={24}
                  color={Colors.sage400}
                />
              </TouchableOpacity>
            )}

            <View style={styles.navButtonSpacer} />

            <TouchableOpacity
              style={styles.nextButton}
              onPress={handleNextSlide}
              accessibilityRole="button"
              accessibilityLabel={
                currentSlide === providerOnboardingSlides.length - 1
                  ? 'Get started with Vierla'
                  : 'Next slide'
              }
              testID="next-button">
              <Text style={styles.nextButtonText}>
                {currentSlide === providerOnboardingSlides.length - 1
                  ? 'Get Started'
                  : 'Next'}
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </ModernStaticGradientBackgroundDark>
    </SafeAreaScreen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(24),
    paddingTop: getResponsiveSpacing(20),
    paddingBottom: getResponsiveSpacing(20),
  },
  backButton: {
    padding: getResponsiveSpacing(8),
  },
  skipButton: {
    padding: getResponsiveSpacing(8),
  },
  skipButtonText: {
    fontSize: getResponsiveFontSize(16),
    color: Colors.sage200,
    fontWeight: '500',
  },
  scrollView: {
    flex: 1,
  },
  slideContainer: {
    width: width,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(height < 700 ? 24 : 32),
    paddingVertical: getResponsiveSpacing(height < 700 ? 16 : 20),
  },
  slideContent: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  iconContainer: {
    marginBottom: getResponsiveSpacing(height < 700 ? 24 : 40),
    padding: getResponsiveSpacing(height < 700 ? 16 : 20),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: getResponsiveSpacing(20),
  },
  textContainer: {
    alignItems: 'center',
  },
  slideTitle: {
    fontSize: getResponsiveFontSize(32),
    fontWeight: '700',
    color: Colors.white,
    textAlign: 'center',
    marginBottom: getResponsiveSpacing(12),
    lineHeight: getResponsiveFontSize(38),
  },
  slideSubtitle: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '600',
    color: Colors.sage200,
    textAlign: 'center',
    marginBottom: getResponsiveSpacing(16),
    lineHeight: getResponsiveFontSize(26),
  },
  slideDescription: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '400',
    color: Colors.sage100,
    textAlign: 'center',
    lineHeight: getResponsiveFontSize(24),
    opacity: 0.9,
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(20),
    gap: getResponsiveSpacing(8),
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  activeIndicator: {
    backgroundColor: Colors.sage400,
    width: 24,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(24),
    paddingBottom: getResponsiveSpacing(32),
  },
  navButton: {
    padding: getResponsiveSpacing(12),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: getResponsiveSpacing(8),
  },
  navButtonSpacer: {
    flex: 1,
  },
  nextButton: {
    backgroundColor: Colors.sage400,
    paddingVertical: getResponsiveSpacing(16),
    paddingHorizontal: getResponsiveSpacing(32),
    borderRadius: getResponsiveSpacing(12),
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  nextButtonText: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: Colors.white,
  },
});
