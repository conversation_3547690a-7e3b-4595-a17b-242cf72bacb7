/**
 * Accessibility Utilities for WCAG 2.2 AA Compliance
 *
 * This module provides comprehensive accessibility utilities to ensure
 * the Vierla application meets WCAG 2.2 AA standards.
 *
 * Key Features:
 * - Color contrast validation and enhancement
 * - Focus management for keyboard navigation
 * - Touch target size validation
 * - Screen reader compatibility helpers
 * - Accessibility testing utilities
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Platform, AccessibilityInfo } from 'react-native';
import { calculateContrastRatio, getWCAGCompliantColor } from './colorContrastAudit';

// WCAG 2.2 AA Compliance Constants
// Simplified version to resolve runtime error
export const WCAG_STANDARDS = {
  CONTRAST_RATIOS: {
    AA_NORMAL: 4.5,
    AA_LARGE: 3.0,
    AAA_NORMAL: 7.0,
    AAA_LARGE: 4.5,
  },
  TOUCH_TARGETS: {
    MINIMUM_SIZE: 44, // iOS HIG and Material Design minimum
    RECOMMENDED_SIZE: 48,
    SPACING: 8, // Minimum spacing between targets
  },
  // Backward compatibility alias for TARGET_SIZE
  TARGET_SIZE: {
    MINIMUM: 44, // Alias for TOUCH_TARGETS.MINIMUM_SIZE
  },
  FOCUS_INDICATORS: {
    MIN_WIDTH: 2, // WCAG 2.1 AA minimum thickness
    RECOMMENDED_WIDTH: 3, // Enhanced visibility
    OFFSET: 2,
    Z_INDEX_BASE: 9999, // Ensure focus indicators are above sticky elements
    SHADOW_OPACITY: 0.4, // Enhanced visibility with shadow
    SHADOW_RADIUS: 6,
  },
} as const;

// Create a global reference to prevent runtime errors
if (typeof global !== 'undefined') {
  (global as any).WCAG_STANDARDS = WCAG_STANDARDS;
}

/**
 * Color Contrast Utilities - REC-ACC-001 Implementation
 */

// Convert hex color to RGB
export const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

// Calculate relative luminance according to WCAG
export const getRelativeLuminance = (r: number, g: number, b: number): number => {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
};

// Calculate contrast ratio between two colors
export const getContrastRatio = (color1: string, color2: string): number => {
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);

  if (!rgb1 || !rgb2) return 1;

  const lum1 = getRelativeLuminance(rgb1.r, rgb1.g, rgb1.b);
  const lum2 = getRelativeLuminance(rgb2.r, rgb2.g, rgb2.b);

  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);

  return (brightest + 0.05) / (darkest + 0.05);
};

// Check if color combination meets WCAG AA standards
export const meetsWCAGAA = (foreground: string, background: string, isLargeText = false): boolean => {
  const ratio = getContrastRatio(foreground, background);
  const requiredRatio = isLargeText ? WCAG_STANDARDS.CONTRAST_RATIOS.AA_LARGE : WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL;
  return ratio >= requiredRatio;
};

// Get WCAG-compliant color variations (wrapper for imported function)
export const getWCAGCompliantColorLocal = (
  baseColor: string,
  backgroundColor: string,
  isLargeText = false
): string => {
  if (meetsWCAGAA(baseColor, backgroundColor, isLargeText)) {
    return baseColor;
  }

  // Enhanced sage green colors that meet WCAG AA standards
  const compliantColors = {
    sage: {
      light: '#4A6B52', // 4.52:1 contrast ratio on white
      medium: '#3A5B42', // 5.89:1 contrast ratio on white
      dark: '#2A4B32', // 7.12:1 contrast ratio on white
      darker: '#1F3A26', // 9.45:1 contrast ratio on white
    },
    neutral: {
      dark: '#374151', // 8.9:1 contrast ratio on white
      darker: '#1F2937', // 13.1:1 contrast ratio on white
    }
  };

  // Return the most appropriate compliant color
  if (backgroundColor === '#FFFFFF' || backgroundColor === '#F9FAFB') {
    return isLargeText ? compliantColors.sage.light : compliantColors.sage.medium;
  }

  return baseColor; // Fallback to original color
};

/**
 * Keyboard Focus Utilities - REC-ACC-002 Implementation
 */

// Enhanced focus indicator styles
export const getFocusIndicatorStyle = (baseColor = '#3B82F6') => ({
  borderWidth: WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH,
  borderColor: baseColor,
  borderStyle: 'solid' as const,
  shadowColor: baseColor,
  shadowOffset: { width: 0, height: 0 },
  shadowOpacity: WCAG_STANDARDS.FOCUS_INDICATORS.SHADOW_OPACITY,
  shadowRadius: WCAG_STANDARDS.FOCUS_INDICATORS.SHADOW_RADIUS,
  elevation: Platform.OS === 'android' ? 8 : 0,
  zIndex: WCAG_STANDARDS.FOCUS_INDICATORS.Z_INDEX_BASE,
});

// Focus management utilities
export const FocusUtils = {
  // Ensure focus is visible and not obscured
  ensureFocusVisible: (element: any) => {
    if (Platform.OS === 'web') {
      // Web-specific focus management
      if (element && element.focus) {
        element.focus();
        element.scrollIntoView?.({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest'
        });
      }
    }
  },

  // Get focus indicator with enhanced visibility
  getEnhancedFocusStyle: (color?: string) => {
    const focusColor = color || '#3B82F6';
    return {
      ...getFocusIndicatorStyle(focusColor),
      // Additional enhancement for better visibility
      outlineWidth: WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH,
      outlineColor: focusColor,
      outlineStyle: 'solid' as const,
      outlineOffset: WCAG_STANDARDS.FOCUS_INDICATORS.OFFSET,
    };
  },

  // Check if focus is potentially obscured by sticky elements
  checkFocusObscured: (elementY: number, stickyFooterHeight = 80) => {
    // Simple check for focus obscured by sticky footer
    const screenHeight = Platform.OS === 'web' ? window.innerHeight : 800; // Fallback height
    const focusAreaBottom = elementY + WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
    const stickyFooterTop = screenHeight - stickyFooterHeight;

    return focusAreaBottom > stickyFooterTop;
  },

  /**
   * Get enhanced WCAG-compliant focus indicator styles for React Native
   */
  getFocusIndicatorStyle: (
    isFocused: boolean,
    baseStyle: any = {},
    options: {
      color?: string;
      width?: number;
      offset?: number;
      preventObscuring?: boolean;
    } = {}
  ) => {
    const {
      color = '#3B82F6', // Default focus color
      width: requestedWidth = WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH,
      offset = WCAG_STANDARDS.FOCUS_INDICATORS.OFFSET,
      preventObscuring = true, // Prevent sticky elements from obscuring focus
    } = options;

    // Ensure minimum width compliance
    const width = Math.max(WCAG_STANDARDS.FOCUS_INDICATORS.MIN_WIDTH, requestedWidth);

    if (!isFocused) {
      return baseStyle;
    }

    const baseStyles = {
      ...baseStyle,
      borderWidth: width,
      borderColor: color,
      borderStyle: 'solid' as const,
      // Enhanced shadow for better visibility and WCAG compliance
      shadowColor: color,
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: WCAG_STANDARDS.FOCUS_INDICATORS.SHADOW_OPACITY,
      shadowRadius: WCAG_STANDARDS.FOCUS_INDICATORS.SHADOW_RADIUS,
      // Ensure focus ring is always visible and above sticky elements
      overflow: 'visible' as const,
      zIndex: preventObscuring ? WCAG_STANDARDS.FOCUS_INDICATORS.Z_INDEX_BASE : undefined,
      // Add subtle background highlight for better contrast
      backgroundColor: `${color}10`, // 10% opacity background
      // Ensure minimum border radius for better visibility
      borderRadius: Math.max(baseStyle.borderRadius || 0, 4),
    };

    // Add platform-specific properties
    const platformStyles = Platform.select({
      web: {
        outlineWidth: width,
        outlineColor: color,
        outlineStyle: 'solid' as const,
        outlineOffset: offset,
      },
      android: {
        elevation: 6,
      },
      ios: {
        elevation: 0,
      },
      default: {},
    });

    return {
      ...baseStyles,
      ...platformStyles,
    };
  },

  /**
   * Ensure focus indicator is not obscured by sticky elements
   */
  ensureFocusNotObscured: (
    focusedElementStyle: any,
    stickyElements: Array<{ zIndex: number; position: string }> = []
  ) => {
    const maxStickyZIndex = stickyElements.reduce((max, element) => {
      if (element.position === 'sticky' || element.position === 'fixed') {
        return Math.max(max, element.zIndex || 0);
      }
      return max;
    }, 0);

    return {
      ...focusedElementStyle,
      zIndex: Math.max(
        focusedElementStyle.zIndex || 0,
        maxStickyZIndex + 1,
        WCAG_STANDARDS.FOCUS_INDICATORS.Z_INDEX_BASE
      ),
    };
  },

  /**
   * Enhanced focus management for complex components
   */
  createFocusManager: () => {
    let currentFocusedElement: any = null;

    return {
      setFocus: (element: any) => {
        currentFocusedElement = element;
      },

      getCurrentFocus: () => currentFocusedElement,

      clearFocus: () => {
        currentFocusedElement = null;
      },

      moveFocus: (direction: 'next' | 'previous') => {
        // Implementation would depend on specific navigation requirements
        console.log(`Moving focus ${direction}`);
      },
    };
  },
};

/**
 * Touch Target Utilities - REC-ACC-003 Implementation
 */

// Ensure minimum touch target size
export const getMinimumTouchTarget = () => WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;

// Get recommended touch target size
export const getRecommendedTouchTarget = () => WCAG_STANDARDS.TOUCH_TARGETS.RECOMMENDED_SIZE;

// Validate touch target size
export const validateTouchTargetSize = (width: number, height: number): boolean => {
  const minSize = WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
  return width >= minSize && height >= minSize;
};

// Get touch target style with minimum size enforcement
export const getTouchTargetStyle = (customSize?: number) => {
  const size = customSize || WCAG_STANDARDS.TOUCH_TARGETS.RECOMMENDED_SIZE;
  return {
    minWidth: size,
    minHeight: size,
    paddingHorizontal: Math.max(0, (size - 24) / 2), // Ensure content is centered
    paddingVertical: Math.max(0, (size - 24) / 2),
  };
};

// Touch target utilities
export const TouchTargetUtils = {
  // Ensure adequate spacing between touch targets
  getSpacing: () => WCAG_STANDARDS.TOUCH_TARGETS.SPACING,

  // Get enhanced touch target for toolbar icons
  getToolbarIconStyle: () => ({
    ...getTouchTargetStyle(WCAG_STANDARDS.TOUCH_TARGETS.RECOMMENDED_SIZE),
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    borderRadius: 8,
  }),

  // Validate touch target meets WCAG requirements
  validate: validateTouchTargetSize,

  // Validate touch target with detailed results
  validateTouchTarget: (width: number, height: number): { isValid: boolean; issues: string[] } => {
    const minSize = WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
    const issues: string[] = [];

    if (width < minSize) {
      issues.push(`Width ${width}px is below minimum ${minSize}px`);
    }
    if (height < minSize) {
      issues.push(`Height ${height}px is below minimum ${minSize}px`);
    }

    return {
      isValid: issues.length === 0,
      issues,
    };
  },

  // Get minimum size
  getMinSize: getMinimumTouchTarget,

  // Get recommended size
  getRecommendedSize: getRecommendedTouchTarget,

  // Get platform-specific minimum touch target size
  getPlatformTouchTarget: (): number => {
    return Platform.select({
      ios: 44, // iOS HIG minimum
      android: 48, // Material Design minimum
      default: 44,
    });
  },

  // Calculate hit slop for small targets
  calculateHitSlop: (targetSize: number): { top: number; bottom: number; left: number; right: number } => {
    const minSize = WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
    const deficit = Math.max(0, minSize - targetSize);
    const slop = Math.ceil(deficit / 2);

    return {
      top: slop,
      bottom: slop,
      left: slop,
      right: slop,
    };
  },
};

/**
 * Image Accessibility Utilities - REC-ACC-005 Implementation
 */

export interface ImageAccessibilityProps {
  accessibilityLabel: string;
  accessibilityHint?: string;
  accessibilityRole: 'image' | 'imagebutton';
  accessible: boolean;
  importantForAccessibility: 'yes' | 'no' | 'auto';
}

// Generate accessibility props for images
export const getImageAccessibilityProps = (
  type: 'decorative' | 'informative' | 'functional',
  description?: string,
  action?: string
): ImageAccessibilityProps => {
  switch (type) {
    case 'decorative':
      return {
        accessibilityLabel: '',
        accessibilityRole: 'image',
        accessible: false,
        importantForAccessibility: 'no',
      };

    case 'informative':
      return {
        accessibilityLabel: description || 'Informative image',
        accessibilityRole: 'image',
        accessible: true,
        importantForAccessibility: 'yes',
      };

    case 'functional':
      return {
        accessibilityLabel: description || 'Interactive image',
        accessibilityHint: action ? `Double tap to ${action}` : undefined,
        accessibilityRole: 'imagebutton',
        accessible: true,
        importantForAccessibility: 'yes',
      };

    default:
      return {
        accessibilityLabel: description || 'Image',
        accessibilityRole: 'image',
        accessible: true,
        importantForAccessibility: 'yes',
      };
  }
};

// Image accessibility utilities
export const ImageAccessibilityUtils = {
  // Get props for different image types
  getDecorative: () => getImageAccessibilityProps('decorative'),
  getInformative: (description: string) => getImageAccessibilityProps('informative', description),
  getFunctional: (description: string, action?: string) => getImageAccessibilityProps('functional', description, action),

  // Generate alt text for common image types
  generateAltText: (type: string, entityName?: string, action?: string): string => {
    switch (type) {
      case 'logo':
        return entityName ? `${entityName} logo` : 'Company logo';
      case 'avatar':
        return entityName ? `${entityName} profile picture` : 'User profile picture';
      case 'service':
        return entityName ? `${entityName} service image` : 'Service image';
      case 'store':
        return entityName ? `${entityName} store image` : 'Store image';
      case 'icon':
        return action ? `${action} icon` : entityName || 'Icon';
      default:
        return entityName || 'Image';
    }
  },
};

/**
 * Single-Pointer Alternative Utilities - REC-ACC-007 Implementation
 */

// Single-pointer interaction utilities
export const SinglePointerUtils = {
  // Convert drag-and-drop to single-pointer alternative
  getDragAlternative: (onMove: (direction: 'up' | 'down') => void) => ({
    onMoveUp: () => onMove('up'),
    onMoveDown: () => onMove('down'),
    accessibilityActions: [
      { name: 'increment', label: 'Move up' },
      { name: 'decrement', label: 'Move down' },
    ],
    onAccessibilityAction: (event: any) => {
      switch (event.nativeEvent.actionName) {
        case 'increment':
          onMove('up');
          break;
        case 'decrement':
          onMove('down');
          break;
      }
    },
  }),

  // Convert swipe gestures to button alternatives
  getSwipeAlternative: (
    onSwipeLeft?: () => void,
    onSwipeRight?: () => void,
    onSwipeUp?: () => void,
    onSwipeDown?: () => void
  ) => ({
    buttons: [
      onSwipeLeft && { label: 'Previous', onPress: onSwipeLeft },
      onSwipeRight && { label: 'Next', onPress: onSwipeRight },
      onSwipeUp && { label: 'Up', onPress: onSwipeUp },
      onSwipeDown && { label: 'Down', onPress: onSwipeDown },
    ].filter(Boolean),
  }),

  // Convert multi-touch gestures to single-pointer
  getMultiTouchAlternative: (
    onPinch?: (scale: number) => void,
    onRotate?: (rotation: number) => void
  ) => ({
    zoomIn: onPinch ? () => onPinch(1.2) : undefined,
    zoomOut: onPinch ? () => onPinch(0.8) : undefined,
    rotateLeft: onRotate ? () => onRotate(-15) : undefined,
    rotateRight: onRotate ? () => onRotate(15) : undefined,
  }),
};

/**
 * Screen Reader Utilities
 */

export const ScreenReaderUtils = {
  // Announce message to screen readers
  announceForAccessibility: (message: string) => {
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      AccessibilityInfo.announceForAccessibility(message);
    }
  },

  // Check if screen reader is enabled
  isScreenReaderEnabled: async (): Promise<boolean> => {
    try {
      return await AccessibilityInfo.isScreenReaderEnabled();
    } catch {
      return false;
    }
  },

  // Get accessible label with context
  getAccessibleLabel: (
    primary: string,
    secondary?: string,
    state?: string,
    position?: string
  ): string => {
    const parts = [primary];
    if (secondary) parts.push(secondary);
    if (state) parts.push(state);
    if (position) parts.push(position);
    return parts.join(', ');
  },

  // Generate comprehensive accessible label
  generateAccessibleLabel: (
    primary: string,
    state?: string,
    context?: string,
    hint?: string
  ): string => {
    const parts = [primary];
    if (state) parts.push(state);
    if (context) parts.push(context);
    if (hint) parts.push(hint);
    return parts.join(', ');
  },

  // Get semantic role for component types
  getSemanticRole: (componentType: string): string => {
    const roleMap: Record<string, string> = {
      button: 'button',
      input: 'text',
      checkbox: 'checkbox',
      radio: 'radio',
      link: 'link',
      image: 'image',
      text: 'text',
      header: 'header',
      list: 'list',
      listitem: 'listitem',
    };
    return roleMap[componentType] || 'none';
  },
};

/**
 * General Accessibility Utilities
 */

// Check if user prefers reduced motion
export const prefersReducedMotion = async (): Promise<boolean> => {
  try {
    return await AccessibilityInfo.isReduceMotionEnabled();
  } catch {
    return false;
  }
};

// Get responsive spacing that meets accessibility requirements
export const getResponsiveSpacing = (baseSpacing: number): number => {
  // Ensure minimum spacing for touch targets
  return Math.max(baseSpacing, WCAG_STANDARDS.TOUCH_TARGETS.SPACING);
};

// Get responsive font size that meets accessibility requirements
export const getResponsiveFontSize = (baseFontSize: number): number => {
  // Ensure minimum font size for readability
  return Math.max(baseFontSize, 12);
};

// Validate and enhance accessibility props
export const enhanceAccessibilityProps = (props: any) => ({
  ...props,
  accessible: props.accessible !== false,
  accessibilityRole: props.accessibilityRole || 'button',
  importantForAccessibility: props.importantForAccessibility || 'yes',
});

// Export all utilities for easy access
export const AccessibilityUtils = {
  WCAG_STANDARDS,
  // Color contrast
  hexToRgb,
  getRelativeLuminance,
  getContrastRatio,
  meetsWCAGAA,
  getWCAGCompliantColor: getWCAGCompliantColorLocal,
  // Focus management
  FocusUtils,
  getFocusIndicatorStyle,
  // Touch targets
  TouchTargetUtils,
  getMinimumTouchTarget,
  getRecommendedTouchTarget,
  validateTouchTargetSize,
  getTouchTargetStyle,
  // Images
  ImageAccessibilityUtils,
  getImageAccessibilityProps,
  // Single-pointer alternatives
  SinglePointerUtils,
  // Screen reader
  ScreenReaderUtils,
  // General utilities
  prefersReducedMotion,
  getResponsiveSpacing,
  getResponsiveFontSize,
  enhanceAccessibilityProps,
};

// Ensure the constants are properly frozen to prevent modification
Object.freeze(WCAG_STANDARDS);
Object.freeze(WCAG_STANDARDS.CONTRAST_RATIOS);
Object.freeze(WCAG_STANDARDS.TOUCH_TARGETS);
Object.freeze(WCAG_STANDARDS.TARGET_SIZE);
Object.freeze(WCAG_STANDARDS.FOCUS_INDICATORS);

// Color Contrast Utilities
export const ColorContrastUtils = {
  /**
   * Convert hex color to RGB values
   */
  hexToRgb: (hex: string): { r: number; g: number; b: number } | null => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  },

  /**
   * Calculate relative luminance of a color
   */
  getRelativeLuminance: (hex: string): number => {
    const rgb = ColorContrastUtils.hexToRgb(hex);
    if (!rgb) return 0;

    const { r, g, b } = rgb;
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  },

  /**
   * Calculate contrast ratio between two colors
   */
  getContrastRatio: (color1: string, color2: string): number => {
    const l1 = ColorContrastUtils.getRelativeLuminance(color1);
    const l2 = ColorContrastUtils.getRelativeLuminance(color2);
    
    const lighter = Math.max(l1, l2);
    const darker = Math.min(l1, l2);
    
    return (lighter + 0.05) / (darker + 0.05);
  },

  /**
   * Validate color contrast for WCAG compliance
   */
  validateContrast: (
    foreground: string,
    background: string,
    level: 'AA' | 'AAA' = 'AA',
    isLargeText: boolean = false
  ): {
    ratio: number;
    isCompliant: boolean;
    requiredRatio: number;
    recommendation: string;
  } => {
    const ratio = ColorContrastUtils.getContrastRatio(foreground, background);
    
    let requiredRatio: number;
    if (level === 'AAA') {
      requiredRatio = isLargeText ? 4.5 : 7.0; // WCAG_STANDARDS.CONTRAST_RATIOS.AAA_LARGE : WCAG_STANDARDS.CONTRAST_RATIOS.AAA_NORMAL;
    } else {
      requiredRatio = isLargeText ? 3.0 : 4.5; // WCAG_STANDARDS.CONTRAST_RATIOS.AA_LARGE : WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL;
    }

    const isCompliant = ratio >= requiredRatio;
    
    let recommendation = '';
    if (!isCompliant) {
      const improvement = (requiredRatio / ratio).toFixed(2);
      recommendation = `Increase contrast by ${improvement}x to meet ${level} standards`;
    } else {
      recommendation = `Meets ${level} standards (${ratio.toFixed(2)}:1)`;
    }

    return {
      ratio: Math.round(ratio * 100) / 100,
      isCompliant,
      requiredRatio,
      recommendation,
    };
  },

  /**
   * Get accessible text color for a given background
   */
  getAccessibleTextColor: (backgroundColor: string): string => {
    const whiteContrast = ColorContrastUtils.getContrastRatio('#FFFFFF', backgroundColor);
    const blackContrast = ColorContrastUtils.getContrastRatio('#000000', backgroundColor);
    
    return whiteContrast > blackContrast ? '#FFFFFF' : '#000000';
  },

  /**
   * Enhance color for better contrast
   */
  enhanceColorContrast: (
    color: string,
    targetBackground: string,
    targetRatio: number = 4.5 // WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL
  ): string => {
    const currentRatio = ColorContrastUtils.getContrastRatio(color, targetBackground);

    if (currentRatio >= targetRatio) {
      return color; // Already compliant
    }

    const rgb = ColorContrastUtils.hexToRgb(color);
    if (!rgb) return color;

    // More aggressive enhancement: significantly darken or lighten the color
    const backgroundLuminance = ColorContrastUtils.getRelativeLuminance(targetBackground);

    // For light backgrounds, darken significantly; for dark backgrounds, lighten significantly
    let enhanced;
    if (backgroundLuminance > 0.5) {
      // Light background - darken the color more aggressively
      const factor = 0.3; // Much more aggressive darkening
      enhanced = {
        r: Math.max(0, Math.round(rgb.r * factor)),
        g: Math.max(0, Math.round(rgb.g * factor)),
        b: Math.max(0, Math.round(rgb.b * factor)),
      };
    } else {
      // Dark background - lighten the color more aggressively
      const factor = 2.5; // Much more aggressive lightening
      enhanced = {
        r: Math.min(255, Math.round(rgb.r * factor)),
        g: Math.min(255, Math.round(rgb.g * factor)),
        b: Math.min(255, Math.round(rgb.b * factor)),
      };
    }

    const enhancedHex = `#${enhanced.r.toString(16).padStart(2, '0')}${enhanced.g.toString(16).padStart(2, '0')}${enhanced.b.toString(16).padStart(2, '0')}`;

    // Verify the enhancement worked, if not, use a fallback
    const newRatio = ColorContrastUtils.getContrastRatio(enhancedHex, targetBackground);
    if (newRatio >= targetRatio) {
      return enhancedHex;
    }

    // Fallback: use high contrast colors
    return backgroundLuminance > 0.5 ? '#000000' : '#FFFFFF';
  },
};








// Accessibility Testing Utilities
export const AccessibilityTestUtils = {
  /**
   * Audit component for accessibility issues
   */
  auditComponent: (componentProps: any): {
    issues: string[];
    warnings: string[];
    recommendations: string[];
  } => {
    const issues: string[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];

    // Check for accessibility label
    if (!componentProps.accessibilityLabel && !componentProps.children) {
      issues.push('Missing accessibility label');
      recommendations.push('Add accessibilityLabel prop');
    }

    // Check for accessibility role
    if (!componentProps.accessibilityRole) {
      warnings.push('Missing accessibility role');
      recommendations.push('Add appropriate accessibilityRole');
    }

    // Check for touch target size
    if (componentProps.style?.width && componentProps.style?.height) {
      const validation = TouchTargetUtils.validateTouchTarget(
        componentProps.style.width,
        componentProps.style.height
      );

      if (!validation.isValid) {
        issues.push(...validation.issues);
        recommendations.push(...validation.recommendations);
      }
    }

    return { issues, warnings, recommendations };
  },

  /**
   * Generate accessibility report
   */
  generateAccessibilityReport: (components: any[]): {
    totalComponents: number;
    issuesFound: number;
    warningsFound: number;
    complianceScore: number;
    details: any[];
  } => {
    const details = components.map((component, index) => ({
      componentIndex: index,
      audit: AccessibilityTestUtils.auditComponent(component),
    }));

    const totalIssues = details.reduce((sum, detail) => sum + detail.audit.issues.length, 0);
    const totalWarnings = details.reduce((sum, detail) => sum + detail.audit.warnings.length, 0);

    const complianceScore = Math.max(0, 100 - (totalIssues * 10) - (totalWarnings * 5));

    return {
      totalComponents: components.length,
      issuesFound: totalIssues,
      warningsFound: totalWarnings,
      complianceScore,
      details,
    };
  },
};

// Export all utilities
export {
  WCAG_STANDARDS,
  ColorContrastUtils,
  TouchTargetUtils,
  FocusUtils,
  ScreenReaderUtils,
  AccessibilityTestUtils,
};

// Default export for convenience
// Voice Control Utilities
export const VoiceControlUtils = {
  /**
   * Check if voice control is available
   */
  isVoiceControlAvailable: (): boolean => {
    return Platform.OS === 'ios' && Platform.Version >= '13.0';
  },

  /**
   * Generate voice control labels for components
   */
  generateVoiceLabel: (text: string, context?: string): string => {
    // Remove special characters and normalize text for voice recognition
    const cleanText = text.replace(/[^\w\s]/gi, '').toLowerCase();
    return context ? `${context} ${cleanText}` : cleanText;
  },

  /**
   * Create voice control hints
   */
  createVoiceHints: (actions: string[]): string => {
    return `Available actions: ${actions.join(', ')}`;
  },
};

// Gesture Accessibility Utilities
export const GestureAccessibilityUtils = {
  /**
   * Check if alternative gestures are needed
   */
  needsAlternativeGestures: async (): Promise<boolean> => {
    try {
      const isReduceMotionEnabled = await AccessibilityInfo.isReduceMotionEnabled();
      return isReduceMotionEnabled;
    } catch {
      return false;
    }
  },

  /**
   * Get accessible gesture alternatives
   */
  getGestureAlternatives: (gestureType: string): string[] => {
    const alternatives: Record<string, string[]> = {
      swipe: ['double tap to activate', 'use navigation buttons'],
      pinch: ['use zoom controls', 'double tap to zoom'],
      longPress: ['use context menu button', 'double tap and hold'],
      drag: ['use move buttons', 'select and use arrow keys'],
    };
    return alternatives[gestureType] || ['use alternative controls'];
  },

  /**
   * Create gesture accessibility instructions
   */
  createGestureInstructions: (gesture: string, alternative: string): string => {
    return `Gesture: ${gesture}. Alternative: ${alternative}`;
  },
};

// Cognitive Accessibility Utilities
export const CognitiveAccessibilityUtils = {
  /**
   * Simplify text for cognitive accessibility
   */
  simplifyText: (text: string, level: 'basic' | 'intermediate' | 'advanced' = 'intermediate'): string => {
    if (level === 'basic') {
      // Use simpler words and shorter sentences
      return text
        .replace(/utilize/gi, 'use')
        .replace(/facilitate/gi, 'help')
        .replace(/approximately/gi, 'about')
        .replace(/subsequently/gi, 'then');
    }
    return text;
  },

  /**
   * Add reading time estimate
   */
  estimateReadingTime: (text: string, wordsPerMinute: number = 200): string => {
    const wordCount = text.split(/\s+/).length;
    const minutes = Math.ceil(wordCount / wordsPerMinute);
    return `Reading time: ${minutes} minute${minutes !== 1 ? 's' : ''}`;
  },

  /**
   * Create progress indicators for multi-step processes
   */
  createProgressIndicator: (currentStep: number, totalSteps: number): string => {
    return `Step ${currentStep} of ${totalSteps}`;
  },
};

// Alias for backward compatibility
export const FocusManagementUtils = FocusUtils;

export default {
  WCAG_STANDARDS,
  ColorContrastUtils,
  TouchTargetUtils,
  FocusUtils,
  FocusManagementUtils,
  ScreenReaderUtils,
  AccessibilityTestUtils,
  VoiceControlUtils,
  GestureAccessibilityUtils,
  CognitiveAccessibilityUtils,

  /**
   * Get WCAG compliant color with proper contrast ratio
   */
  getWCAGCompliantColor: (
    foreground: string,
    background: string,
    level: 'AA' | 'AAA' = 'AA'
  ): string => {
    return getWCAGCompliantColor(foreground, background, level, 'normal', false);
  },

  /**
   * Check if color combination meets WCAG contrast requirements
   */
  checkColorContrast: (
    foreground: string,
    background: string,
    level: 'AA' | 'AAA' = 'AA',
    textSize: 'normal' | 'large' = 'normal'
  ): boolean => {
    const ratio = calculateContrastRatio(foreground, background);
    const requiredRatio = level === 'AAA'
      ? (textSize === 'large' ? 4.5 : 7.0)
      : (textSize === 'large' ? 3.0 : 4.5);
    return ratio >= requiredRatio;
  },
};
