/**
 * Undo Provider Component
 * 
 * Global provider component that manages undo toast notifications across the entire
 * application. Renders active undo toasts and handles their lifecycle automatically.
 * Should be placed at the root level of the application to ensure undo functionality
 * is available throughout the app.
 * 
 * Features:
 * - Global undo toast management
 * - Automatic toast positioning and stacking
 * - Support for multiple concurrent undo operations
 * - Accessibility and haptic feedback integration
 * - Dark mode support
 * - Safe area handling for different devices
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { ReactNode } from 'react';
import { View, StyleSheet } from 'react-native';
import { getSafeAreaInsets } from '../../utils/responsiveUtils';
import { UndoToast } from '../ui/UndoToast';
import { useUndo, UseUndoConfig } from '../../hooks/useUndo';

// Provider props
export interface UndoProviderProps {
  children: ReactNode;
  config?: UseUndoConfig;
  position?: 'top' | 'bottom';
  testID?: string;
}

export const UndoProvider: React.FC<UndoProviderProps> = ({
  children,
  config,
  position = 'bottom',
  testID = 'undo-provider',
}) => {
  const insets = getSafeAreaInsets();
  const { activeToasts, dismissToast } = useUndo(config);

  return (
    <>
      {children}
      
      {/* Undo Toast Container */}
      <View
        style={[
          styles.toastContainer,
          position === 'top' 
            ? { top: insets.top + 10 }
            : { bottom: insets.bottom + 10 },
        ]}
        pointerEvents="box-none"
        testID={testID}
      >
        {activeToasts.map((toast, index) => (
          <View
            key={toast.id}
            style={[
              styles.toastWrapper,
              {
                zIndex: 1000 + index,
                transform: [
                  {
                    translateY: position === 'bottom' 
                      ? -(index * 80) // Stack upwards from bottom
                      : (index * 80), // Stack downwards from top
                  },
                ],
              },
            ]}
          >
            <UndoToast
              operationId={toast.operationId}
              visible={toast.visible}
              onDismiss={() => dismissToast(toast.id)}
              position={position}
              testID={`${testID}-toast-${index}`}
            />
          </View>
        ))}
      </View>
    </>
  );
};

// Styles
const styles = StyleSheet.create({
  toastContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    pointerEvents: 'box-none',
  },
  toastWrapper: {
    position: 'absolute',
    left: 0,
    right: 0,
  },
});

export default UndoProvider;
