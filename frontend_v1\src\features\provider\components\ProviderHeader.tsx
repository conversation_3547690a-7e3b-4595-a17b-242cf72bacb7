/**
 * Provider Header - Header Component for Provider Portal
 * 
 * Displays provider information, notifications, and quick actions
 * in the header of the provider portal.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import { useI18n } from '../../../contexts/I18nContext';
import { Icon } from '../../../components/ui/Icon';

interface ProviderHeaderProps {
  providerName?: string;
  providerAvatar?: string;
  notificationCount?: number;
  onNotificationsPress?: () => void;
  onProfilePress?: () => void;
  onMenuPress?: () => void;
  style?: any;
}

export const ProviderHeader: React.FC<ProviderHeaderProps> = ({
  providerName = 'Elite Cleaning Services',
  providerAvatar,
  notificationCount = 0,
  onNotificationsPress,
  onProfilePress,
  onMenuPress,
  style,
}) => {
  const { colors } = useTheme();
  const { t } = useI18n();
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update time every minute
  React.useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'short',
      day: 'numeric',
    });
  };

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) {
      return t('provider.header.goodMorning');
    } else if (hour < 17) {
      return t('provider.header.goodAfternoon');
    } else {
      return t('provider.header.goodEvening');
    }
  };

  const handleNotificationsPress = () => {
    if (onNotificationsPress) {
      onNotificationsPress();
    } else {
      // Default action - show notifications
      Alert.alert(
        t('provider.header.notifications'),
        t('provider.header.notificationsMessage')
      );
    }
  };

  const handleProfilePress = () => {
    if (onProfilePress) {
      onProfilePress();
    } else {
      // Default action - navigate to profile
      console.log('Navigate to profile');
    }
  };

  const handleMenuPress = () => {
    if (onMenuPress) {
      onMenuPress();
    } else {
      // Default action - show menu
      Alert.alert(
        t('provider.header.menu'),
        t('provider.header.menuMessage')
      );
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }, style]}>
      {/* Top Row - Time and Menu */}
      <View style={styles.topRow}>
        <View style={styles.timeContainer}>
          <Text style={[styles.timeText, { color: colors.text.primary }]}>
            {formatTime(currentTime)}
          </Text>
          <Text style={[styles.dateText, { color: colors.text.secondary }]}>
            {formatDate(currentTime)}
          </Text>
        </View>
        
        <TouchableOpacity
          style={styles.menuButton}
          onPress={handleMenuPress}
          accessibilityRole="button"
          accessibilityLabel={t('provider.header.menu')}
        >
          <Icon name="menu" size={24} color={colors.text.primary} />
        </TouchableOpacity>
      </View>

      {/* Main Row - Greeting, Provider Info, and Actions */}
      <View style={styles.mainRow}>
        {/* Greeting and Provider Info */}
        <TouchableOpacity
          style={styles.providerInfo}
          onPress={handleProfilePress}
          accessibilityRole="button"
          accessibilityLabel={t('provider.header.viewProfile')}
        >
          <View style={styles.avatarContainer}>
            {providerAvatar ? (
              <Image 
                source={{ uri: providerAvatar }} 
                style={styles.avatar}
              />
            ) : (
              <View style={[styles.avatarPlaceholder, { backgroundColor: colors.primary[100] }]}>
                <Icon name="user" size={20} color={colors.primary[500]} />
              </View>
            )}
          </View>
          
          <View style={styles.greetingContainer}>
            <Text style={[styles.greeting, { color: colors.text.secondary }]}>
              {getGreeting()}
            </Text>
            <Text style={[styles.providerName, { color: colors.text.primary }]}>
              {providerName}
            </Text>
          </View>
        </TouchableOpacity>

        {/* Action Buttons */}
        <View style={styles.actions}>
          {/* Notifications */}
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleNotificationsPress}
            accessibilityRole="button"
            accessibilityLabel={
              notificationCount > 0 
                ? t('provider.header.notificationsWithCount', { count: notificationCount })
                : t('provider.header.notifications')
            }
          >
            <View style={styles.notificationContainer}>
              <Icon name="bell" size={20} color={colors.text.primary} />
              {notificationCount > 0 && (
                <View 
                  style={[
                    styles.notificationBadge,
                    { backgroundColor: colors.error[500] }
                  ]}
                >
                  <Text style={[styles.notificationBadgeText, { color: colors.white }]}>
                    {notificationCount > 99 ? '99+' : notificationCount.toString()}
                  </Text>
                </View>
              )}
            </View>
          </TouchableOpacity>

          {/* Quick Actions Menu */}
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              Alert.alert(
                t('provider.header.quickActions'),
                t('provider.header.quickActionsMessage'),
                [
                  { text: t('provider.header.newJob'), onPress: () => console.log('New job') },
                  { text: t('provider.header.viewEarnings'), onPress: () => console.log('View earnings') },
                  { text: t('provider.header.updateAvailability'), onPress: () => console.log('Update availability') },
                  { text: t('common.cancel'), style: 'cancel' },
                ]
              );
            }}
            accessibilityRole="button"
            accessibilityLabel={t('provider.header.quickActions')}
          >
            <Icon name="more-horizontal" size={20} color={colors.text.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Status Indicator */}
      <View style={styles.statusRow}>
        <View style={styles.statusIndicator}>
          <View 
            style={[
              styles.statusDot,
              { backgroundColor: colors.success[500] }
            ]}
          />
          <Text style={[styles.statusText, { color: colors.text.secondary }]}>
            {t('provider.header.online')}
          </Text>
        </View>
        
        <Text style={[styles.statusInfo, { color: colors.text.secondary }]}>
          {t('provider.header.readyForJobs')}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  timeContainer: {
    alignItems: 'flex-start',
  },
  timeText: {
    fontSize: 18,
    fontWeight: '600',
  },
  dateText: {
    fontSize: 14,
    marginTop: 2,
  },
  menuButton: {
    padding: 8,
  },
  mainRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  providerInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    marginRight: 12,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  avatarPlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  greetingContainer: {
    flex: 1,
  },
  greeting: {
    fontSize: 14,
    marginBottom: 2,
  },
  providerName: {
    fontSize: 18,
    fontWeight: '600',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
  },
  notificationContainer: {
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -6,
    right: -6,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  notificationBadgeText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusInfo: {
    fontSize: 12,
  },
});
