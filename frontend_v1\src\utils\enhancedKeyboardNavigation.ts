/**
 * Enhanced Keyboard Navigation System
 * 
 * Provides comprehensive keyboard navigation support with no keyboard traps,
 * ensuring full WCAG 2.2 AA compliance for keyboard-only users.
 * 
 * Features:
 * - Roving tabindex management
 * - Arrow key navigation
 * - Escape key handling
 * - Focus trap prevention
 * - Skip links
 * - Keyboard shortcuts
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Platform } from 'react-native';
import { accessibilityUtils } from './accessibilityUtils';

// Keyboard navigation configuration
export interface KeyboardNavConfig {
  enableArrowKeys: boolean;
  enableHomeEnd: boolean;
  enableEscape: boolean;
  enableSkipLinks: boolean;
  preventTraps: boolean;
  announceChanges: boolean;
}

// Focusable element selector
const FOCUSABLE_SELECTOR = [
  'button',
  '[href]',
  'input',
  'select',
  'textarea',
  '[tabindex]:not([tabindex="-1"])',
  '[role="button"]',
  '[role="link"]',
  '[role="menuitem"]',
  '[role="tab"]',
].join(', ');

// Default configuration
const DEFAULT_CONFIG: KeyboardNavConfig = {
  enableArrowKeys: true,
  enableHomeEnd: true,
  enableEscape: true,
  enableSkipLinks: true,
  preventTraps: true,
  announceChanges: true,
};

/**
 * Enhanced Keyboard Navigation Manager
 */
export class EnhancedKeyboardNavigation {
  private config: KeyboardNavConfig;
  private focusHistory: HTMLElement[] = [];
  private skipLinks: Map<string, HTMLElement> = new Map();
  private activeTraps: Set<string> = new Set();

  constructor(config: Partial<KeyboardNavConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.initialize();
  }

  /**
   * Initialize keyboard navigation system
   */
  private initialize() {
    if (Platform.OS !== 'web') return;

    // Global keyboard event handler
    document.addEventListener('keydown', this.handleGlobalKeyDown.bind(this));
    
    // Focus tracking
    document.addEventListener('focusin', this.handleFocusIn.bind(this));
    document.addEventListener('focusout', this.handleFocusOut.bind(this));

    // Create skip links
    if (this.config.enableSkipLinks) {
      this.createSkipLinks();
    }
  }

  /**
   * Handle global keyboard events
   */
  private handleGlobalKeyDown(event: KeyboardEvent) {
    const { key, target, shiftKey, ctrlKey, metaKey } = event;
    const activeElement = target as HTMLElement;

    // Handle escape key
    if (key === 'Escape' && this.config.enableEscape) {
      this.handleEscape(activeElement);
      return;
    }

    // Handle skip links
    if (key === 'Tab' && !shiftKey && this.isFirstFocusableElement(activeElement)) {
      this.showSkipLinks();
    }

    // Handle arrow key navigation
    if (this.config.enableArrowKeys && this.isArrowKey(key)) {
      this.handleArrowNavigation(event, activeElement);
    }

    // Handle Home/End keys
    if (this.config.enableHomeEnd && (key === 'Home' || key === 'End')) {
      this.handleHomeEnd(event, activeElement);
    }

    // Prevent keyboard traps
    if (this.config.preventTraps && key === 'Tab') {
      this.preventKeyboardTrap(event, activeElement);
    }
  }

  /**
   * Handle focus in events
   */
  private handleFocusIn(event: FocusEvent) {
    const element = event.target as HTMLElement;
    
    // Add to focus history
    this.focusHistory.push(element);
    
    // Limit history size
    if (this.focusHistory.length > 10) {
      this.focusHistory.shift();
    }

    // Announce focus changes for screen readers
    if (this.config.announceChanges) {
      this.announceFocusChange(element);
    }

    // Ensure element is visible
    this.ensureElementVisible(element);
  }

  /**
   * Handle focus out events
   */
  private handleFocusOut(event: FocusEvent) {
    // Hide skip links when focus moves away
    this.hideSkipLinks();
  }

  /**
   * Handle escape key press
   */
  private handleEscape(activeElement: HTMLElement) {
    // Close modals, dropdowns, etc.
    const modal = activeElement.closest('[role="dialog"], [role="alertdialog"]');
    if (modal) {
      const closeButton = modal.querySelector('[aria-label*="close"], [aria-label*="Close"]') as HTMLElement;
      if (closeButton) {
        closeButton.click();
        return;
      }
    }

    // Return to previous focus
    this.restorePreviousFocus();
  }

  /**
   * Handle arrow key navigation
   */
  private handleArrowNavigation(event: KeyboardEvent, activeElement: HTMLElement) {
    const { key } = event;
    const container = this.findNavigationContainer(activeElement);
    
    if (!container) return;

    const focusableElements = this.getFocusableElements(container);
    const currentIndex = focusableElements.indexOf(activeElement);
    
    if (currentIndex === -1) return;

    let nextIndex = currentIndex;

    switch (key) {
      case 'ArrowUp':
        nextIndex = Math.max(0, currentIndex - 1);
        break;
      case 'ArrowDown':
        nextIndex = Math.min(focusableElements.length - 1, currentIndex + 1);
        break;
      case 'ArrowLeft':
        nextIndex = Math.max(0, currentIndex - 1);
        break;
      case 'ArrowRight':
        nextIndex = Math.min(focusableElements.length - 1, currentIndex + 1);
        break;
    }

    if (nextIndex !== currentIndex) {
      event.preventDefault();
      focusableElements[nextIndex].focus();
    }
  }

  /**
   * Handle Home/End key navigation
   */
  private handleHomeEnd(event: KeyboardEvent, activeElement: HTMLElement) {
    const container = this.findNavigationContainer(activeElement);
    
    if (!container) return;

    const focusableElements = this.getFocusableElements(container);
    
    if (focusableElements.length === 0) return;

    event.preventDefault();

    if (event.key === 'Home') {
      focusableElements[0].focus();
    } else if (event.key === 'End') {
      focusableElements[focusableElements.length - 1].focus();
    }
  }

  /**
   * Prevent keyboard traps
   */
  private preventKeyboardTrap(event: KeyboardEvent, activeElement: HTMLElement) {
    const { shiftKey } = event;
    
    // Check if we're in a focus trap
    const trapContainer = activeElement.closest('[data-focus-trap]');
    if (!trapContainer) return;

    const focusableElements = this.getFocusableElements(trapContainer as HTMLElement);
    const currentIndex = focusableElements.indexOf(activeElement);
    
    if (currentIndex === -1) return;

    // Handle forward tab at last element
    if (!shiftKey && currentIndex === focusableElements.length - 1) {
      event.preventDefault();
      focusableElements[0].focus();
    }
    
    // Handle backward tab at first element
    if (shiftKey && currentIndex === 0) {
      event.preventDefault();
      focusableElements[focusableElements.length - 1].focus();
    }
  }

  /**
   * Create skip links for main content areas
   */
  private createSkipLinks() {
    const skipLinksContainer = document.createElement('div');
    skipLinksContainer.className = 'skip-links';
    skipLinksContainer.setAttribute('aria-label', 'Skip navigation links');
    
    const skipLinks = [
      { text: 'Skip to main content', target: '#main-content' },
      { text: 'Skip to navigation', target: '#navigation' },
      { text: 'Skip to search', target: '#search' },
    ];

    skipLinks.forEach(({ text, target }) => {
      const link = document.createElement('a');
      link.href = target;
      link.textContent = text;
      link.className = 'skip-link';
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetElement = document.querySelector(target) as HTMLElement;
        if (targetElement) {
          targetElement.focus();
          targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      });
      
      skipLinksContainer.appendChild(link);
      this.skipLinks.set(target, link);
    });

    document.body.insertBefore(skipLinksContainer, document.body.firstChild);
  }

  /**
   * Show skip links
   */
  private showSkipLinks() {
    const skipLinksContainer = document.querySelector('.skip-links') as HTMLElement;
    if (skipLinksContainer) {
      skipLinksContainer.style.display = 'block';
    }
  }

  /**
   * Hide skip links
   */
  private hideSkipLinks() {
    const skipLinksContainer = document.querySelector('.skip-links') as HTMLElement;
    if (skipLinksContainer) {
      skipLinksContainer.style.display = 'none';
    }
  }

  /**
   * Find navigation container for an element
   */
  private findNavigationContainer(element: HTMLElement): HTMLElement | null {
    return element.closest('[role="menu"], [role="menubar"], [role="tablist"], [role="listbox"], .navigation-container') as HTMLElement;
  }

  /**
   * Get focusable elements within a container
   */
  private getFocusableElements(container: HTMLElement): HTMLElement[] {
    return Array.from(container.querySelectorAll(FOCUSABLE_SELECTOR))
      .filter(el => this.isElementFocusable(el as HTMLElement)) as HTMLElement[];
  }

  /**
   * Check if element is focusable
   */
  private isElementFocusable(element: HTMLElement): boolean {
    if (element.hasAttribute('disabled') || element.getAttribute('aria-disabled') === 'true') {
      return false;
    }
    
    if (element.hasAttribute('hidden') || element.getAttribute('aria-hidden') === 'true') {
      return false;
    }
    
    const style = window.getComputedStyle(element);
    if (style.display === 'none' || style.visibility === 'hidden') {
      return false;
    }
    
    return true;
  }

  /**
   * Check if element is first focusable element on page
   */
  private isFirstFocusableElement(element: HTMLElement): boolean {
    const allFocusable = this.getFocusableElements(document.body);
    return allFocusable[0] === element;
  }

  /**
   * Check if key is an arrow key
   */
  private isArrowKey(key: string): boolean {
    return ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(key);
  }

  /**
   * Announce focus changes for screen readers
   */
  private announceFocusChange(element: HTMLElement) {
    const label = element.getAttribute('aria-label') || 
                  element.getAttribute('aria-labelledby') || 
                  element.textContent || 
                  element.getAttribute('title') || 
                  'Interactive element';
    
    accessibilityUtils.announceForScreenReader(`Focused: ${label}`);
  }

  /**
   * Ensure focused element is visible
   */
  private ensureElementVisible(element: HTMLElement) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
      inline: 'nearest',
    });
  }

  /**
   * Restore previous focus
   */
  private restorePreviousFocus() {
    if (this.focusHistory.length > 1) {
      const previousElement = this.focusHistory[this.focusHistory.length - 2];
      if (previousElement && this.isElementFocusable(previousElement)) {
        previousElement.focus();
      }
    }
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<KeyboardNavConfig>) {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Destroy keyboard navigation system
   */
  destroy() {
    if (Platform.OS !== 'web') return;
    
    document.removeEventListener('keydown', this.handleGlobalKeyDown.bind(this));
    document.removeEventListener('focusin', this.handleFocusIn.bind(this));
    document.removeEventListener('focusout', this.handleFocusOut.bind(this));
    
    // Remove skip links
    const skipLinksContainer = document.querySelector('.skip-links');
    if (skipLinksContainer) {
      skipLinksContainer.remove();
    }
  }
}

// Global instance
export const enhancedKeyboardNavigation = new EnhancedKeyboardNavigation();

// Hook for using enhanced keyboard navigation
export const useEnhancedKeyboardNavigation = (config?: Partial<KeyboardNavConfig>) => {
  if (config) {
    enhancedKeyboardNavigation.updateConfig(config);
  }
  
  return enhancedKeyboardNavigation;
};

export default enhancedKeyboardNavigation;
