/**
 * Accessibility Test Runner Component
 * 
 * This component provides real-time accessibility testing and validation
 * for WCAG 2.1 AA compliance. It can be used during development to
 * identify and fix accessibility issues.
 * 
 * Features:
 * - Real-time accessibility auditing
 * - WCAG 2.1 AA compliance checking
 * - Color contrast validation
 * - Screen reader compatibility testing
 * - Focus management validation
 * - Accessibility score reporting
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  AccessibilityInfo,
} from 'react-native';
import { Colors } from '../../constants/Colors';
import { EnhancedAccessibilityUtils } from '../../utils/accessibilityEnhancements';
import { AccessibleTouchable } from './AccessibleTouchable';
import { Box } from '../layout/Box';

interface AccessibilityTestResult {
  category: string;
  score: number;
  issues: Array<{
    type: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    recommendation: string;
  }>;
}

interface AccessibilityTestRunnerProps {
  /** Whether to show the test runner in development mode */
  enabled?: boolean;
  /** Whether to run tests automatically on mount */
  autoRun?: boolean;
  /** Callback when tests complete */
  onTestComplete?: (results: AccessibilityTestResult[]) => void;
  /** Test ID for automation */
  testID?: string;
}

export const AccessibilityTestRunner: React.FC<AccessibilityTestRunnerProps> = ({
  enabled = __DEV__,
  autoRun = false,
  onTestComplete,
  testID = 'accessibility-test-runner',
}) => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<AccessibilityTestResult[]>([]);
  const [overallScore, setOverallScore] = useState<number>(0);
  const [isVisible, setIsVisible] = useState(false);

  // Run accessibility tests
  const runTests = useCallback(async () => {
    if (!enabled) return;

    setIsRunning(true);
    
    try {
      // Run comprehensive accessibility audit
      const auditResult = await EnhancedAccessibilityUtils.EnhancedAccessibilityTester.runComprehensiveAudit();
      
      // Test color contrast
      const contrastTests = [
        { name: 'Primary Text', fg: Colors.text.primary, bg: Colors.background.primary },
        { name: 'Secondary Text', fg: Colors.text.secondary, bg: Colors.background.primary },
        { name: 'Primary Button', fg: '#FFFFFF', bg: Colors.primary.main },
        { name: 'Error Text', fg: Colors.status.error, bg: Colors.background.primary },
      ];

      const contrastIssues = contrastTests
        .map(test => {
          const result = EnhancedAccessibilityUtils.EnhancedColorContrastValidator.validateContrast(
            test.fg,
            test.bg,
            'normal',
            'AA'
          );
          
          if (!result.isCompliant) {
            return {
              type: 'color-contrast',
              severity: result.severity === 'fail' ? 'high' as const : 'medium' as const,
              description: `${test.name}: ${result.recommendation}`,
              recommendation: `Adjust colors to achieve ${result.requiredRatio}:1 contrast ratio`,
            };
          }
          return null;
        })
        .filter(Boolean) as Array<{
          type: string;
          severity: 'low' | 'medium' | 'high' | 'critical';
          description: string;
          recommendation: string;
        }>;

      // Check screen reader status
      let screenReaderIssues: Array<{
        type: string;
        severity: 'low' | 'medium' | 'high' | 'critical';
        description: string;
        recommendation: string;
      }> = [];

      try {
        const isScreenReaderEnabled = await AccessibilityInfo.isScreenReaderEnabled();
        if (!isScreenReaderEnabled) {
          screenReaderIssues.push({
            type: 'screen-reader',
            severity: 'medium',
            description: 'Screen reader not currently enabled',
            recommendation: 'Enable screen reader (VoiceOver/TalkBack) for comprehensive testing',
          });
        }
      } catch (error) {
        screenReaderIssues.push({
          type: 'screen-reader-detection',
          severity: 'low',
          description: 'Unable to detect screen reader status',
          recommendation: 'Ensure accessibility services are available on device',
        });
      }

      // Compile results
      const testResults: AccessibilityTestResult[] = [
        {
          category: 'General Accessibility',
          score: auditResult.score,
          issues: auditResult.issues,
        },
        {
          category: 'Color Contrast',
          score: contrastIssues.length === 0 ? 100 : Math.max(0, 100 - (contrastIssues.length * 20)),
          issues: contrastIssues,
        },
        {
          category: 'Screen Reader',
          score: screenReaderIssues.length === 0 ? 100 : 80,
          issues: screenReaderIssues,
        },
      ];

      // Calculate overall score
      const totalScore = testResults.reduce((sum, result) => sum + result.score, 0);
      const avgScore = Math.round(totalScore / testResults.length);

      setResults(testResults);
      setOverallScore(avgScore);
      
      if (onTestComplete) {
        onTestComplete(testResults);
      }

      // Announce results to screen reader
      EnhancedAccessibilityUtils.EnhancedScreenReaderUtils.announceDynamicUpdate(
        'content-loaded',
        testResults.length
      );

    } catch (error) {
      console.error('Accessibility test error:', error);
      Alert.alert(
        'Test Error',
        'Failed to run accessibility tests. Check console for details.'
      );
    } finally {
      setIsRunning(false);
    }
  }, [enabled, onTestComplete]);

  // Auto-run tests on mount if enabled
  useEffect(() => {
    if (autoRun && enabled) {
      runTests();
    }
  }, [autoRun, enabled, runTests]);

  // Don't render if not enabled
  if (!enabled) {
    return null;
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return '#FF3B30';
      case 'high': return '#FF9500';
      case 'medium': return '#FFCC00';
      case 'low': return '#34C759';
      default: return Colors.text.secondary;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return '#34C759';
    if (score >= 70) return '#FFCC00';
    if (score >= 50) return '#FF9500';
    return '#FF3B30';
  };

  return (
    <Box style={styles.container} testID={testID}>
      {/* Toggle Button */}
      <AccessibleTouchable
        style={styles.toggleButton}
        onPress={() => setIsVisible(!isVisible)}
        accessibilityLabel={isVisible ? 'Hide accessibility test results' : 'Show accessibility test results'}
        accessibilityHint="Double tap to toggle accessibility test panel visibility"
        accessibilityRole="button">
        <Text style={styles.toggleButtonText}>
          A11y Tests {isVisible ? '▼' : '▶'}
        </Text>
      </AccessibleTouchable>

      {/* Test Panel */}
      {isVisible && (
        <View style={styles.panel}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title} accessibilityRole="heading" accessibilityLevel={2}>
              Accessibility Test Results
            </Text>
            <Text style={[styles.score, { color: getScoreColor(overallScore) }]}>
              Score: {overallScore}/100
            </Text>
          </View>

          {/* Run Tests Button */}
          <AccessibleTouchable
            style={[styles.runButton, isRunning && styles.runButtonDisabled]}
            onPress={runTests}
            disabled={isRunning}
            accessibilityLabel={isRunning ? 'Running accessibility tests' : 'Run accessibility tests'}
            accessibilityHint="Double tap to start comprehensive accessibility testing"
            accessibilityRole="button">
            <Text style={styles.runButtonText}>
              {isRunning ? 'Running Tests...' : 'Run Tests'}
            </Text>
          </AccessibleTouchable>

          {/* Results */}
          {results.length > 0 && (
            <ScrollView style={styles.results} accessibilityLabel="Test results">
              {results.map((result, index) => (
                <View key={index} style={styles.resultCategory}>
                  <View style={styles.categoryHeader}>
                    <Text style={styles.categoryTitle} accessibilityRole="heading" accessibilityLevel={3}>
                      {result.category}
                    </Text>
                    <Text style={[styles.categoryScore, { color: getScoreColor(result.score) }]}>
                      {result.score}/100
                    </Text>
                  </View>
                  
                  {result.issues.map((issue, issueIndex) => (
                    <View key={issueIndex} style={styles.issue}>
                      <View style={styles.issueHeader}>
                        <Text style={[styles.severity, { color: getSeverityColor(issue.severity) }]}>
                          {issue.severity.toUpperCase()}
                        </Text>
                        <Text style={styles.issueType}>{issue.type}</Text>
                      </View>
                      <Text style={styles.issueDescription}>{issue.description}</Text>
                      <Text style={styles.issueRecommendation}>{issue.recommendation}</Text>
                    </View>
                  ))}
                  
                  {result.issues.length === 0 && (
                    <Text style={styles.noIssues}>✓ No issues found</Text>
                  )}
                </View>
              ))}
            </ScrollView>
          )}
        </View>
      )}
    </Box>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 100,
    right: 10,
    zIndex: 1000,
    maxWidth: 300,
  },
  toggleButton: {
    backgroundColor: Colors.primary.main,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    alignSelf: 'flex-end',
  },
  toggleButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  panel: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginTop: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    maxHeight: 400,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    flex: 1,
  },
  score: {
    fontSize: 14,
    fontWeight: '600',
  },
  runButton: {
    backgroundColor: Colors.primary.main,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    marginBottom: 12,
  },
  runButtonDisabled: {
    backgroundColor: Colors.text.disabled,
  },
  runButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  results: {
    maxHeight: 250,
  },
  resultCategory: {
    marginBottom: 16,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  categoryScore: {
    fontSize: 12,
    fontWeight: '600',
  },
  issue: {
    backgroundColor: Colors.background.secondary,
    padding: 8,
    borderRadius: 4,
    marginBottom: 4,
  },
  issueHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  severity: {
    fontSize: 10,
    fontWeight: '700',
    marginRight: 8,
  },
  issueType: {
    fontSize: 10,
    color: Colors.text.secondary,
    textTransform: 'uppercase',
  },
  issueDescription: {
    fontSize: 12,
    color: Colors.text.primary,
    marginBottom: 2,
  },
  issueRecommendation: {
    fontSize: 11,
    color: Colors.text.secondary,
    fontStyle: 'italic',
  },
  noIssues: {
    fontSize: 12,
    color: '#34C759',
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 8,
  },
});

export default AccessibilityTestRunner;
