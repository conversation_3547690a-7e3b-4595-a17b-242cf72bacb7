/**
 * Standardized Button Component
 * 
 * Enforces consistent button terminology, placement, and styling across
 * the application. Addresses Heuristic H4 violations by providing a
 * single, standardized button implementation with consistent labeling.
 * 
 * Features:
 * - Automatic terminology standardization
 * - Consistent icon mapping
 * - Standardized accessibility labels
 * - Proper button placement patterns
 * - Variant enforcement based on action type
 * - Development warnings for non-standard usage
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  ActivityIndicator,
  AccessibilityRole,
} from 'react-native';
import { NavigationIcon } from './IconLibrary';
import { useTheme } from '../../contexts/ThemeContext';
import { Colors } from '../../constants/Colors';
import { HapticPatterns } from '../../utils/hapticPatterns';
import { AccessibilityUtils } from '../../utils/accessibilityUtils';
import {
  BUTTON_LABELS,
  BUTTON_ICONS,
  BUTTON_VARIANTS,
  BUTTON_ACCESSIBILITY,
  <PERSON><PERSON><PERSON><PERSON><PERSON>dsHel<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>tonVarian<PERSON>,
} from '../../design-system/ButtonStandards';

// Component props
export interface StandardizedButtonProps {
  // Action-based props (recommended approach)
  action?: keyof typeof BUTTON_LABELS;
  
  // Manual props (for custom cases)
  title?: string;
  icon?: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'destructive' | 'ghost';
  
  // Behavior props
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  
  // Layout props
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  iconPosition?: 'left' | 'right' | 'top' | 'bottom';
  iconOnly?: boolean;
  
  // Accessibility props
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: AccessibilityRole;
  
  // Styling props
  style?: any;
  textStyle?: any;
  
  // Testing props
  testID?: string;
  
  // Development props
  suppressWarnings?: boolean; // Suppress non-standard terminology warnings
}

export const StandardizedButton: React.FC<StandardizedButtonProps> = ({
  action,
  title: manualTitle,
  icon: manualIcon,
  variant: manualVariant,
  onPress,
  disabled = false,
  loading = false,
  size = 'medium',
  fullWidth = false,
  iconPosition = 'left',
  iconOnly = false,
  accessibilityLabel: manualAccessibilityLabel,
  accessibilityHint: manualAccessibilityHint,
  accessibilityRole = 'button',
  style,
  textStyle,
  testID,
  suppressWarnings = false,
}) => {
  const { isDark } = useTheme();

  // Get standardized props if action is provided
  const standardProps = action ? ButtonStandardsHelpers.getStandardButtonProps(action) : null;
  
  // Resolve final props (standardized props take precedence)
  const title = standardProps?.title || manualTitle || 'Button';
  const icon = standardProps?.icon || manualIcon;
  const variant = (standardProps?.variant as ButtonVariant) || manualVariant || 'secondary';
  const accessibilityLabel = standardProps?.accessibilityLabel || manualAccessibilityLabel || title;
  const accessibilityHint = standardProps?.accessibilityHint || manualAccessibilityHint;

  // Development warnings for non-standard usage
  React.useEffect(() => {
    if (__DEV__ && !suppressWarnings) {
      // Warn about non-standard terminology
      if (manualTitle && !ButtonStandardsHelpers.validateButtonLabel(manualTitle)) {
        const suggestion = ButtonStandardsHelpers.getSuggestedLabel(manualTitle);
        console.warn(
          `[StandardizedButton] Non-standard button label: "${manualTitle}". ` +
          (suggestion ? `Consider using: "${suggestion}"` : 'Consider using a standardized label.')
        );
      }
      
      // Warn about missing action prop
      if (!action && !manualTitle) {
        console.warn('[StandardizedButton] Consider using the "action" prop for better consistency.');
      }
    }
  }, [action, manualTitle, suppressWarnings]);

  // Handle button press
  const handlePress = () => {
    if (!disabled && !loading) {
      // Provide haptic feedback based on variant
      if (variant === 'destructive') {
        HapticPatterns.warningPress();
      } else if (variant === 'primary') {
        HapticPatterns.successPress();
      } else {
        HapticPatterns.lightImpact();
      }
      
      onPress();
    }
  };

  // Get button styles
  const styles = createStyles(isDark, variant, size, fullWidth, disabled);

  // Render icon
  const renderIcon = () => {
    if (!icon || iconOnly === false && !title) return null;
    
    return (
      <NavigationIcon
        name={icon}
        size={getIconSize(size)}
        color={getIconColor(variant, disabled, isDark)}
        style={getIconStyle(iconPosition, iconOnly)}
      />
    );
  };

  // Render content
  const renderContent = () => {
    if (loading) {
      return (
        <ActivityIndicator
          size={size === 'small' ? 'small' : 'small'}
          color={getIconColor(variant, disabled, isDark)}
        />
      );
    }

    if (iconOnly) {
      return renderIcon();
    }

    const iconElement = renderIcon();
    const textElement = (
      <Text style={[styles.text, textStyle]} numberOfLines={1}>
        {title}
      </Text>
    );

    if (!iconElement) {
      return textElement;
    }

    // Arrange icon and text based on position
    if (iconPosition === 'top' || iconPosition === 'bottom') {
      return (
        <View style={styles.verticalContent}>
          {iconPosition === 'top' && iconElement}
          {textElement}
          {iconPosition === 'bottom' && iconElement}
        </View>
      );
    }

    return (
      <View style={styles.horizontalContent}>
        {iconPosition === 'left' && iconElement}
        {textElement}
        {iconPosition === 'right' && iconElement}
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={[styles.button, style]}
      onPress={handlePress}
      disabled={disabled || loading}
      accessibilityRole={accessibilityRole}
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      accessibilityState={{
        disabled: disabled || loading,
        busy: loading,
      }}
      testID={testID}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

// Helper functions
const getIconSize = (size: string): number => {
  switch (size) {
    case 'small':
      return 16;
    case 'large':
      return 24;
    default:
      return 20;
  }
};

const getIconColor = (variant: string, disabled: boolean, isDark: boolean): string => {
  if (disabled) {
    return isDark ? Colors.text.disabled : Colors.text.disabled;
  }

  switch (variant) {
    case 'primary':
      return Colors.text.inverse;
    case 'destructive':
      return Colors.text.inverse;
    case 'outline':
      return Colors.primary.sage;
    case 'ghost':
      return Colors.primary.sage;
    default:
      return isDark ? Colors.text.primary : Colors.text.primary;
  }
};

const getIconStyle = (position: string, iconOnly: boolean) => {
  if (iconOnly) return {};

  const margin = 8;
  switch (position) {
    case 'left':
      return { marginRight: margin };
    case 'right':
      return { marginLeft: margin };
    case 'top':
      return { marginBottom: margin };
    case 'bottom':
      return { marginTop: margin };
    default:
      return {};
  }
};

// Styles
const createStyles = (
  isDark: boolean,
  variant: string,
  size: string,
  fullWidth: boolean,
  disabled: boolean
) =>
  StyleSheet.create({
    button: {
      ...getVariantStyles(variant, isDark),
      ...getSizeStyles(size),
      width: fullWidth ? '100%' : undefined,
      opacity: disabled ? 0.5 : 1,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 8,
    },
    text: {
      ...getTextStyles(variant, size, isDark),
      textAlign: 'center',
    },
    horizontalContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    verticalContent: {
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
    },
  });

const getVariantStyles = (variant: string, isDark: boolean) => {
  switch (variant) {
    case 'primary':
      return {
        backgroundColor: Colors.primary.sage,
        borderWidth: 0,
      };
    case 'destructive':
      return {
        backgroundColor: Colors.status.error,
        borderWidth: 0,
      };
    case 'outline':
      return {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: Colors.primary.sage,
      };
    case 'ghost':
      return {
        backgroundColor: 'transparent',
        borderWidth: 0,
      };
    default: // secondary
      return {
        backgroundColor: isDark ? Colors.background.secondary : Colors.background.tertiary,
        borderWidth: 1,
        borderColor: isDark ? Colors.border.secondary : Colors.border.primary,
      };
  }
};

const getSizeStyles = (size: string) => {
  switch (size) {
    case 'small':
      return {
        paddingHorizontal: 12,
        paddingVertical: 8,
        minHeight: 36,
      };
    case 'large':
      return {
        paddingHorizontal: 24,
        paddingVertical: 16,
        minHeight: 56,
      };
    default: // medium
      return {
        paddingHorizontal: 16,
        paddingVertical: 12,
        minHeight: 44,
      };
  }
};

const getTextStyles = (variant: string, size: string, isDark: boolean) => {
  const baseStyles = {
    fontWeight: '600' as const,
  };

  // Size-based styles
  const sizeStyles = {
    fontSize: size === 'small' ? 14 : size === 'large' ? 18 : 16,
  };

  // Variant-based color
  let color: string;
  switch (variant) {
    case 'primary':
    case 'destructive':
      color = Colors.text.inverse;
      break;
    case 'outline':
    case 'ghost':
      color = Colors.primary.sage;
      break;
    default:
      color = isDark ? Colors.text.primary : Colors.text.primary;
  }

  return {
    ...baseStyles,
    ...sizeStyles,
    color,
  };
};

export default StandardizedButton;

// Export types for external use
export type { StandardizedButtonProps };
