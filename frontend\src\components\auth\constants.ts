/**
 * Authentication Constants - Enhanced based on Frontend Agent feedback
 * Centralized authentication configuration and constants
 */

// Secure storage keys for authentication data
export const AUTH_STORAGE_KEYS = {
  ACCESS_TOKEN: 'vierla_access_token',
  REFRESH_TOKEN: 'vierla_refresh_token',
  USER_DATA: 'vierla_user_data',
  BIOMETRIC_ENABLED: 'vierla_biometric_enabled',
  PIN_HASH: 'vierla_pin_hash',
  DEVICE_ID: 'vierla_device_id',
  LAST_LOGIN: 'vierla_last_login',
  SESSION_DATA: 'vierla_session_data',
} as const;

// Token refresh intervals and timing
export const TOKEN_REFRESH_INTERVALS = {
  // Refresh token 5 minutes before expiry
  DEFAULT_REFRESH_THRESHOLD: 5 * 60 * 1000, // 5 minutes in ms
  
  // Minimum time between refresh attempts
  MIN_REFRESH_INTERVAL: 30 * 1000, // 30 seconds
  
  // Maximum retry attempts for failed refresh
  MAX_RETRY_ATTEMPTS: 3,
  
  // Exponential backoff base delay
  RETRY_BASE_DELAY: 2000, // 2 seconds
  
  // Session timeout (24 hours)
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  
  // Remember me duration (30 days)
  REMEMBER_ME_DURATION: 30 * 24 * 60 * 60 * 1000, // 30 days
} as const;

// Biometric authentication types and configuration
export const BIOMETRIC_AUTH_TYPES = {
  FINGERPRINT: 'fingerprint',
  FACE_ID: 'faceId',
  IRIS: 'iris',
  VOICE: 'voice',
} as const;

export const BIOMETRIC_CONFIG = {
  // Prompt messages for different auth types
  PROMPTS: {
    FINGERPRINT: 'Use your fingerprint to authenticate',
    FACE_ID: 'Use Face ID to authenticate',
    IRIS: 'Use iris scan to authenticate',
    VOICE: 'Use voice recognition to authenticate',
    FALLBACK: 'Use biometric authentication',
  },
  
  // Fallback options
  FALLBACK_TITLE: 'Use PIN',
  CANCEL_TITLE: 'Cancel',
  
  // Security levels
  SECURITY_LEVELS: {
    BIOMETRIC_ANY: 1,
    BIOMETRIC_CURRENT_SET: 2,
    DEVICE_PASSCODE: 3,
  },
} as const;

// PIN authentication configuration
export const PIN_CONFIG = {
  MIN_LENGTH: 4,
  MAX_LENGTH: 8,
  MAX_ATTEMPTS: 5,
  LOCKOUT_DURATION: 5 * 60 * 1000, // 5 minutes
  HASH_ROUNDS: 12, // bcrypt rounds
} as const;

// Session security configuration
export const SESSION_SECURITY = {
  // Device fingerprinting
  DEVICE_FINGERPRINT_ENABLED: true,
  
  // IP address validation
  IP_VALIDATION_ENABLED: false, // Disabled for mobile
  
  // Concurrent session limits
  MAX_CONCURRENT_SESSIONS: 3,
  
  // Session activity timeout
  ACTIVITY_TIMEOUT: 30 * 60 * 1000, // 30 minutes
  
  // Force logout on suspicious activity
  FORCE_LOGOUT_ON_SUSPICIOUS: true,
} as const;

// API endpoints for authentication
export const AUTH_ENDPOINTS = {
  LOGIN: '/api/auth/login/',
  REGISTER: '/api/auth/register/',
  REFRESH: '/api/auth/refresh/',
  LOGOUT: '/api/auth/logout/',
  VERIFY_EMAIL: '/api/auth/verify-email/',
  FORGOT_PASSWORD: '/api/auth/forgot-password/',
  RESET_PASSWORD: '/api/auth/reset-password/',
  CHANGE_PASSWORD: '/api/auth/change-password/',
  PROFILE: '/api/auth/profile/',
} as const;

// Error codes and messages
export const AUTH_ERRORS = {
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  REFRESH_FAILED: 'REFRESH_FAILED',
  BIOMETRIC_NOT_AVAILABLE: 'BIOMETRIC_NOT_AVAILABLE',
  BIOMETRIC_NOT_ENROLLED: 'BIOMETRIC_NOT_ENROLLED',
  PIN_INCORRECT: 'PIN_INCORRECT',
  PIN_LOCKED: 'PIN_LOCKED',
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  NETWORK_ERROR: 'NETWORK_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

export const AUTH_ERROR_MESSAGES = {
  [AUTH_ERRORS.INVALID_CREDENTIALS]: 'We couldn\'t sign you in with those credentials. Please double-check your email and password, or use "Forgot Password" if you need to reset it.',
  [AUTH_ERRORS.TOKEN_EXPIRED]: 'Your session has expired for security. Please sign in again to continue where you left off.',
  [AUTH_ERRORS.TOKEN_INVALID]: 'We couldn\'t verify your identity. Please sign in again, or contact support if this keeps happening.',
  [AUTH_ERRORS.REFRESH_FAILED]: 'We couldn\'t refresh your session. Please sign in again to continue securely.',
  [AUTH_ERRORS.BIOMETRIC_NOT_AVAILABLE]: 'Biometric sign-in isn\'t available on this device. Please use your email and password instead.',
  [AUTH_ERRORS.BIOMETRIC_NOT_ENROLLED]: 'No fingerprint or face ID is set up on this device. Please set one up in your device settings or use your password.',
  [AUTH_ERRORS.PIN_INCORRECT]: 'That PIN isn\'t correct. Please try again or use "Forgot PIN" if you need help.',
  [AUTH_ERRORS.PIN_LOCKED]: 'Too many incorrect attempts. Please wait a few minutes and try again, or use "Forgot PIN" to reset it.',
  [AUTH_ERRORS.SESSION_EXPIRED]: 'Your session has expired for security. Please sign in again to continue where you left off.',
  [AUTH_ERRORS.NETWORK_ERROR]: 'We couldn\'t connect to our servers. Please check your internet connection and try again.',
  [AUTH_ERRORS.UNKNOWN_ERROR]: 'Something unexpected happened. Please try again, or contact support if this continues.',
} as const;

// Validation patterns
export const VALIDATION_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  PHONE: /^\+?[\d\s\-\(\)]{10,}$/,
  PIN: /^\d{4,8}$/,
} as const;

// Password requirements
export const PASSWORD_REQUIREMENTS = {
  MIN_LENGTH: 8,
  MAX_LENGTH: 128,
  REQUIRE_UPPERCASE: true,
  REQUIRE_LOWERCASE: true,
  REQUIRE_NUMBERS: true,
  REQUIRE_SPECIAL_CHARS: true,
  SPECIAL_CHARS: '@$!%*?&',
} as const;

// Role-based authentication configuration
export const ROLE_AUTH_CONFIG = {
  customer: {
    requiredFields: ['email', 'password'],
    optionalFields: ['phone', 'firstName', 'lastName'],
    biometricEnabled: true,
    pinEnabled: true,
    sessionTimeout: TOKEN_REFRESH_INTERVALS.SESSION_TIMEOUT,
  },
  service_provider: {
    requiredFields: ['email', 'password', 'businessName', 'phone'],
    optionalFields: ['firstName', 'lastName', 'businessAddress'],
    biometricEnabled: true,
    pinEnabled: true,
    sessionTimeout: TOKEN_REFRESH_INTERVALS.SESSION_TIMEOUT,
    requireEmailVerification: true,
  },
  admin: {
    requiredFields: ['email', 'password'],
    optionalFields: [],
    biometricEnabled: false, // Admins use more secure auth
    pinEnabled: false,
    sessionTimeout: 4 * 60 * 60 * 1000, // 4 hours for admins
    requireTwoFactor: true,
  },
} as const;
