/**
 * Provider Authentication Hook
 * 
 * Manages provider authentication state and provides authentication
 * utilities for the provider portal.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useState, useEffect } from 'react';
import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface Provider {
  id: string;
  businessName: string;
  email: string;
  isVerified: boolean;
  profilePhoto?: string;
  contactInfo: {
    phone: string;
    email: string;
  };
  location: {
    city: string;
    province: string;
  };
}

interface UseProviderAuthReturn {
  provider: Provider | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshProvider: () => Promise<void>;
}

const PROVIDER_STORAGE_KEY = '@vierla_provider_auth';

export const useProviderAuth = (): UseProviderAuthReturn => {
  const [provider, setProvider] = useState<Provider | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize authentication state
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      setIsLoading(true);
      
      // Check for stored authentication
      const storedAuth = await AsyncStorage.getItem(PROVIDER_STORAGE_KEY);
      
      if (storedAuth) {
        const authData = JSON.parse(storedAuth);
        
        // Validate stored token (in real app, verify with backend)
        if (authData.token && authData.provider) {
          setProvider(authData.provider);
          setIsAuthenticated(true);
        } else {
          // Clear invalid stored data
          await AsyncStorage.removeItem(PROVIDER_STORAGE_KEY);
        }
      }
    } catch (error) {
      console.error('Error initializing provider auth:', error);
      // Clear potentially corrupted data
      await AsyncStorage.removeItem(PROVIDER_STORAGE_KEY);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      // Mock authentication - replace with actual API call
      if (email === '<EMAIL>' && password === 'password123') {
        const mockProvider: Provider = {
          id: 'provider_1',
          businessName: 'Elite Cleaning Services',
          email: '<EMAIL>',
          isVerified: true,
          profilePhoto: 'https://via.placeholder.com/150',
          contactInfo: {
            phone: '+****************',
            email: '<EMAIL>',
          },
          location: {
            city: 'Toronto',
            province: 'Ontario',
          },
        };

        const authData = {
          token: 'mock_provider_token_' + Date.now(),
          provider: mockProvider,
          expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
        };

        // Store authentication data
        await AsyncStorage.setItem(PROVIDER_STORAGE_KEY, JSON.stringify(authData));
        
        setProvider(mockProvider);
        setIsAuthenticated(true);
        
        return true;
      } else {
        Alert.alert(
          'Login Failed',
          'Invalid email or password. Please try again.'
        );
        return false;
      }
    } catch (error) {
      console.error('Error during provider login:', error);
      Alert.alert(
        'Login Error',
        'An error occurred during login. Please try again.'
      );
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);
      
      // Clear stored authentication
      await AsyncStorage.removeItem(PROVIDER_STORAGE_KEY);
      
      // Reset state
      setProvider(null);
      setIsAuthenticated(false);
      
      // In a real app, you might also want to:
      // - Invalidate the token on the backend
      // - Clear other provider-related cached data
      // - Navigate to login screen
      
    } catch (error) {
      console.error('Error during provider logout:', error);
      Alert.alert(
        'Logout Error',
        'An error occurred during logout. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const refreshProvider = async (): Promise<void> => {
    try {
      if (!isAuthenticated || !provider) {
        return;
      }

      // In a real app, fetch updated provider data from backend
      // For now, we'll just simulate a refresh
      console.log('Refreshing provider data...');
      
      // Mock updated data
      const updatedProvider: Provider = {
        ...provider,
        // Add any updated fields here
      };

      setProvider(updatedProvider);

      // Update stored data
      const storedAuth = await AsyncStorage.getItem(PROVIDER_STORAGE_KEY);
      if (storedAuth) {
        const authData = JSON.parse(storedAuth);
        authData.provider = updatedProvider;
        await AsyncStorage.setItem(PROVIDER_STORAGE_KEY, JSON.stringify(authData));
      }
    } catch (error) {
      console.error('Error refreshing provider data:', error);
    }
  };

  return {
    provider,
    isAuthenticated,
    isLoading,
    login,
    logout,
    refreshProvider,
  };
};
