/**
 * Performance Monitoring Utilities
 *
 * Component Contract:
 * - Provides comprehensive performance monitoring and metrics collection
 * - Tracks component render times, navigation performance, and API calls
 * - Implements performance budgets and alerts
 * - Provides real-time performance insights
 * - Integrates with analytics and monitoring services
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
  metadata?: Record<string, any>;
}

interface PerformanceBudget {
  metric: string;
  threshold: number;
  severity: 'warning' | 'error';
}

interface NavigationTiming {
  screenName: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

interface RenderTiming {
  componentName: string;
  renderCount: number;
  totalTime: number;
  averageTime: number;
  lastRenderTime: number;
}

/**
 * Performance monitoring class
 */
export class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private budgets: PerformanceBudget[] = [];
  private navigationTimings: Map<string, NavigationTiming> = new Map();
  private renderTimings: Map<string, RenderTiming> = new Map();
  private isEnabled: boolean = __DEV__;

  constructor() {
    this.setupDefaultBudgets();
  }

  /**
   * Enable or disable performance monitoring
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * Record a performance metric
   */
  recordMetric(
    name: string,
    value: number,
    tags?: Record<string, string>,
    metadata?: Record<string, any>
  ): void {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      tags,
      metadata,
    };

    this.metrics.push(metric);
    this.checkBudgets(metric);

    // Keep only last 1000 metrics to prevent memory leaks
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    if (__DEV__) {
      console.log(`[Performance] ${name}: ${value}ms`, tags);
    }
  }

  /**
   * Start timing a performance metric
   */
  startTiming(name: string): () => void {
    if (!this.isEnabled) return () => {};

    const startTime = performance.now();

    return (tags?: Record<string, string>, metadata?: Record<string, any>) => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.recordMetric(name, duration, tags, metadata);
    };
  }

  /**
   * Measure function execution time
   */
  measureFunction<T extends (...args: any[]) => any>(
    fn: T,
    name?: string
  ): T {
    if (!this.isEnabled) return fn;

    const functionName = name || fn.name || 'anonymous';

    return ((...args: Parameters<T>) => {
      const endTiming = this.startTiming(`function.${functionName}`);
      const result = fn(...args);

      if (result instanceof Promise) {
        return result.finally(() => endTiming()) as ReturnType<T>;
      } else {
        endTiming();
        return result;
      }
    }) as T;
  }

  /**
   * Track navigation performance
   */
  startNavigation(screenName: string, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return;

    this.navigationTimings.set(screenName, {
      screenName,
      startTime: performance.now(),
      metadata,
    });
  }

  /**
   * End navigation timing
   */
  endNavigation(screenName: string): void {
    if (!this.isEnabled) return;

    const timing = this.navigationTimings.get(screenName);
    if (timing) {
      const endTime = performance.now();
      const duration = endTime - timing.startTime;

      timing.endTime = endTime;
      timing.duration = duration;

      this.recordMetric(
        'navigation.duration',
        duration,
        { screen: screenName },
        timing.metadata
      );

      this.navigationTimings.delete(screenName);
    }
  }

  /**
   * Track component render performance
   */
  trackRender(componentName: string, renderTime: number): void {
    if (!this.isEnabled) return;

    const existing = this.renderTimings.get(componentName);
    if (existing) {
      existing.renderCount++;
      existing.totalTime += renderTime;
      existing.averageTime = existing.totalTime / existing.renderCount;
      existing.lastRenderTime = renderTime;
    } else {
      this.renderTimings.set(componentName, {
        componentName,
        renderCount: 1,
        totalTime: renderTime,
        averageTime: renderTime,
        lastRenderTime: renderTime,
      });
    }

    this.recordMetric(
      'component.render',
      renderTime,
      { component: componentName }
    );
  }

  /**
   * Set performance budgets
   */
  setBudgets(budgets: PerformanceBudget[]): void {
    this.budgets = budgets;
  }

  /**
   * Add a performance budget
   */
  addBudget(budget: PerformanceBudget): void {
    this.budgets.push(budget);
  }

  /**
   * Get performance metrics
   */
  getMetrics(name?: string, since?: number): PerformanceMetric[] {
    let filtered = this.metrics;

    if (name) {
      filtered = filtered.filter(metric => metric.name === name);
    }

    if (since) {
      filtered = filtered.filter(metric => metric.timestamp >= since);
    }

    return filtered;
  }

  /**
   * Get navigation timings
   */
  getNavigationTimings(): NavigationTiming[] {
    return Array.from(this.navigationTimings.values());
  }

  /**
   * Get render timings
   */
  getRenderTimings(): RenderTiming[] {
    return Array.from(this.renderTimings.values());
  }

  /**
   * Get performance summary
   */
  getSummary(): {
    totalMetrics: number;
    averageNavigationTime: number;
    slowestComponents: RenderTiming[];
    budgetViolations: number;
  } {
    const navigationMetrics = this.getMetrics('navigation.duration');
    const averageNavigationTime = navigationMetrics.length > 0
      ? navigationMetrics.reduce((sum, metric) => sum + metric.value, 0) / navigationMetrics.length
      : 0;

    const slowestComponents = Array.from(this.renderTimings.values())
      .sort((a, b) => b.averageTime - a.averageTime)
      .slice(0, 5);

    const budgetViolations = this.metrics.filter(metric =>
      this.budgets.some(budget =>
        budget.metric === metric.name && metric.value > budget.threshold
      )
    ).length;

    return {
      totalMetrics: this.metrics.length,
      averageNavigationTime,
      slowestComponents,
      budgetViolations,
    };
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics = [];
    this.navigationTimings.clear();
    this.renderTimings.clear();
  }

  /**
   * Setup default performance budgets
   */
  private setupDefaultBudgets(): void {
    this.budgets = [
      { metric: 'navigation.duration', threshold: 1000, severity: 'warning' },
      { metric: 'navigation.duration', threshold: 2000, severity: 'error' },
      { metric: 'component.render', threshold: 16, severity: 'warning' },
      { metric: 'component.render', threshold: 32, severity: 'error' },
      { metric: 'api.request', threshold: 3000, severity: 'warning' },
      { metric: 'api.request', threshold: 5000, severity: 'error' },
    ];
  }

  /**
   * Check if metric violates any budgets
   */
  private checkBudgets(metric: PerformanceMetric): void {
    const violations = this.budgets.filter(
      budget => budget.metric === metric.name && metric.value > budget.threshold
    );

    violations.forEach(violation => {
      const message = `Performance budget violation: ${metric.name} (${metric.value}ms) exceeds ${violation.threshold}ms threshold`;
      
      if (violation.severity === 'error') {
        console.error(`[Performance] ${message}`);
      } else {
        console.warn(`[Performance] ${message}`);
      }
    });
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * React hook for component performance tracking
 */
export const usePerformanceTracking = (componentName: string) => {
  const trackRender = (renderTime: number) => {
    performanceMonitor.trackRender(componentName, renderTime);
  };

  const measureRender = () => {
    const startTime = performance.now();
    return () => {
      const endTime = performance.now();
      trackRender(endTime - startTime);
    };
  };

  return { trackRender, measureRender };
};

/**
 * Performance decorator for class methods
 */
export const performanceDecorator = (name?: string) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;
    const methodName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = function (...args: any[]) {
      const endTiming = performanceMonitor.startTiming(methodName);
      const result = originalMethod.apply(this, args);

      if (result instanceof Promise) {
        return result.finally(() => endTiming());
      } else {
        endTiming();
        return result;
      }
    };

    return descriptor;
  };
};
