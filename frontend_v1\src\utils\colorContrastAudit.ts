/**
 * Color Contrast Audit System
 * 
 * Comprehensive system for verifying WCAG 2.2 AA color contrast compliance
 * across all text and UI components in the application.
 * 
 * Features:
 * - Automated contrast ratio calculation
 * - WCAG 2.2 AA/AAA compliance checking
 * - Real-time contrast validation
 * - Color palette optimization
 * - Accessibility reporting
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// WCAG contrast ratio requirements
export const WCAG_CONTRAST_RATIOS = {
  AA: {
    normalText: 4.5,
    largeText: 3.0,
    uiComponents: 3.0,
  },
  AAA: {
    normalText: 7.0,
    largeText: 4.5,
    uiComponents: 4.5,
  },
} as const;

// Text size thresholds for WCAG
export const TEXT_SIZE_THRESHOLDS = {
  largeText: {
    fontSize: 18, // 18px or larger
    fontWeight: 'normal',
  },
  largeBoldText: {
    fontSize: 14, // 14px or larger if bold
    fontWeight: 'bold',
  },
} as const;

// Color contrast audit result
export interface ContrastAuditResult {
  ratio: number;
  passes: {
    AA: boolean;
    AAA: boolean;
  };
  level: 'AA' | 'AAA' | 'FAIL';
  recommendation?: string;
}

// Color pair for testing
export interface ColorPair {
  foreground: string;
  background: string;
  context: string; // Description of where this pair is used
  textSize?: 'normal' | 'large';
  isUIComponent?: boolean;
}

/**
 * Convert hex color to RGB
 */
function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16),
  } : null;
}

/**
 * Calculate relative luminance of a color
 */
function getRelativeLuminance(r: number, g: number, b: number): number {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}

/**
 * Calculate contrast ratio between two colors
 */
export function calculateContrastRatio(color1: string, color2: string): number {
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);
  
  if (!rgb1 || !rgb2) {
    throw new Error('Invalid color format. Please use hex colors.');
  }
  
  const lum1 = getRelativeLuminance(rgb1.r, rgb1.g, rgb1.b);
  const lum2 = getRelativeLuminance(rgb2.r, rgb2.g, rgb2.b);
  
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
}

/**
 * Check if color pair meets WCAG requirements
 */
export function auditColorPair(pair: ColorPair): ContrastAuditResult {
  const ratio = calculateContrastRatio(pair.foreground, pair.background);
  
  let requiredRatio: number;
  
  if (pair.isUIComponent) {
    requiredRatio = WCAG_CONTRAST_RATIOS.AA.uiComponents;
  } else if (pair.textSize === 'large') {
    requiredRatio = WCAG_CONTRAST_RATIOS.AA.largeText;
  } else {
    requiredRatio = WCAG_CONTRAST_RATIOS.AA.normalText;
  }
  
  const passesAA = ratio >= requiredRatio;
  const passesAAA = pair.isUIComponent 
    ? ratio >= WCAG_CONTRAST_RATIOS.AAA.uiComponents
    : pair.textSize === 'large'
      ? ratio >= WCAG_CONTRAST_RATIOS.AAA.largeText
      : ratio >= WCAG_CONTRAST_RATIOS.AAA.normalText;
  
  let level: 'AA' | 'AAA' | 'FAIL';
  let recommendation: string | undefined;
  
  if (passesAAA) {
    level = 'AAA';
  } else if (passesAA) {
    level = 'AA';
  } else {
    level = 'FAIL';
    recommendation = generateRecommendation(pair, ratio, requiredRatio);
  }
  
  return {
    ratio,
    passes: {
      AA: passesAA,
      AAA: passesAAA,
    },
    level,
    recommendation,
  };
}

/**
 * Generate recommendation for failing color pairs
 */
function generateRecommendation(pair: ColorPair, currentRatio: number, requiredRatio: number): string {
  const improvement = requiredRatio / currentRatio;
  
  if (improvement < 1.2) {
    return `Slightly adjust the ${pair.foreground} foreground or ${pair.background} background color to improve contrast.`;
  } else if (improvement < 2) {
    return `Consider using a darker foreground or lighter background color for better contrast.`;
  } else {
    return `Significant color changes needed. Consider using high-contrast color combinations from the design system.`;
  }
}

/**
 * Audit entire color palette
 */
export function auditColorPalette(colorPairs: ColorPair[]): {
  results: Array<ColorPair & { audit: ContrastAuditResult }>;
  summary: {
    total: number;
    passing: number;
    failing: number;
    aaCompliant: number;
    aaaCompliant: number;
  };
} {
  const results = colorPairs.map(pair => ({
    ...pair,
    audit: auditColorPair(pair),
  }));
  
  const summary = {
    total: results.length,
    passing: results.filter(r => r.audit.passes.AA).length,
    failing: results.filter(r => !r.audit.passes.AA).length,
    aaCompliant: results.filter(r => r.audit.level === 'AA' || r.audit.level === 'AAA').length,
    aaaCompliant: results.filter(r => r.audit.level === 'AAA').length,
  };
  
  return { results, summary };
}

/**
 * Get WCAG compliant color
 */
export function getWCAGCompliantColor(
  foreground: string,
  background: string,
  level: 'AA' | 'AAA' = 'AA',
  textSize: 'normal' | 'large' = 'normal',
  isUIComponent: boolean = false
): string {
  const currentRatio = calculateContrastRatio(foreground, background);
  
  let requiredRatio: number;
  if (isUIComponent) {
    requiredRatio = WCAG_CONTRAST_RATIOS[level].uiComponents;
  } else if (textSize === 'large') {
    requiredRatio = WCAG_CONTRAST_RATIOS[level].largeText;
  } else {
    requiredRatio = WCAG_CONTRAST_RATIOS[level].normalText;
  }
  
  if (currentRatio >= requiredRatio) {
    return foreground; // Already compliant
  }
  
  // Adjust foreground color to meet requirements
  const fgRgb = hexToRgb(foreground);
  const bgRgb = hexToRgb(background);
  
  if (!fgRgb || !bgRgb) {
    throw new Error('Invalid color format');
  }
  
  const bgLuminance = getRelativeLuminance(bgRgb.r, bgRgb.g, bgRgb.b);
  
  // Determine if we should make foreground darker or lighter
  const targetLuminance = bgLuminance > 0.5
    ? (bgLuminance + 0.05) / requiredRatio - 0.05  // Make darker
    : (bgLuminance + 0.05) * requiredRatio - 0.05; // Make lighter
  
  // Adjust RGB values to achieve target luminance
  const factor = targetLuminance > bgLuminance ? 1.2 : 0.8;
  
  const adjustedR = Math.min(255, Math.max(0, Math.round(fgRgb.r * factor)));
  const adjustedG = Math.min(255, Math.max(0, Math.round(fgRgb.g * factor)));
  const adjustedB = Math.min(255, Math.max(0, Math.round(fgRgb.b * factor)));
  
  return `#${adjustedR.toString(16).padStart(2, '0')}${adjustedG.toString(16).padStart(2, '0')}${adjustedB.toString(16).padStart(2, '0')}`;
}

/**
 * Vierla app color pairs for audit
 */
export const VIERLA_COLOR_PAIRS: ColorPair[] = [
  // Primary text combinations
  {
    foreground: '#1F2937', // gray-800
    background: '#FFFFFF', // white
    context: 'Primary text on white background',
    textSize: 'normal',
  },
  {
    foreground: '#FFFFFF', // white
    background: '#1F2937', // gray-800
    context: 'White text on dark background',
    textSize: 'normal',
  },
  
  // Primary button
  {
    foreground: '#FFFFFF', // white
    background: '#2A4B32', // primary green
    context: 'Primary button text',
    textSize: 'normal',
  },
  
  // Secondary text
  {
    foreground: '#6B7280', // gray-500
    background: '#FFFFFF', // white
    context: 'Secondary text on white background',
    textSize: 'normal',
  },
  
  // Error states
  {
    foreground: '#DC2626', // red-600
    background: '#FFFFFF', // white
    context: 'Error text',
    textSize: 'normal',
  },
  
  // Success states
  {
    foreground: '#059669', // green-600
    background: '#FFFFFF', // white
    context: 'Success text',
    textSize: 'normal',
  },
  
  // UI Components
  {
    foreground: '#2A4B32', // primary
    background: '#F3F4F6', // gray-100
    context: 'Primary button outline',
    isUIComponent: true,
  },
  
  // Links
  {
    foreground: '#2563EB', // blue-600
    background: '#FFFFFF', // white
    context: 'Link text',
    textSize: 'normal',
  },
  
  // Form inputs
  {
    foreground: '#1F2937', // gray-800
    background: '#F9FAFB', // gray-50
    context: 'Form input text',
    textSize: 'normal',
  },
  
  // Navigation
  {
    foreground: '#374151', // gray-700
    background: '#FFFFFF', // white
    context: 'Navigation text',
    textSize: 'normal',
  },
];

/**
 * Run complete color contrast audit for Vierla app
 */
export function runVierlaContrastAudit() {
  return auditColorPalette(VIERLA_COLOR_PAIRS);
}

/**
 * Generate contrast audit report
 */
export function generateContrastReport(auditResults: ReturnType<typeof auditColorPalette>): string {
  const { results, summary } = auditResults;
  
  let report = `# Color Contrast Audit Report\n\n`;
  report += `## Summary\n`;
  report += `- Total color pairs tested: ${summary.total}\n`;
  report += `- Passing WCAG AA: ${summary.passing}/${summary.total} (${Math.round(summary.passing / summary.total * 100)}%)\n`;
  report += `- Failing WCAG AA: ${summary.failing}/${summary.total} (${Math.round(summary.failing / summary.total * 100)}%)\n`;
  report += `- WCAG AAA compliant: ${summary.aaaCompliant}/${summary.total} (${Math.round(summary.aaaCompliant / summary.total * 100)}%)\n\n`;
  
  if (summary.failing > 0) {
    report += `## Failing Color Pairs\n\n`;
    results
      .filter(r => !r.audit.passes.AA)
      .forEach(result => {
        report += `### ${result.context}\n`;
        report += `- Foreground: ${result.foreground}\n`;
        report += `- Background: ${result.background}\n`;
        report += `- Contrast Ratio: ${result.audit.ratio.toFixed(2)}:1\n`;
        report += `- Status: ${result.audit.level}\n`;
        if (result.audit.recommendation) {
          report += `- Recommendation: ${result.audit.recommendation}\n`;
        }
        report += `\n`;
      });
  }
  
  report += `## All Results\n\n`;
  results.forEach(result => {
    const status = result.audit.passes.AA ? '✅' : '❌';
    report += `${status} ${result.context}: ${result.audit.ratio.toFixed(2)}:1 (${result.audit.level})\n`;
  });
  
  return report;
}
