/**
 * UI Components Export Index
 * 
 * Centralized export for all UI components in the Vierla application.
 * Organized by component type and functionality.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Layout Components
export { default as AnimatedGradientBackground } from './AnimatedGradientBackground';
export { default as RadialGradientBackground } from './RadialGradientBackground';
export { default as SafeAreaWrapper } from './SafeAreaWrapper';

// Navigation Components
export { default as BreadcrumbNavigation } from './BreadcrumbNavigation';
export { default as MegaMenu } from './MegaMenu';
export { default as StickyNavigationBar } from './StickyNavigationBar';

// Dashboard Components
export { default as BentoGrid } from './BentoGrid';
export { default as DashboardWidget } from './DashboardWidget';

// Utility Components
export { default as ErrorBoundary } from './ErrorBoundary';
export { default as LoadingStates } from './LoadingStates';
export { default as MockWebView } from './MockWebView';
export { default as UndoToast } from './UndoToast';

// Button Components (Unified)
export { UnifiedButton } from './UnifiedButton';
export { UnifiedButton as Button } from './UnifiedButton'; // Backward compatibility
export { UnifiedButton as StandardizedButton } from './UnifiedButton'; // Backward compatibility
export { UnifiedButton as MinimalistButton } from './UnifiedButton'; // Backward compatibility

// Feedback System Components
export * from './ConfirmationModal';
export * from './ActionFeedbackSystem';
export * from './SystemFeedbackDisplay';
export * from './FeedbackSystem';

// Performance Components (REC-PERF-001)
export { LazyImage } from './LazyImage';
export { OptimizedImage } from './OptimizedImage';
export { LazyComponent } from './LazyComponent';
export { LazyFlatList } from './LazyFlatList';

// Icon System
export { default as IconLibrary } from './IconLibrary';
export { default as IconShowcase } from './IconShowcase';

// Type Exports
export type {
  BentoWidget,
  BentoAction,
  BentoGridProps,
  WidgetSize,
} from './BentoGrid';

export type {
  DashboardWidgetProps,
  MetricData,
  ChartData,
  ListData,
  ActionData,
} from './DashboardWidget';

// Navigation Types
export type { BreadcrumbItem } from './BreadcrumbNavigation';
export type { MegaMenuSection, MegaMenuItem } from './MegaMenu';

// Undo Types
export type { UndoToastProps } from './UndoToast';

// Feedback System Types
export type {
  ConfirmationModalProps,
  ConfirmationAction
} from './ConfirmationModal';
export type {
  ActionFeedbackState,
  SystemFeedbackDisplayProps
} from './ActionFeedbackSystem';

// Component Categories for Documentation
export const UI_COMPONENT_CATEGORIES = {
  LAYOUT: 'Layout & Background Components',
  NAVIGATION: 'Navigation Components',
  DASHBOARD: 'Dashboard & Data Components',
  UTILITY: 'Utility Components',
  FEEDBACK: 'Feedback & Confirmation Components',
  ICONS: 'Icon System Components',
  PERFORMANCE: 'Performance & Lazy Loading Components',
} as const;
