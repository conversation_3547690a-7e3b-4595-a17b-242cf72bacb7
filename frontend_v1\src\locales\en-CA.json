{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "ok": "OK", "yes": "Yes", "no": "No", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "retry": "Try Again", "learnMore": "Learn More", "viewAll": "View All", "showMore": "Show More", "showLess": "Show Less", "language": "Language", "settings": "Settings", "help": "Help", "support": "Support", "about": "About", "version": "Version", "terms": "Terms of Service", "privacy": "Privacy Policy", "contact": "Contact Us"}, "navigation": {"home": "Home", "services": "Services", "bookings": "Bookings", "messages": "Messages", "profile": "Profile", "settings": "Settings", "help": "Help", "about": "About", "menu": "<PERSON><PERSON>", "skipToContent": "Skip to main content", "skipToNavigation": "Skip to navigation"}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "createAccount": "Create Account", "welcomeBack": "Welcome Back", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "phoneNumber": "Phone Number", "rememberMe": "Remember Me", "termsAndConditions": "Terms and Conditions", "privacyPolicy": "Privacy Policy", "agreeToTerms": "I agree to the Terms and Conditions", "invalidCredentials": "Invalid email or password", "accountCreated": "Account created successfully", "passwordReset": "Password reset link sent to your email"}, "booking": {"bookService": "Book Service", "selectDate": "Select Date", "selectTime": "Select Time", "selectProvider": "Select Provider", "bookingDetails": "Booking Details", "bookingConfirmation": "Booking Confirmation", "bookingCancelled": "Booking Cancelled", "bookingCompleted": "Booking Completed", "upcomingBookings": "Upcoming Bookings", "pastBookings": "Past Bookings", "cancelBooking": "Cancel Booking", "rescheduleBooking": "Reschedule Booking", "confirmBooking": "Confirm Booking", "bookingReference": "Booking Reference", "serviceName": "Service", "providerName": "Provider", "dateTime": "Date & Time", "duration": "Duration", "price": "Price", "totalPrice": "Total Price", "taxes": "Taxes", "subtotal": "Subtotal", "paymentMethod": "Payment Method", "billingAddress": "Billing Address", "serviceAddress": "Service Address"}, "profile": {"myProfile": "My Profile", "editProfile": "Edit Profile", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "preferences": "Preferences", "notifications": "Notifications", "security": "Security", "changePassword": "Change Password", "deleteAccount": "Delete Account", "profileUpdated": "Profile updated successfully", "profilePicture": "Profile Picture", "uploadPhoto": "Upload Photo", "removePhoto": "Remove Photo", "language": "Language", "timezone": "Time Zone", "currency": "<PERSON><PERSON><PERSON><PERSON>", "address": "Address", "city": "City", "province": "Province", "postalCode": "Postal Code", "country": "Country"}, "services": {"browseServices": "Browse Services", "featuredServices": "Featured Services", "popularServices": "Popular Services", "nearbyServices": "Nearby Services", "serviceCategories": "Service Categories", "serviceDetails": "Service Details", "serviceDescription": "Description", "serviceIncludes": "What's Included", "serviceRequirements": "Requirements", "serviceDuration": "Duration", "servicePrice": "Price", "serviceRating": "Rating", "serviceReviews": "Reviews", "serviceAvailability": "Availability", "serviceLocation": "Location", "addToFavorites": "Add to Favourites", "removeFromFavorites": "Remove from Favourites", "shareService": "Share Service", "reportService": "Report Service"}, "providers": {"serviceProviders": "Service Providers", "featuredProviders": "Featured Providers", "topRatedProviders": "Top Rated Providers", "nearbyProviders": "Nearby Providers", "providerProfile": "Provider Profile", "providerServices": "Services Offered", "providerReviews": "Reviews", "providerRating": "Rating", "providerExperience": "Experience", "providerCertifications": "Certifications", "providerInsurance": "Insurance", "contactProvider": "Contact Provider", "viewProfile": "View Profile", "yearsExperience": "Years of Experience", "completedJobs": "Completed Jobs", "responseTime": "Response Time", "availability": "Availability"}, "messages": {"messages": "Messages", "newMessage": "New Message", "sendMessage": "Send Message", "messageHistory": "Message History", "conversation": "Conversation", "typing": "Typing...", "online": "Online", "offline": "Offline", "lastSeen": "Last seen", "messageDelivered": "Delivered", "messageRead": "Read", "attachFile": "Attach File", "takePhoto": "Take Photo", "chooseFromLibrary": "Choose from Library", "recordVoice": "Record Voice Message", "messageDeleted": "Message deleted", "conversationDeleted": "Conversation deleted"}, "payments": {"payment": "Payment", "paymentMethod": "Payment Method", "addPaymentMethod": "Add Payment Method", "creditCard": "Credit Card", "debitCard": "Debit Card", "paypal": "PayPal", "applePay": "Apple Pay", "googlePay": "Google Pay", "cardNumber": "Card Number", "expiryDate": "Expiry Date", "cvv": "CVV", "cardholderName": "Cardholder Name", "billingAddress": "Billing Address", "paymentSuccessful": "Payment Successful", "paymentFailed": "Payment Failed", "refund": "Refund", "refundProcessed": "Refund Processed", "gst": "GST", "pst": "PST", "hst": "HST", "qst": "QST", "taxIncluded": "Tax Included", "taxExcluded": "Tax Excluded"}, "errors": {"genericError": "Something unexpected happened. Please try again.", "networkError": "Connection issue. Please check your internet connection.", "validationError": "Please check your information and try again.", "authError": "Authentication failed. Please sign in again.", "notFound": "The requested item could not be found.", "serverError": "Our servers are experiencing issues. Please try again later.", "permissionDenied": "You don't have permission to perform this action.", "sessionExpired": "Your session has expired. Please sign in again.", "fileUploadError": "Failed to upload file. Please try again.", "paymentError": "Payment processing failed. Please try again.", "bookingError": "Booking could not be completed. Please try again.", "serviceUnavailable": "This service is currently unavailable."}, "validation": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Please enter a valid phone number", "invalidPostalCode": "Please enter a valid postal code", "passwordTooShort": "Password must be at least 8 characters", "passwordsDoNotMatch": "Passwords do not match", "invalidDate": "Please select a valid date", "invalidTime": "Please select a valid time", "fileTooLarge": "File size is too large", "invalidFileType": "Invalid file type", "minimumAge": "You must be at least 18 years old", "invalidCreditCard": "Please enter a valid credit card number", "invalidCVV": "Please enter a valid CVV"}, "accessibility": {"closeModal": "Close modal", "openMenu": "Open menu", "closeMenu": "Close menu", "showPassword": "Show password", "hidePassword": "Hide password", "selectDate": "Select date", "selectTime": "Select time", "increaseQuantity": "Increase quantity", "decreaseQuantity": "Decrease quantity", "addToCart": "Add to cart", "removeFromCart": "Remove from cart", "playVideo": "Play video", "pauseVideo": "Pause video", "muteAudio": "Mute audio", "unmuteAudio": "Unmute audio", "expandSection": "Expand section", "collapseSection": "Collapse section", "sortAscending": "Sort ascending", "sortDescending": "Sort descending", "filterResults": "Filter results", "clearFilters": "Clear filters", "loadMore": "Load more items", "refreshContent": "Refresh content"}, "home": {"welcome": "Welcome to Vierla", "greeting": {"morning": "Good morning", "afternoon": "Good afternoon", "evening": "Good evening"}, "subtitle": "What service are you looking for today?", "searchPlaceholder": "Search services, providers, locations...", "quickActions": {"bookNow": "Book Now", "findNearby": "Find Nearby", "viewMessages": "Messages", "viewBookings": "My Bookings"}, "sections": {"browseServices": "Browse Services", "featuredProviders": "Featured Providers", "nearbyProviders": "Nearby Providers", "recentBookings": "Recent Bookings"}, "categories": {"hair": "Hair Services", "nails": "Nail Services", "lashes": "Lash Services", "braiding": "Braiding", "makeup": "Makeup", "skincare": "Skincare", "descriptions": {"hair": "Cuts, styling, coloring, and treatments", "nails": "Manicures, pedicures, nail art", "lashes": "Extensions, lifts, tinting", "braiding": "Protective styles and braiding"}}, "noProviders": "No providers available", "viewAll": "View All", "distance": "{{distance}} mi", "book": "Book"}, "bookings": {"title": "My Bookings", "filters": {"all": "All", "upcoming": "Upcoming", "completed": "Completed", "cancelled": "Cancelled"}, "emptyState": {"title": "No Bookings Found", "description": {"all": "You haven't made any bookings yet.", "filtered": "No {{filter}} bookings found."}, "action": "Book a Service"}, "status": {"confirmed": "Confirmed", "pending": "Pending", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled"}}, "search": {"title": "Search Services", "placeholder": "Search for services...", "suggestions": {"services": "{{query}} services", "location": "{{query}} near me", "popular": "best {{query}}", "price": "affordable {{query}}"}, "noResults": "No results found", "tryDifferent": "Try a different search term"}, "dates": {"today": "Today", "tomorrow": "Tomorrow", "yesterday": "Yesterday", "thisWeek": "This Week", "nextWeek": "Next Week", "thisMonth": "This Month", "nextMonth": "Next Month", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}}