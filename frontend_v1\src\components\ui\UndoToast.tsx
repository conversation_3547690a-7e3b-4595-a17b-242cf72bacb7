/**
 * Undo Toast Component
 * 
 * Toast notification component with undo functionality for destructive actions.
 * Provides users with a safety net to reverse accidental deletions and other
 * destructive operations within a time window.
 * 
 * Features:
 * - Countdown timer showing remaining undo time
 * - Accessible undo button with proper ARIA labels
 * - Haptic feedback for interactions
 * - Auto-dismiss when undo time expires
 * - Support for different action types (delete, archive, etc.)
 * - Dark mode support
 * - Smooth animations and micro-interactions
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  StyleSheet,
  Dimensions,
  AccessibilityInfo,
} from 'react-native';
import { NavigationIcon } from './IconLibrary';
import { useTheme } from '../../contexts/ThemeContext';
import { Colors } from '../../constants/Colors';

import { AccessibilityUtils } from '../../utils/accessibilityUtils';
import { undoService, UndoOperation } from '../../services/undoService';

const { width: screenWidth } = Dimensions.get('window');

// Component props
export interface UndoToastProps {
  operationId: string;
  visible: boolean;
  onDismiss: () => void;
  onUndo?: () => void;
  position?: 'top' | 'bottom';
  testID?: string;
}

export const UndoToast: React.FC<UndoToastProps> = ({
  operationId,
  visible,
  onDismiss,
  onUndo,
  position = 'bottom',
  testID = 'undo-toast',
}) => {
  const { isDark } = useTheme();
  const [operation, setOperation] = useState<UndoOperation | null>(null);
  const [remainingTime, setRemainingTime] = useState(0);
  const [isUndoing, setIsUndoing] = useState(false);

  // Animation values
  const translateY = useRef(new Animated.Value(position === 'bottom' ? 100 : -100)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const progressWidth = useRef(new Animated.Value(100)).current;

  // Update operation data
  useEffect(() => {
    if (operationId) {
      const op = undoService.getOperation(operationId);
      setOperation(op || null);
      
      if (op) {
        const remaining = undoService.getRemainingTime(operationId);
        setRemainingTime(remaining);
      }
    }
  }, [operationId]);

  // Handle visibility changes
  useEffect(() => {
    if (visible && operation) {
      showToast();
      startCountdown();
    } else {
      hideToast();
    }

    return () => {
      // Cleanup any running animations
      translateY.stopAnimation();
      opacity.stopAnimation();
      progressWidth.stopAnimation();
    };
  }, [visible, operation]);

  // Show toast animation
  const showToast = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    // Announce for screen readers
    if (operation) {
      AccessibilityUtils.ScreenReaderUtils.announceForAccessibility(
        `${operation.description}. Undo button available. ${Math.round(remainingTime / 1000)} seconds remaining.`
      );
    }
  };

  // Hide toast animation
  const hideToast = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: position === 'bottom' ? 100 : -100,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onDismiss();
    });
  };

  // Start countdown timer
  const startCountdown = () => {
    if (!operation) return;

    const totalTime = operation.expiresAt - operation.timestamp;
    const remaining = undoService.getRemainingTime(operationId);
    
    // Animate progress bar
    Animated.timing(progressWidth, {
      toValue: 0,
      duration: remaining,
      useNativeDriver: false,
    }).start();

    // Update remaining time every second
    const interval = setInterval(() => {
      const newRemaining = undoService.getRemainingTime(operationId);
      setRemainingTime(newRemaining);

      if (newRemaining <= 0) {
        clearInterval(interval);
        hideToast();
      }
    }, 1000);

    // Auto-dismiss when time expires
    setTimeout(() => {
      clearInterval(interval);
      if (visible) {
        hideToast();
      }
    }, remaining);
  };

  // Handle undo button press
  const handleUndo = async () => {
    if (!operation || isUndoing) return;

    setIsUndoing(true);
    // HapticPatterns.lightImpact();

    try {
      const success = await undoService.executeUndo(operationId);
      
      if (success) {
        // Announce success
        AccessibilityUtils.ScreenReaderUtils.announceForAccessibility('Undo successful');
        
        // Call optional callback
        onUndo?.();
        
        // Hide toast
        hideToast();
      } else {
        // Show error feedback
        // HapticPatterns.errorPress();
        AccessibilityUtils.ScreenReaderUtils.announceForAccessibility('Undo failed. Please try again.');
      }
    } catch (error) {
      console.error('[UndoToast] Undo failed:', error);
      // HapticPatterns.errorPress();
      AccessibilityUtils.ScreenReaderUtils.announceForAccessibility('Undo failed. Please try again.');
    } finally {
      setIsUndoing(false);
    }
  };

  // Handle dismiss button press
  const handleDismiss = () => {
    // HapticPatterns.lightImpact();
    undoService.cancelUndo(operationId);
    hideToast();
  };

  // Get action icon based on operation type
  const getActionIcon = () => {
    if (!operation) return 'trash-outline';
    
    switch (operation.type) {
      case 'delete':
        return 'trash-outline';
      case 'archive':
        return 'archive-outline';
      case 'remove':
        return 'remove-circle-outline';
      case 'clear':
        return 'refresh-outline';
      default:
        return 'undo-outline';
    }
  };

  // Format remaining time
  const formatTime = (ms: number): string => {
    const seconds = Math.ceil(ms / 1000);
    return `${seconds}s`;
  };

  if (!visible || !operation) {
    return null;
  }

  const styles = createStyles(isDark);

  return (
    <Animated.View
      style={[
        styles.container,
        position === 'top' ? styles.topPosition : styles.bottomPosition,
        {
          transform: [{ translateY }],
          opacity,
        },
      ]}
      testID={testID}
    >
      {/* Progress bar */}
      <View style={styles.progressContainer}>
        <Animated.View
          style={[
            styles.progressBar,
            {
              width: progressWidth.interpolate({
                inputRange: [0, 100],
                outputRange: ['0%', '100%'],
              }),
            },
          ]}
        />
      </View>

      <View style={styles.content}>
        {/* Action icon */}
        <View style={styles.iconContainer}>
          <NavigationIcon
            name={getActionIcon()}
            size={20}
            color={Colors.status.warning}
          />
        </View>

        {/* Message */}
        <View style={styles.messageContainer}>
          <Text style={styles.message} numberOfLines={2}>
            {operation.description}
          </Text>
          <Text style={styles.timer}>
            {formatTime(remainingTime)} remaining
          </Text>
        </View>

        {/* Action buttons */}
        <View style={styles.actions}>
          <TouchableOpacity
            style={[styles.button, styles.undoButton]}
            onPress={handleUndo}
            disabled={isUndoing}
            accessibilityRole="button"
            accessibilityLabel={`Undo ${operation.description}`}
            accessibilityHint="Tap to reverse this action"
            testID={`${testID}-undo-button`}
          >
            <Text style={styles.undoButtonText}>
              {isUndoing ? 'Undoing...' : 'Undo'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.dismissButton]}
            onPress={handleDismiss}
            accessibilityRole="button"
            accessibilityLabel="Dismiss undo notification"
            accessibilityHint="Tap to dismiss this notification"
            testID={`${testID}-dismiss-button`}
          >
            <NavigationIcon
              name="close"
              size={16}
              color={isDark ? Colors.text.secondary : Colors.text.primary}
            />
          </TouchableOpacity>
        </View>
      </View>
    </Animated.View>
  );
};

// Styles
const createStyles = (isDark: boolean) =>
  StyleSheet.create({
    container: {
      position: 'absolute',
      left: 16,
      right: 16,
      backgroundColor: isDark ? Colors.background.secondary : Colors.background.primary,
      borderRadius: 12,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 8,
      overflow: 'hidden',
    },
    topPosition: {
      top: 60, // Account for status bar and safe area
    },
    bottomPosition: {
      bottom: 100, // Account for tab bar and safe area
    },
    progressContainer: {
      height: 3,
      backgroundColor: isDark ? Colors.border.secondary : Colors.border.primary,
    },
    progressBar: {
      height: '100%',
      backgroundColor: Colors.status.warning,
    },
    content: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
    },
    iconContainer: {
      marginRight: 12,
    },
    messageContainer: {
      flex: 1,
      marginRight: 12,
    },
    message: {
      fontSize: 14,
      fontWeight: '500',
      color: isDark ? Colors.text.primary : Colors.text.primary,
      marginBottom: 2,
    },
    timer: {
      fontSize: 12,
      color: isDark ? Colors.text.secondary : Colors.text.secondary,
    },
    actions: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    button: {
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 6,
      marginLeft: 8,
    },
    undoButton: {
      backgroundColor: Colors.primary.sage,
    },
    undoButtonText: {
      fontSize: 14,
      fontWeight: '600',
      color: Colors.text.inverse,
    },
    dismissButton: {
      backgroundColor: 'transparent',
    },
  });

export default UndoToast;
