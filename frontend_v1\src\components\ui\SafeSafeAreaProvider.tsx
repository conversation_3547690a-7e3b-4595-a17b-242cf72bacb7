/**
 * Safe SafeAreaProvider Wrapper
 * 
 * Provides a safer implementation of SafeAreaProvider that handles
 * undefined status property errors and provides fallback values.
 * 
 * This component addresses the "Cannot read property 'status' of undefined"
 * error that occurs with react-native-safe-area-context in certain environments.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { ReactNode, useEffect, useState } from 'react';
import { View, Platform, StatusBar, Dimensions } from 'react-native';
import { SafeAreaProvider, SafeAreaContext } from 'react-native-safe-area-context';

interface SafeSafeAreaProviderProps {
  children: ReactNode;
}

// Fallback safe area insets
const getFallbackInsets = () => {
  const { height, width } = Dimensions.get('window');
  
  return {
    top: Platform.select({
      ios: height >= 812 ? 44 : 20, // iPhone X+ vs older iPhones
      android: StatusBar.currentHeight || 24,
      default: 0,
    }),
    bottom: Platform.select({
      ios: height >= 812 ? 34 : 0, // iPhone X+ home indicator
      android: 0,
      default: 0,
    }),
    left: 0,
    right: 0,
  };
};

// Safe context value with fallbacks
const createSafeContextValue = () => {
  const insets = getFallbackInsets();
  
  return {
    insets,
    frame: {
      x: 0,
      y: 0,
      width: Dimensions.get('window').width,
      height: Dimensions.get('window').height,
    },
  };
};

export const SafeSafeAreaProvider: React.FC<SafeSafeAreaProviderProps> = ({ children }) => {
  const [hasError, setHasError] = useState(false);
  const [fallbackContext] = useState(createSafeContextValue);

  useEffect(() => {
    // Test if SafeAreaProvider works without errors
    const testSafeArea = () => {
      try {
        // This will trigger the error if it exists
        const testElement = React.createElement(SafeAreaProvider, {}, null);
        setHasError(false);
      } catch (error) {
        console.warn('[SafeSafeAreaProvider] SafeAreaProvider error detected, using fallback:', error);
        setHasError(true);
      }
    };

    testSafeArea();
  }, []);

  // If SafeAreaProvider has errors, use fallback implementation
  if (hasError) {
    console.log('[SafeSafeAreaProvider] Using fallback safe area implementation');
    
    return (
      <SafeAreaContext.Provider value={fallbackContext}>
        <View style={{ flex: 1 }}>
          {children}
        </View>
      </SafeAreaContext.Provider>
    );
  }

  // Try to use the normal SafeAreaProvider with error boundary
  return (
    <SafeAreaProviderErrorBoundary fallback={fallbackContext}>
      <SafeAreaProvider>
        {children}
      </SafeAreaProvider>
    </SafeAreaProviderErrorBoundary>
  );
};

// Error boundary specifically for SafeAreaProvider
interface SafeAreaProviderErrorBoundaryProps {
  children: ReactNode;
  fallback: any;
}

interface SafeAreaProviderErrorBoundaryState {
  hasError: boolean;
}

class SafeAreaProviderErrorBoundary extends React.Component<
  SafeAreaProviderErrorBoundaryProps,
  SafeAreaProviderErrorBoundaryState
> {
  constructor(props: SafeAreaProviderErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): SafeAreaProviderErrorBoundaryState {
    // Check if this is the specific status property error
    if (error.message.includes("Cannot read property 'status' of undefined")) {
      console.warn('[SafeAreaProviderErrorBoundary] Status property error caught, using fallback');
      return { hasError: true };
    }
    
    // For other errors, let them bubble up
    throw error;
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    if (error.message.includes("Cannot read property 'status' of undefined")) {
      console.warn('[SafeAreaProviderErrorBoundary] SafeAreaProvider status error:', error);
      console.warn('[SafeAreaProviderErrorBoundary] Error info:', errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      // Render fallback UI
      return (
        <SafeAreaContext.Provider value={this.props.fallback}>
          <View style={{ flex: 1 }}>
            {this.props.children}
          </View>
        </SafeAreaContext.Provider>
      );
    }

    return this.props.children;
  }
}

// Enhanced hook that provides safe area insets with fallbacks
export const useSafeSafeAreaInsets = () => {
  try {
    // Try to use the normal hook
    const { useSafeAreaInsets } = require('react-native-safe-area-context');
    const insets = useSafeAreaInsets();
    
    // Validate that insets are reasonable
    if (insets && typeof insets.top === 'number' && typeof insets.bottom === 'number') {
      return insets;
    }
    
    // If insets are invalid, use fallback
    console.warn('[useSafeSafeAreaInsets] Invalid insets detected, using fallback');
    return getFallbackInsets();
    
  } catch (error) {
    console.warn('[useSafeSafeAreaInsets] Error getting safe area insets, using fallback:', error);
    return getFallbackInsets();
  }
};

// Utility to get safe area insets without hooks (for use in non-component contexts)
export const getSafeSafeAreaInsets = () => {
  return getFallbackInsets();
};

export default SafeSafeAreaProvider;
