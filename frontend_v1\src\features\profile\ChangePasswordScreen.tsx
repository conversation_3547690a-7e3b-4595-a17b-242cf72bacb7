/**
 * Change Password Screen - Secure Password Management
 *
 * Component Contract:
 * - Allows authenticated users to change their password
 * - Validates current password before allowing change
 * - Enforces strong password requirements
 * - Provides secure password input with visibility toggle
 * - Handles password change API integration
 * - Follows TDD methodology with comprehensive test coverage
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaWrapper } from '../../components/ui/SafeAreaWrapper';
import { useAuthStore } from '../../store/authSlice';
import { authService, ChangePasswordRequest } from '../../services/authService';
import { useTheme } from '../../contexts/ThemeContext';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsiveUtils';
import { createStyles } from './ChangePasswordScreen.styles';

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface PasswordValidation {
  minLength: boolean;
  hasUppercase: boolean;
  hasLowercase: boolean;
  hasNumber: boolean;
  hasSpecialChar: boolean;
}

export const ChangePasswordScreen: React.FC = () => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const { authToken } = useAuthStore();

  const [formData, setFormData] = useState<PasswordFormData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<PasswordFormData>>({});
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const validatePassword = (password: string): PasswordValidation => {
    return {
      minLength: password.length >= 8,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumber: /\d/.test(password),
      hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    };
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<PasswordFormData> = {};

    if (!formData.currentPassword) {
      newErrors.currentPassword = 'Current password is required';
    }

    if (!formData.newPassword) {
      newErrors.newPassword = 'New password is required';
    } else {
      const validation = validatePassword(formData.newPassword);
      if (!Object.values(validation).every(Boolean)) {
        newErrors.newPassword = 'Password does not meet requirements';
      }
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your new password';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    if (formData.currentPassword === formData.newPassword) {
      newErrors.newPassword = 'New password must be different from current password';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChangePassword = async () => {
    if (!validateForm() || !authToken) {
      return;
    }

    setIsLoading(true);
    try {
      const changeData: ChangePasswordRequest = {
        current_password: formData.currentPassword,
        new_password: formData.newPassword,
      };

      await authService.changePassword(changeData, authToken);
      
      Alert.alert(
        'Success',
        'Password changed successfully',
        [{ text: 'OK', onPress: () => {
          // Reset form
          setFormData({
            currentPassword: '',
            newPassword: '',
            confirmPassword: '',
          });
          setErrors({});
        }}]
      );
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to change password');
    } finally {
      setIsLoading(false);
    }
  };

  const renderPasswordField = (
    label: string,
    value: string,
    onChangeText: (text: string) => void,
    error?: string,
    showPassword: boolean = false,
    onToggleVisibility?: () => void,
    testID?: string
  ) => (
    <View style={styles.fieldContainer}>
      <Text style={styles.fieldLabel}>{label}</Text>
      <View style={styles.passwordInputContainer}>
        <TextInput
          style={[styles.passwordInput, error && styles.fieldInputError]}
          value={value}
          onChangeText={onChangeText}
          secureTextEntry={!showPassword}
          autoCapitalize="none"
          autoCorrect={false}
          testID={testID}
        />
        {onToggleVisibility && (
          <TouchableOpacity
            style={styles.visibilityToggle}
            onPress={onToggleVisibility}
            testID={`${testID}-visibility-toggle`}
          >
            <Text style={styles.visibilityToggleText}>
              {showPassword ? 'Hide' : 'Show'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );

  const renderPasswordRequirements = () => {
    const validation = validatePassword(formData.newPassword);
    
    return (
      <View style={styles.requirementsContainer}>
        <Text style={styles.requirementsTitle}>Password Requirements:</Text>
        {Object.entries({
          'At least 8 characters': validation.minLength,
          'One uppercase letter': validation.hasUppercase,
          'One lowercase letter': validation.hasLowercase,
          'One number': validation.hasNumber,
          'One special character': validation.hasSpecialChar,
        }).map(([requirement, met]) => (
          <View key={requirement} style={styles.requirementItem}>
            <Text style={[
              styles.requirementText,
              met && styles.requirementMet
            ]}>
              {met ? '✓' : '○'} {requirement}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  return (
    <SafeAreaWrapper style={styles.container} testID="change-password-screen">
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Change Password</Text>
          <Text style={styles.subtitle}>
            Enter your current password and choose a new secure password
          </Text>
        </View>

        <View style={styles.formContainer}>
          {renderPasswordField(
            'Current Password',
            formData.currentPassword,
            (text) => setFormData(prev => ({ ...prev, currentPassword: text })),
            errors.currentPassword,
            showPasswords.current,
            () => setShowPasswords(prev => ({ ...prev, current: !prev.current })),
            'current-password-input'
          )}

          {renderPasswordField(
            'New Password',
            formData.newPassword,
            (text) => setFormData(prev => ({ ...prev, newPassword: text })),
            errors.newPassword,
            showPasswords.new,
            () => setShowPasswords(prev => ({ ...prev, new: !prev.new })),
            'new-password-input'
          )}

          {formData.newPassword.length > 0 && renderPasswordRequirements()}

          {renderPasswordField(
            'Confirm New Password',
            formData.confirmPassword,
            (text) => setFormData(prev => ({ ...prev, confirmPassword: text })),
            errors.confirmPassword,
            showPasswords.confirm,
            () => setShowPasswords(prev => ({ ...prev, confirm: !prev.confirm })),
            'confirm-password-input'
          )}

          <TouchableOpacity
            style={[styles.changeButton, isLoading && styles.changeButtonDisabled]}
            onPress={handleChangePassword}
            disabled={isLoading}
            testID="change-password-button"
          >
            {isLoading ? (
              <ActivityIndicator size="small" color={colors.white} />
            ) : (
              <Text style={styles.changeButtonText}>Change Password</Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaWrapper>
  );
};
