/**
 * Internationalization Context
 * 
 * React context for managing internationalization state and providing
 * translation functions throughout the application.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Text } from 'react-native';
import {
  SupportedLocale,
  initializeI18n,
  setLocale as setI18nLocale,
  getCurrentLocale,
  onLocaleChange,
  t,
  tp,
  formatCurrency,
  formatDate,
  formatTime,
  formatPhoneNumber,
  formatPostalCode,
  getProvinces,
  getAvailableLocales,
  isFrenchCanadian,
} from '../utils/i18n';

// I18n context interface
interface I18nContextType {
  // Current locale state
  locale: SupportedLocale;
  isLoading: boolean;
  
  // Translation functions
  t: typeof t;
  tp: typeof tp;
  
  // Formatting functions
  formatCurrency: typeof formatCurrency;
  formatDate: typeof formatDate;
  formatTime: typeof formatTime;
  formatPhoneNumber: typeof formatPhoneNumber;
  formatPostalCode: typeof formatPostalCode;
  
  // Locale management
  setLocale: (locale: SupportedLocale) => Promise<void>;
  getAvailableLocales: typeof getAvailableLocales;
  getProvinces: typeof getProvinces;
  
  // Utility functions
  isFrenchCanadian: typeof isFrenchCanadian;
  isRTL: boolean; // For future RTL support
}

// Create context
const I18nContext = createContext<I18nContextType | undefined>(undefined);

// Provider props
interface I18nProviderProps {
  children: ReactNode;
  fallbackLocale?: SupportedLocale;
}

/**
 * I18n Provider Component
 */
export const I18nProvider: React.FC<I18nProviderProps> = ({
  children,
  fallbackLocale = 'en-CA',
}) => {
  const [locale, setLocaleState] = useState<SupportedLocale>(fallbackLocale);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize i18n system
  useEffect(() => {
    const initialize = async () => {
      try {
        const initialLocale = await initializeI18n();
        setLocaleState(initialLocale);
      } catch (error) {
        console.error('Failed to initialize i18n:', error);
        setLocaleState(fallbackLocale);
      } finally {
        setIsLoading(false);
      }
    };

    initialize();
  }, [fallbackLocale]);

  // Listen for locale changes
  useEffect(() => {
    const unsubscribe = onLocaleChange((newLocale) => {
      setLocaleState(newLocale);
    });

    return unsubscribe;
  }, []);

  // Handle locale change
  const handleSetLocale = async (newLocale: SupportedLocale) => {
    try {
      await setI18nLocale(newLocale);
      // State will be updated via the locale change listener
    } catch (error) {
      console.error('Failed to set locale:', error);
    }
  };

  // Context value
  const contextValue: I18nContextType = {
    locale,
    isLoading,
    t,
    tp,
    formatCurrency,
    formatDate,
    formatTime,
    formatPhoneNumber,
    formatPostalCode,
    setLocale: handleSetLocale,
    getAvailableLocales,
    getProvinces,
    isFrenchCanadian,
    isRTL: false, // Future RTL support
  };

  return (
    <I18nContext.Provider value={contextValue}>
      {children}
    </I18nContext.Provider>
  );
};

/**
 * Hook to use i18n context
 */
export const useI18n = (): I18nContextType => {
  const context = useContext(I18nContext);
  
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  
  return context;
};

/**
 * Hook for translation with automatic re-rendering on locale change
 */
export const useTranslation = () => {
  const { t, tp, locale } = useI18n();
  
  return {
    t,
    tp,
    locale,
    // Convenience function for common translations
    common: {
      loading: () => t('common.loading'),
      error: () => t('common.error'),
      success: () => t('common.success'),
      cancel: () => t('common.cancel'),
      confirm: () => t('common.confirm'),
      save: () => t('common.save'),
      edit: () => t('common.edit'),
      delete: () => t('common.delete'),
      back: () => t('common.back'),
      next: () => t('common.next'),
      close: () => t('common.close'),
      ok: () => t('common.ok'),
      yes: () => t('common.yes'),
      no: () => t('common.no'),
    },
    navigation: {
      home: () => t('navigation.home'),
      services: () => t('navigation.services'),
      bookings: () => t('navigation.bookings'),
      messages: () => t('navigation.messages'),
      profile: () => t('navigation.profile'),
      settings: () => t('navigation.settings'),
      help: () => t('navigation.help'),
      about: () => t('navigation.about'),
    },
    auth: {
      signIn: () => t('auth.signIn'),
      signUp: () => t('auth.signUp'),
      signOut: () => t('auth.signOut'),
      email: () => t('auth.email'),
      password: () => t('auth.password'),
      forgotPassword: () => t('auth.forgotPassword'),
    },
  };
};

/**
 * Hook for formatting functions
 */
export const useFormatting = () => {
  const {
    formatCurrency,
    formatDate,
    formatTime,
    formatPhoneNumber,
    formatPostalCode,
    locale,
  } = useI18n();
  
  return {
    currency: formatCurrency,
    date: formatDate,
    time: formatTime,
    phone: formatPhoneNumber,
    postalCode: formatPostalCode,
    locale,
  };
};

/**
 * Hook for locale management
 */
export const useLocale = () => {
  const {
    locale,
    setLocale,
    getAvailableLocales,
    isFrenchCanadian,
    isRTL,
  } = useI18n();
  
  return {
    locale,
    setLocale,
    availableLocales: getAvailableLocales(),
    isFrench: isFrenchCanadian(),
    isEnglish: locale === 'en-CA',
    isRTL,
  };
};

/**
 * Higher-order component for i18n
 */
export function withI18n<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  return function I18nComponent(props: P) {
    const i18n = useI18n();
    
    return <Component {...props} i18n={i18n} />;
  };
}

/**
 * Component for conditional rendering based on locale
 */
export const LocaleSwitch: React.FC<{
  en?: ReactNode;
  fr?: ReactNode;
  children?: ReactNode;
}> = ({ en, fr, children }) => {
  const { isFrenchCanadian } = useI18n();
  
  if (isFrenchCanadian()) {
    return <>{fr || children}</>;
  } else {
    return <>{en || children}</>;
  }
};

/**
 * Component for displaying localized text
 */
export const LocalizedText: React.FC<{
  i18nKey: string;
  params?: Record<string, string | number>;
  fallback?: string;
  style?: any;
  numberOfLines?: number;
}> = ({ i18nKey, params, fallback, ...textProps }) => {
  const { t } = useI18n();
  
  const text = t(i18nKey, params);
  const displayText = text.startsWith('[Missing:') || text.startsWith('[Invalid:') 
    ? (fallback || i18nKey) 
    : text;
  
  return (
    <Text {...textProps}>
      {displayText}
    </Text>
  );
};

// Re-export types and utilities
export type { SupportedLocale };
export { getCurrentLocale, getAvailableLocales };
