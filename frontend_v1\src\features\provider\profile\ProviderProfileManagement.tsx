/**
 * Provider Profile Management - Profile Management Component
 * 
 * Phase 1 MVP Implementation:
 * - Basic profile editing (bio, photo, primary skills)
 * - Contact information management
 * - Service offerings management
 * - Profile completion tracking
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  Alert,
  Image,
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import { useI18n } from '../../../contexts/I18nContext';
import { Card } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/Button';
import { Icon } from '../../../components/ui/Icon';
import { TextInput } from '../../../components/ui/TextInput';

// Profile interfaces
interface ProviderProfile {
  id: string;
  businessName: string;
  bio: string;
  profilePhoto?: string;
  contactInfo: {
    phone: string;
    email: string;
    website?: string;
  };
  location: {
    address: string;
    city: string;
    province: string;
    postalCode: string;
  };
  services: Service[];
  skills: string[];
  certifications: Certification[];
  completionPercentage: number;
}

interface Service {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number;
  isActive: boolean;
}

interface Certification {
  id: string;
  name: string;
  issuer: string;
  dateObtained: string;
  expiryDate?: string;
  documentUrl?: string;
}

export const ProviderProfileManagement: React.FC = () => {
  const { colors } = useTheme();
  const { t } = useI18n();
  
  // State
  const [profile, setProfile] = useState<ProviderProfile | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Load profile data
  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setIsLoading(true);
      
      // Mock data for Phase 1 MVP
      const mockProfile: ProviderProfile = {
        id: '1',
        businessName: 'Elite Cleaning Services',
        bio: 'Professional cleaning service with over 5 years of experience. We provide reliable, thorough, and eco-friendly cleaning solutions for homes and offices.',
        profilePhoto: 'https://via.placeholder.com/150',
        contactInfo: {
          phone: '+****************',
          email: '<EMAIL>',
          website: 'https://elitecleaning.com',
        },
        location: {
          address: '123 Main Street',
          city: 'Toronto',
          province: 'Ontario',
          postalCode: 'M5V 3A8',
        },
        services: [
          {
            id: '1',
            name: 'House Cleaning',
            description: 'Complete house cleaning including all rooms',
            price: 85,
            duration: 180,
            isActive: true,
          },
          {
            id: '2',
            name: 'Office Cleaning',
            description: 'Professional office cleaning services',
            price: 120,
            duration: 240,
            isActive: true,
          },
          {
            id: '3',
            name: 'Deep Cleaning',
            description: 'Thorough deep cleaning service',
            price: 150,
            duration: 300,
            isActive: true,
          },
        ],
        skills: ['Residential Cleaning', 'Commercial Cleaning', 'Eco-Friendly Products', 'Deep Cleaning'],
        certifications: [
          {
            id: '1',
            name: 'Professional Cleaning Certification',
            issuer: 'Canadian Cleaning Association',
            dateObtained: '2023-01-15',
            expiryDate: '2026-01-15',
          },
        ],
        completionPercentage: 85,
      };

      setProfile(mockProfile);
    } catch (error) {
      console.error('Error loading profile:', error);
      Alert.alert(
        t('common.error'),
        t('provider.profile.loadError')
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveProfile = async () => {
    if (!profile) return;

    try {
      setIsSaving(true);
      
      // Save profile changes
      console.log('Saving profile:', profile);
      
      Alert.alert(
        t('common.success'),
        t('provider.profile.saveSuccess')
      );
      
      setHasChanges(false);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving profile:', error);
      Alert.alert(
        t('common.error'),
        t('provider.profile.saveError')
      );
    } finally {
      setIsSaving(false);
    }
  };

  const handleProfileChange = (field: string, value: any) => {
    if (!profile) return;

    setProfile(prev => ({
      ...prev!,
      [field]: value,
    }));
    setHasChanges(true);
  };

  const handleContactInfoChange = (field: string, value: string) => {
    if (!profile) return;

    setProfile(prev => ({
      ...prev!,
      contactInfo: {
        ...prev!.contactInfo,
        [field]: value,
      },
    }));
    setHasChanges(true);
  };

  const handleLocationChange = (field: string, value: string) => {
    if (!profile) return;

    setProfile(prev => ({
      ...prev!,
      location: {
        ...prev!.location,
        [field]: value,
      },
    }));
    setHasChanges(true);
  };

  const handleAddSkill = () => {
    Alert.prompt(
      t('provider.profile.addSkill'),
      t('provider.profile.addSkillMessage'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.add'),
          onPress: (skill) => {
            if (skill && profile) {
              setProfile(prev => ({
                ...prev!,
                skills: [...prev!.skills, skill],
              }));
              setHasChanges(true);
            }
          },
        },
      ]
    );
  };

  const handleRemoveSkill = (skillToRemove: string) => {
    if (!profile) return;

    setProfile(prev => ({
      ...prev!,
      skills: prev!.skills.filter(skill => skill !== skillToRemove),
    }));
    setHasChanges(true);
  };

  const renderCompletionProgress = () => {
    if (!profile) return null;

    return (
      <Card style={styles.completionCard}>
        <View style={styles.completionHeader}>
          <Text style={[styles.completionTitle, { color: colors.text.primary }]}>
            {t('provider.profile.profileCompletion')}
          </Text>
          <Text style={[styles.completionPercentage, { color: colors.primary[500] }]}>
            {profile.completionPercentage}%
          </Text>
        </View>
        <View style={[styles.progressBar, { backgroundColor: colors.gray[200] }]}>
          <View 
            style={[
              styles.progressFill,
              { 
                backgroundColor: colors.primary[500],
                width: `${profile.completionPercentage}%`,
              }
            ]}
          />
        </View>
        <Text style={[styles.completionMessage, { color: colors.text.secondary }]}>
          {t('provider.profile.completionMessage')}
        </Text>
      </Card>
    );
  };

  const renderProfilePhoto = () => (
    <Card style={styles.photoCard}>
      <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
        {t('provider.profile.profilePhoto')}
      </Text>
      <View style={styles.photoContainer}>
        {profile?.profilePhoto ? (
          <Image 
            source={{ uri: profile.profilePhoto }} 
            style={styles.profileImage}
          />
        ) : (
          <View style={[styles.photoPlaceholder, { backgroundColor: colors.gray[200] }]}>
            <Icon name="camera" size={32} color={colors.text.secondary} />
          </View>
        )}
        <Button
          title={t('provider.profile.changePhoto')}
          variant="outline"
          size="small"
          onPress={() => console.log('Change photo')}
          style={styles.changePhotoButton}
        />
      </View>
    </Card>
  );

  const renderBasicInfo = () => (
    <Card style={styles.infoCard}>
      <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
        {t('provider.profile.basicInfo')}
      </Text>
      
      <TextInput
        label={t('provider.profile.businessName')}
        value={profile?.businessName || ''}
        onChangeText={(value) => handleProfileChange('businessName', value)}
        editable={isEditing}
        style={styles.input}
      />
      
      <TextInput
        label={t('provider.profile.bio')}
        value={profile?.bio || ''}
        onChangeText={(value) => handleProfileChange('bio', value)}
        editable={isEditing}
        multiline
        numberOfLines={4}
        style={styles.input}
      />
    </Card>
  );

  const renderContactInfo = () => (
    <Card style={styles.infoCard}>
      <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
        {t('provider.profile.contactInfo')}
      </Text>
      
      <TextInput
        label={t('provider.profile.phone')}
        value={profile?.contactInfo.phone || ''}
        onChangeText={(value) => handleContactInfoChange('phone', value)}
        editable={isEditing}
        keyboardType="phone-pad"
        style={styles.input}
      />
      
      <TextInput
        label={t('provider.profile.email')}
        value={profile?.contactInfo.email || ''}
        onChangeText={(value) => handleContactInfoChange('email', value)}
        editable={isEditing}
        keyboardType="email-address"
        style={styles.input}
      />
      
      <TextInput
        label={t('provider.profile.website')}
        value={profile?.contactInfo.website || ''}
        onChangeText={(value) => handleContactInfoChange('website', value)}
        editable={isEditing}
        keyboardType="url"
        style={styles.input}
      />
    </Card>
  );

  const renderSkills = () => (
    <Card style={styles.infoCard}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
          {t('provider.profile.skills')}
        </Text>
        {isEditing && (
          <Button
            title={t('provider.profile.addSkill')}
            variant="outline"
            size="small"
            onPress={handleAddSkill}
          />
        )}
      </View>
      
      <View style={styles.skillsContainer}>
        {profile?.skills.map((skill, index) => (
          <View 
            key={index}
            style={[styles.skillChip, { backgroundColor: colors.primary[100] }]}
          >
            <Text style={[styles.skillText, { color: colors.primary[700] }]}>
              {skill}
            </Text>
            {isEditing && (
              <TouchableOpacity
                onPress={() => handleRemoveSkill(skill)}
                style={styles.removeSkillButton}
              >
                <Icon name="x" size={16} color={colors.primary[700]} />
              </TouchableOpacity>
            )}
          </View>
        ))}
      </View>
    </Card>
  );

  const renderServices = () => (
    <Card style={styles.infoCard}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
          {t('provider.profile.services')}
        </Text>
        {isEditing && (
          <Button
            title={t('provider.profile.addService')}
            variant="outline"
            size="small"
            onPress={() => console.log('Add service')}
          />
        )}
      </View>
      
      {profile?.services.map((service) => (
        <View key={service.id} style={styles.serviceItem}>
          <View style={styles.serviceInfo}>
            <Text style={[styles.serviceName, { color: colors.text.primary }]}>
              {service.name}
            </Text>
            <Text style={[styles.serviceDescription, { color: colors.text.secondary }]}>
              {service.description}
            </Text>
            <Text style={[styles.servicePrice, { color: colors.success[500] }]}>
              ${service.price} • {service.duration} min
            </Text>
          </View>
          {isEditing && (
            <TouchableOpacity
              onPress={() => console.log('Edit service:', service.id)}
              style={styles.editServiceButton}
            >
              <Icon name="edit-2" size={20} color={colors.primary[500]} />
            </TouchableOpacity>
          )}
        </View>
      ))}
    </Card>
  );

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
          {t('provider.profile.loading')}
        </Text>
      </View>
    );
  }

  if (!profile) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={[styles.errorText, { color: colors.error[500] }]}>
          {t('provider.profile.loadError')}
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text.primary }]}>
          {t('provider.profile.title')}
        </Text>
        <View style={styles.headerActions}>
          {isEditing ? (
            <>
              <Button
                title={t('common.cancel')}
                variant="outline"
                size="small"
                onPress={() => {
                  setIsEditing(false);
                  setHasChanges(false);
                  loadProfile(); // Reload original data
                }}
                style={styles.headerButton}
              />
              <Button
                title={t('common.save')}
                variant="primary"
                size="small"
                onPress={handleSaveProfile}
                loading={isSaving}
                disabled={!hasChanges}
                style={styles.headerButton}
              />
            </>
          ) : (
            <Button
              title={t('common.edit')}
              variant="primary"
              size="small"
              onPress={() => setIsEditing(true)}
            />
          )}
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCompletionProgress()}
        {renderProfilePhoto()}
        {renderBasicInfo()}
        {renderContactInfo()}
        {renderSkills()}
        {renderServices()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  errorText: {
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerButton: {
    marginLeft: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  completionCard: {
    padding: 16,
    marginBottom: 16,
  },
  completionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  completionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  completionPercentage: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  completionMessage: {
    fontSize: 14,
  },
  photoCard: {
    padding: 16,
    marginBottom: 16,
  },
  photoContainer: {
    alignItems: 'center',
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 16,
  },
  photoPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  changePhotoButton: {
    alignSelf: 'center',
  },
  infoCard: {
    padding: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  input: {
    marginBottom: 16,
  },
  skillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  skillChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  skillText: {
    fontSize: 14,
    fontWeight: '500',
  },
  removeSkillButton: {
    marginLeft: 8,
  },
  serviceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: 14,
    marginBottom: 4,
  },
  servicePrice: {
    fontSize: 14,
    fontWeight: '500',
  },
  editServiceButton: {
    padding: 8,
  },
});
