/**
 * Accessible Image Component - REC-ACC-005 Implementation
 *
 * Enhanced image component that automatically provides appropriate alt-text
 * and accessibility properties for all informational images.
 * Implements WCAG 2.2 AA compliance for image accessibility.
 *
 * Features:
 * - Automatic alt-text generation based on context
 * - Support for decorative, informative, and functional images
 * - WCAG-compliant accessibility properties
 * - Screen reader optimization
 * - Context-aware descriptions
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import { Image, ImageProps, TouchableOpacity, View } from 'react-native';
import { ImageAccessibilityUtils } from '../../utils/accessibilityUtils';

export type ImageType = 'decorative' | 'informative' | 'functional' | 'logo' | 'avatar' | 'service' | 'store' | 'icon';

export interface AccessibleImageProps extends Omit<ImageProps, 'accessibilityLabel' | 'accessibilityRole'> {
  /** Type of image for appropriate accessibility handling */
  type: ImageType;
  /** Entity name for context-aware alt-text generation */
  entityName?: string;
  /** Action description for functional images */
  action?: string;
  /** Custom alt-text (overrides automatic generation) */
  altText?: string;
  /** Whether the image is interactive/clickable */
  interactive?: boolean;
  /** Press handler for interactive images */
  onPress?: () => void;
  /** Additional accessibility hint */
  accessibilityHint?: string;
  /** Test ID for testing */
  testID?: string;
}

export const AccessibleImage: React.FC<AccessibleImageProps> = ({
  type,
  entityName,
  action,
  altText,
  interactive = false,
  onPress,
  accessibilityHint,
  testID,
  style,
  ...imageProps
}) => {
  // Generate appropriate accessibility properties
  const getAccessibilityProps = () => {
    // Use custom alt-text if provided
    if (altText) {
      return {
        accessibilityLabel: altText,
        accessibilityRole: interactive ? 'button' : 'image',
        accessible: true,
        importantForAccessibility: 'yes' as const,
        accessibilityHint: interactive && action ? `Double tap to ${action}` : accessibilityHint,
      };
    }

    // Generate accessibility props based on image type
    switch (type) {
      case 'decorative':
        return {
          ...ImageAccessibilityUtils.getDecorative(),
          accessibilityElementsHidden: true,
          importantForAccessibility: 'no-hide-descendants' as const,
        };

      case 'informative':
      case 'logo':
      case 'avatar':
      case 'service':
      case 'store':
        const description = ImageAccessibilityUtils.generateAltText(type, entityName, action);
        return {
          ...ImageAccessibilityUtils.getInformative(description),
          accessibilityLabel: description,
          accessibilityRole: 'image' as const,
          importantForAccessibility: 'yes' as const,
        };

      case 'functional':
      case 'icon':
        const functionalDescription = ImageAccessibilityUtils.generateAltText(type, entityName, action);
        return {
          ...ImageAccessibilityUtils.getFunctional(functionalDescription, action),
          accessibilityLabel: functionalDescription,
          accessibilityRole: interactive ? 'imagebutton' : 'image',
          accessibilityHint: interactive && action ? `Double tap to ${action}` : undefined,
          importantForAccessibility: 'yes' as const,
        };

      default:
        return {
          ...ImageAccessibilityUtils.getInformative(entityName || 'Image'),
          accessibilityLabel: entityName || 'Image',
          accessibilityRole: 'image' as const,
          importantForAccessibility: 'yes' as const,
        };
    }
  };

  const accessibilityProps = getAccessibilityProps();

  // Render interactive image with TouchableOpacity
  if (interactive && onPress) {
    return (
      <TouchableOpacity
        onPress={onPress}
        style={style}
        testID={testID}
        {...accessibilityProps}
      >
        <Image
          {...imageProps}
          style={{ width: '100%', height: '100%' }}
          accessible={false} // Parent TouchableOpacity handles accessibility
        />
      </TouchableOpacity>
    );
  }

  // Render non-interactive image
  return (
    <View style={style} testID={testID}>
      <Image
        {...imageProps}
        style={{ width: '100%', height: '100%' }}
        {...accessibilityProps}
      />
    </View>
  );
};

/**
 * Specialized accessible image components for common use cases
 */

// Logo component with automatic accessibility
export const AccessibleLogo: React.FC<Omit<AccessibleImageProps, 'type'>> = (props) => (
  <AccessibleImage {...props} type="logo" />
);

// Avatar component with automatic accessibility
export const AccessibleAvatar: React.FC<Omit<AccessibleImageProps, 'type'>> = (props) => (
  <AccessibleImage {...props} type="avatar" />
);

// Service image component with automatic accessibility
export const AccessibleServiceImage: React.FC<Omit<AccessibleImageProps, 'type'>> = (props) => (
  <AccessibleImage {...props} type="service" />
);

// Store image component with automatic accessibility
export const AccessibleStoreImage: React.FC<Omit<AccessibleImageProps, 'type'>> = (props) => (
  <AccessibleImage {...props} type="store" />
);

// Icon component with automatic accessibility
export const AccessibleIcon: React.FC<Omit<AccessibleImageProps, 'type'>> = (props) => (
  <AccessibleImage {...props} type="icon" />
);

// Decorative image component (hidden from screen readers)
export const DecorativeImage: React.FC<Omit<AccessibleImageProps, 'type'>> = (props) => (
  <AccessibleImage {...props} type="decorative" />
);

/**
 * Hook for generating accessibility props for existing Image components
 */
export const useImageAccessibility = (
  type: ImageType,
  entityName?: string,
  action?: string,
  customAltText?: string
) => {
  if (customAltText) {
    return {
      accessibilityLabel: customAltText,
      accessibilityRole: 'image' as const,
      accessible: true,
      importantForAccessibility: 'yes' as const,
    };
  }

  switch (type) {
    case 'decorative':
      return ImageAccessibilityUtils.getDecorative();
    
    case 'informative':
    case 'logo':
    case 'avatar':
    case 'service':
    case 'store':
      const description = ImageAccessibilityUtils.generateAltText(type, entityName, action);
      return ImageAccessibilityUtils.getInformative(description);
    
    case 'functional':
    case 'icon':
      const functionalDescription = ImageAccessibilityUtils.generateAltText(type, entityName, action);
      return ImageAccessibilityUtils.getFunctional(functionalDescription, action);
    
    default:
      return ImageAccessibilityUtils.getInformative(entityName || 'Image');
  }
};

/**
 * Utility function to enhance existing Image components with accessibility
 */
export const enhanceImageAccessibility = (
  imageProps: ImageProps,
  type: ImageType,
  entityName?: string,
  action?: string
): ImageProps => {
  const accessibilityProps = useImageAccessibility(type, entityName, action);
  
  return {
    ...imageProps,
    ...accessibilityProps,
  };
};
