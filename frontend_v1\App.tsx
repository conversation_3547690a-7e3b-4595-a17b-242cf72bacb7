/**
 * <PERSON><PERSON><PERSON> App - Main Application Entry Point
 *
 * Component Contract:
 * - Provides navigation container for the entire app
 * - Integrates with role-based navigation system
 * - Handles authentication state routing
 * - Supports dual-role user experience (customer/provider)
 * - Follows React Navigation v6+ patterns
 * - Manages global navigation ref for programmatic navigation
 *
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

// CRITICAL: Pre-bundle fix MUST be absolutely first
import './src/utils/preBundleFix';
// CRITICAL: Emergency module loader MUST be first
import './src/utils/emergencyModuleLoader';
// CRITICAL: Ensure Hermes fixes are loaded (redundant import for safety)
import './src/utils/hermesErrorFix';
import './src/utils/hermesCompatibility';
import './src/utils/hermesModuleResolutionFix';
// CRITICAL: Runtime medium property fix for persistent errors
import './src/utils/runtimeMediumPropertyFix';

import { NavigationContainer } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import React, { createRef, useEffect } from 'react';
import { View, Platform, StatusBar as RNStatusBar } from 'react-native';

// CRITICAL: Import Hermes fixes AFTER React Native core modules
// Re-enabled to prevent Hermes runtime errors
import './src/utils/hermesErrorFix';
import './src/utils/hermesCompatibility';

// CRITICAL: Import theme safety utilities to prevent theme-related crashes
import { initializeThemeSafety } from './src/utils/themeSafety';
import './src/utils/comprehensiveThemeFix';

import ThemeErrorBoundary from './src/components/ThemeErrorBoundary';
import { ThemeProvider } from './src/contexts/ThemeContext';
import { RootNavigator } from './src/navigation/RootNavigator';
import { ConsistentHelpProvider, DocumentationProvider } from './src/components/help';
import { ActionFeedbackProvider } from './src/components/ui/ActionFeedbackSystem';
import { UndoProvider } from './src/components/providers';
// ErrorRecoveryProvider temporarily removed due to provider order issues
import { ConsistencyProvider } from './src/design-system/ConsistencyStandards';
import { UserControlProvider } from './src/components/ux/UserControlEnhancements';
import { ErrorPreventionProvider } from './src/components/ux/ErrorPreventionSystem';

// Global navigation ref for programmatic navigation
export const navigationRef = createRef<any>();

export default function App() {
  // Initialize theme safety utilities on app start
  useEffect(() => {
    console.log('🛡️ App: Initializing theme safety utilities');
    initializeThemeSafety();
    console.log('🛡️ App: Theme safety utilities initialized');
  }, []);



  return (
    <View style={{
      flex: 1,
      paddingTop: Platform.OS === 'android' ? (RNStatusBar.currentHeight || 0) : 44
    }}>
      <ThemeErrorBoundary>
        <ThemeProvider>
          <ActionFeedbackProvider>
            <UndoProvider>
              <ErrorPreventionProvider>
                <DocumentationProvider>
                  <ConsistentHelpProvider>
                    <NavigationContainer ref={navigationRef}>
                      <UserControlProvider>
                        <RootNavigator />
                        <StatusBar style="auto" />
                      </UserControlProvider>
                    </NavigationContainer>
                  </ConsistentHelpProvider>
                </DocumentationProvider>
              </ErrorPreventionProvider>
            </UndoProvider>
          </ActionFeedbackProvider>
        </ThemeProvider>
      </ThemeErrorBoundary>
    </View>
  );
}
