/**
 * Provider Earnings Analytics - Earnings Analytics Component
 * 
 * Phase 1 MVP Implementation:
 * - Basic earnings overview and KPIs
 * - Weekly/Monthly earnings display
 * - Simple earnings history
 * - Payout information
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import { useI18n } from '../../../contexts/I18nContext';
import { Card } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/Button';
import { Icon } from '../../../components/ui/Icon';

// Earnings interfaces
interface EarningsKPI {
  id: string;
  title: string;
  value: string;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  icon: string;
  color: string;
}

interface EarningsTransaction {
  id: string;
  date: string;
  clientName: string;
  serviceName: string;
  amount: number;
  status: 'completed' | 'pending' | 'processing';
  payoutDate?: string;
}

interface PayoutInfo {
  nextPayoutDate: string;
  nextPayoutAmount: number;
  pendingAmount: number;
  totalEarnings: number;
}

type EarningsPeriod = 'week' | 'month' | 'year';

export const ProviderEarningsAnalytics: React.FC = () => {
  const { colors } = useTheme();
  const { t } = useI18n();
  
  // State
  const [selectedPeriod, setSelectedPeriod] = useState<EarningsPeriod>('week');
  const [earningsKPIs, setEarningsKPIs] = useState<EarningsKPI[]>([]);
  const [transactions, setTransactions] = useState<EarningsTransaction[]>([]);
  const [payoutInfo, setPayoutInfo] = useState<PayoutInfo | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Load earnings data
  useEffect(() => {
    loadEarningsData();
  }, [selectedPeriod]);

  const loadEarningsData = async () => {
    try {
      setIsLoading(true);
      
      // Mock data for Phase 1 MVP
      const mockKPIs: EarningsKPI[] = [
        {
          id: '1',
          title: t('provider.earnings.totalEarnings'),
          value: selectedPeriod === 'week' ? '$1,245' : selectedPeriod === 'month' ? '$4,890' : '$58,680',
          change: '+12%',
          changeType: 'positive',
          icon: 'dollar-sign',
          color: colors.success[500],
        },
        {
          id: '2',
          title: t('provider.earnings.completedJobs'),
          value: selectedPeriod === 'week' ? '18' : selectedPeriod === 'month' ? '72' : '864',
          change: '+8%',
          changeType: 'positive',
          icon: 'check-circle',
          color: colors.primary[500],
        },
        {
          id: '3',
          title: t('provider.earnings.avgJobValue'),
          value: selectedPeriod === 'week' ? '$69' : selectedPeriod === 'month' ? '$68' : '$68',
          change: '+3%',
          changeType: 'positive',
          icon: 'trending-up',
          color: colors.info[500],
        },
        {
          id: '4',
          title: t('provider.earnings.tips'),
          value: selectedPeriod === 'week' ? '$156' : selectedPeriod === 'month' ? '$624' : '$7,488',
          change: '+15%',
          changeType: 'positive',
          icon: 'heart',
          color: colors.warning[500],
        },
      ];

      const mockTransactions: EarningsTransaction[] = [
        {
          id: '1',
          date: '2025-07-19',
          clientName: 'Sarah Johnson',
          serviceName: 'House Cleaning',
          amount: 85,
          status: 'completed',
          payoutDate: '2025-07-21',
        },
        {
          id: '2',
          date: '2025-07-18',
          clientName: 'Mike Chen',
          serviceName: 'Lawn Care',
          amount: 120,
          status: 'completed',
          payoutDate: '2025-07-21',
        },
        {
          id: '3',
          date: '2025-07-17',
          clientName: 'Emma Wilson',
          serviceName: 'Pet Sitting',
          amount: 160,
          status: 'processing',
          payoutDate: '2025-07-24',
        },
        {
          id: '4',
          date: '2025-07-16',
          clientName: 'David Brown',
          serviceName: 'Handyman Services',
          amount: 95,
          status: 'completed',
          payoutDate: '2025-07-18',
        },
        {
          id: '5',
          date: '2025-07-15',
          clientName: 'Lisa Garcia',
          serviceName: 'Tutoring',
          amount: 75,
          status: 'completed',
          payoutDate: '2025-07-18',
        },
      ];

      const mockPayoutInfo: PayoutInfo = {
        nextPayoutDate: '2025-07-21',
        nextPayoutAmount: 365,
        pendingAmount: 160,
        totalEarnings: 12450,
      };

      setEarningsKPIs(mockKPIs);
      setTransactions(mockTransactions);
      setPayoutInfo(mockPayoutInfo);
    } catch (error) {
      console.error('Error loading earnings data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadEarningsData();
    setIsRefreshing(false);
  };

  const getStatusColor = (status: EarningsTransaction['status']) => {
    switch (status) {
      case 'completed':
        return colors.success[500];
      case 'processing':
        return colors.warning[500];
      case 'pending':
        return colors.info[500];
      default:
        return colors.text.secondary;
    }
  };

  const renderPeriodButton = (period: EarningsPeriod, label: string) => (
    <TouchableOpacity
      key={period}
      style={[
        styles.periodButton,
        {
          backgroundColor: selectedPeriod === period 
            ? colors.primary[500] 
            : colors.background,
          borderColor: colors.primary[500],
        }
      ]}
      onPress={() => setSelectedPeriod(period)}
      accessibilityRole="button"
      accessibilityLabel={label}
      accessibilityState={{ selected: selectedPeriod === period }}
    >
      <Text
        style={[
          styles.periodButtonText,
          {
            color: selectedPeriod === period 
              ? colors.white 
              : colors.primary[500],
          }
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderKPICard = (kpi: EarningsKPI) => (
    <Card key={kpi.id} style={styles.kpiCard}>
      <View style={styles.kpiHeader}>
        <Icon 
          name={kpi.icon} 
          size={24} 
          color={kpi.color}
          accessibilityLabel={kpi.title}
        />
        <Text style={[styles.kpiTitle, { color: colors.text.secondary }]}>
          {kpi.title}
        </Text>
      </View>
      <Text style={[styles.kpiValue, { color: colors.text.primary }]}>
        {kpi.value}
      </Text>
      {kpi.change && (
        <Text 
          style={[
            styles.kpiChange, 
            { 
              color: kpi.changeType === 'positive' 
                ? colors.success[500] 
                : kpi.changeType === 'negative'
                ? colors.error[500]
                : colors.text.secondary
            }
          ]}
        >
          {kpi.change}
        </Text>
      )}
    </Card>
  );

  const renderTransaction = (transaction: EarningsTransaction) => (
    <Card key={transaction.id} style={styles.transactionCard}>
      <View style={styles.transactionHeader}>
        <View style={styles.transactionInfo}>
          <Text style={[styles.transactionClient, { color: colors.text.primary }]}>
            {transaction.clientName}
          </Text>
          <Text style={[styles.transactionService, { color: colors.text.secondary }]}>
            {transaction.serviceName}
          </Text>
          <Text style={[styles.transactionDate, { color: colors.text.secondary }]}>
            {transaction.date}
          </Text>
        </View>
        <View style={styles.transactionAmount}>
          <Text style={[styles.transactionValue, { color: colors.success[500] }]}>
            ${transaction.amount}
          </Text>
          <View 
            style={[
              styles.statusBadge,
              { backgroundColor: getStatusColor(transaction.status) }
            ]}
          >
            <Text style={[styles.statusText, { color: colors.white }]}>
              {t(`provider.earnings.status.${transaction.status}`)}
            </Text>
          </View>
        </View>
      </View>
      {transaction.payoutDate && (
        <Text style={[styles.payoutDate, { color: colors.text.secondary }]}>
          {t('provider.earnings.payoutOn')}: {transaction.payoutDate}
        </Text>
      )}
    </Card>
  );

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
          {t('provider.earnings.loading')}
        </Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: colors.background }]}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={handleRefresh}
          tintColor={colors.primary[500]}
        />
      }
      showsVerticalScrollIndicator={false}
    >
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text.primary }]}>
          {t('provider.earnings.title')}
        </Text>
      </View>

      {/* Period Selector */}
      <View style={styles.periodSelector}>
        {renderPeriodButton('week', t('provider.earnings.thisWeek'))}
        {renderPeriodButton('month', t('provider.earnings.thisMonth'))}
        {renderPeriodButton('year', t('provider.earnings.thisYear'))}
      </View>

      {/* Payout Information */}
      {payoutInfo && (
        <Card style={styles.payoutCard}>
          <Text style={[styles.payoutTitle, { color: colors.text.primary }]}>
            {t('provider.earnings.nextPayout')}
          </Text>
          <View style={styles.payoutInfo}>
            <View style={styles.payoutItem}>
              <Text style={[styles.payoutLabel, { color: colors.text.secondary }]}>
                {t('provider.earnings.payoutDate')}
              </Text>
              <Text style={[styles.payoutValue, { color: colors.text.primary }]}>
                {payoutInfo.nextPayoutDate}
              </Text>
            </View>
            <View style={styles.payoutItem}>
              <Text style={[styles.payoutLabel, { color: colors.text.secondary }]}>
                {t('provider.earnings.payoutAmount')}
              </Text>
              <Text style={[styles.payoutValue, { color: colors.success[500] }]}>
                ${payoutInfo.nextPayoutAmount}
              </Text>
            </View>
          </View>
          <View style={styles.payoutSummary}>
            <Text style={[styles.payoutSummaryText, { color: colors.text.secondary }]}>
              {t('provider.earnings.pendingAmount')}: ${payoutInfo.pendingAmount}
            </Text>
            <Text style={[styles.payoutSummaryText, { color: colors.text.secondary }]}>
              {t('provider.earnings.totalEarnings')}: ${payoutInfo.totalEarnings.toLocaleString()}
            </Text>
          </View>
        </Card>
      )}

      {/* KPIs */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
          {t('provider.earnings.overview')}
        </Text>
        <View style={styles.kpiGrid}>
          {earningsKPIs.map(renderKPICard)}
        </View>
      </View>

      {/* Recent Transactions */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            {t('provider.earnings.recentTransactions')}
          </Text>
          <Button
            title={t('provider.earnings.viewAll')}
            variant="outline"
            size="small"
            onPress={() => console.log('View all transactions')}
          />
        </View>
        {transactions.map(renderTransaction)}
      </View>

      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
          {t('provider.earnings.quickActions')}
        </Text>
        <View style={styles.quickActions}>
          <Button
            title={t('provider.earnings.downloadStatement')}
            variant="outline"
            onPress={() => console.log('Download statement')}
            style={styles.quickActionButton}
          />
          <Button
            title={t('provider.earnings.taxDocuments')}
            variant="outline"
            onPress={() => console.log('View tax documents')}
            style={styles.quickActionButton}
          />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  periodSelector: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  payoutCard: {
    padding: 16,
    marginBottom: 24,
  },
  payoutTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  payoutInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  payoutItem: {
    flex: 1,
  },
  payoutLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  payoutValue: {
    fontSize: 18,
    fontWeight: '600',
  },
  payoutSummary: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 16,
  },
  payoutSummaryText: {
    fontSize: 14,
    marginBottom: 4,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  kpiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  kpiCard: {
    width: '48%',
    padding: 16,
    marginBottom: 12,
  },
  kpiHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  kpiTitle: {
    fontSize: 14,
    marginLeft: 8,
  },
  kpiValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  kpiChange: {
    fontSize: 12,
    fontWeight: '500',
  },
  transactionCard: {
    padding: 16,
    marginBottom: 12,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionClient: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  transactionService: {
    fontSize: 14,
    marginBottom: 4,
  },
  transactionDate: {
    fontSize: 12,
  },
  transactionAmount: {
    alignItems: 'flex-end',
  },
  transactionValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  payoutDate: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
});
