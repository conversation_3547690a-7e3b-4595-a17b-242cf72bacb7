/**
 * Lazy Loading Utilities - Performance Optimization
 *
 * Component Contract:
 * - Provides lazy loading wrapper for React components
 * - Implements loading states and error boundaries
 * - Optimizes bundle size through code splitting
 * - Provides fallback components for better UX
 * - Supports preloading for critical routes
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { Suspense, ComponentType, lazy } from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useI18n } from '../contexts/I18nContext';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from './responsiveUtils';

interface LazyLoadingProps {
  children: React.ReactNode;
  fallback?: React.ComponentType;
  testID?: string;
}

interface LazyComponentOptions {
  preload?: boolean;
  fallback?: React.ComponentType;
  errorBoundary?: boolean;
}

/**
 * Default loading fallback component
 */
const DefaultLoadingFallback: React.FC = () => {
  const { colors } = useTheme();
  const { t } = useI18n();
  const styles = createStyles(colors);

  return (
    <View style={styles.loadingContainer} testID="lazy-loading-fallback">
      <ActivityIndicator
        size="large"
        color={colors.primary.default}
        accessibilityLabel={t('common.loading')}
      />
      <Text style={styles.loadingText} accessibilityRole="text">
        {t('common.loading')}
      </Text>
    </View>
  );
};

/**
 * Error boundary fallback component
 */
const ErrorFallback: React.FC<{ error?: Error }> = ({ error }) => {
  const { colors } = useTheme();
  const { t } = useI18n();
  const styles = createStyles(colors);

  return (
    <View style={styles.errorContainer} testID="lazy-loading-error">
      <Text style={styles.errorTitle} accessibilityRole="heading">
        {t('common.error')}
      </Text>
      <Text style={styles.errorMessage} accessibilityRole="text">
        {error?.message || 'Failed to load component'}
      </Text>
    </View>
  );
};

/**
 * Lazy loading wrapper component
 */
export const LazyWrapper: React.FC<LazyLoadingProps> = ({
  children,
  fallback: CustomFallback,
  testID = 'lazy-wrapper',
}) => {
  const FallbackComponent = CustomFallback || DefaultLoadingFallback;

  return (
    <Suspense fallback={<FallbackComponent />}>
      <View style={{ flex: 1 }} testID={testID}>
        {children}
      </View>
    </Suspense>
  );
};

/**
 * Create a lazy-loaded component with options
 */
export const createLazyComponent = <T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: LazyComponentOptions = {}
): ComponentType<any> => {
  const LazyComponent = lazy(importFn);

  // Preload the component if requested
  if (options.preload) {
    // Preload after a short delay to not block initial render
    setTimeout(() => {
      importFn().catch(console.error);
    }, 100);
  }

  return (props: any) => (
    <LazyWrapper
      fallback={options.fallback}
      testID={`lazy-${LazyComponent.displayName || 'component'}`}
    >
      <LazyComponent {...props} />
    </LazyWrapper>
  );
};

/**
 * Preload a lazy component
 */
export const preloadComponent = (
  importFn: () => Promise<{ default: ComponentType<any> }>
): void => {
  importFn().catch(console.error);
};

/**
 * Lazy load multiple components
 */
export const createLazyComponents = <T extends Record<string, () => Promise<{ default: ComponentType<any> }>>>(
  imports: T,
  options: LazyComponentOptions = {}
): Record<keyof T, ComponentType<any>> => {
  const lazyComponents = {} as Record<keyof T, ComponentType<any>>;

  Object.entries(imports).forEach(([key, importFn]) => {
    lazyComponents[key as keyof T] = createLazyComponent(importFn, options);
  });

  return lazyComponents;
};

/**
 * Performance monitoring for lazy loading
 */
export const measureLazyLoadTime = (componentName: string) => {
  const startTime = performance.now();
  
  return () => {
    const endTime = performance.now();
    const loadTime = endTime - startTime;
    
    if (__DEV__) {
      console.log(`[LazyLoading] ${componentName} loaded in ${loadTime.toFixed(2)}ms`);
    }
    
    return loadTime;
  };
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary,
      paddingHorizontal: getResponsiveSpacing(20),
    },
    loadingText: {
      marginTop: getResponsiveSpacing(16),
      fontSize: getResponsiveFontSize(16),
      color: colors.text.secondary,
      textAlign: 'center',
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary,
      paddingHorizontal: getResponsiveSpacing(20),
    },
    errorTitle: {
      fontSize: getResponsiveFontSize(18),
      fontWeight: '600',
      color: colors.error.default,
      marginBottom: getResponsiveSpacing(8),
      textAlign: 'center',
    },
    errorMessage: {
      fontSize: getResponsiveFontSize(14),
      color: colors.text.secondary,
      textAlign: 'center',
      lineHeight: getResponsiveFontSize(20),
    },
  });
