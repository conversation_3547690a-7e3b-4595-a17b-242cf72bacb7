/**
 * Language Switcher Component - Canadian Bilingual Support
 *
 * Component Contract:
 * - Provides language switching between English and French
 * - Follows Canadian government bilingual requirements
 * - Accessible design with proper ARIA labels
 * - Responsive design for mobile and tablet
 * - Integrates with I18n context
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { useTheme } from '../../contexts/ThemeContext';
import { useI18n } from '../../contexts/I18nContext';
import { AccessibleTouchable } from '../accessibility/AccessibleTouchable';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
  getMinimumTouchTarget,
} from '../../utils/responsiveUtils';

interface LanguageSwitcherProps {
  variant?: 'compact' | 'full';
  showLabel?: boolean;
  testID?: string;
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  variant = 'compact',
  showLabel = true,
  testID = 'language-switcher',
}) => {
  const { colors } = useTheme();
  const { currentLanguage, setLanguage, t } = useI18n();
  const styles = createStyles(colors);

  const languages = [
    { code: 'en-CA', label: 'EN', fullLabel: 'English' },
    { code: 'fr-CA', label: 'FR', fullLabel: 'Français' },
  ];

  const handleLanguageChange = (languageCode: string) => {
    setLanguage(languageCode);
  };

  if (variant === 'compact') {
    return (
      <View style={styles.compactContainer} testID={testID}>
        {languages.map((language) => (
          <AccessibleTouchable
            key={language.code}
            onPress={() => handleLanguageChange(language.code)}
            style={[
              styles.compactButton,
              currentLanguage === language.code && styles.activeButton,
            ]}
            accessibilityRole="button"
            accessibilityLabel={`Switch to ${language.fullLabel}`}
            accessibilityHint={`Changes the app language to ${language.fullLabel}`}
            accessibilityState={{ selected: currentLanguage === language.code }}
            testID={`${testID}-${language.code}`}
            minTouchTarget={getMinimumTouchTarget()}
          >
            <Text
              style={[
                styles.compactText,
                currentLanguage === language.code && styles.activeText,
              ]}
            >
              {language.label}
            </Text>
          </AccessibleTouchable>
        ))}
      </View>
    );
  }

  return (
    <View style={styles.fullContainer} testID={testID}>
      {showLabel && (
        <Text style={styles.label} accessibilityRole="text">
          {t('common.language')}
        </Text>
      )}
      <View style={styles.buttonContainer}>
        {languages.map((language) => (
          <AccessibleTouchable
            key={language.code}
            onPress={() => handleLanguageChange(language.code)}
            style={[
              styles.fullButton,
              currentLanguage === language.code && styles.activeButton,
            ]}
            accessibilityRole="button"
            accessibilityLabel={`Switch to ${language.fullLabel}`}
            accessibilityHint={`Changes the app language to ${language.fullLabel}`}
            accessibilityState={{ selected: currentLanguage === language.code }}
            testID={`${testID}-${language.code}`}
            minTouchTarget={getMinimumTouchTarget()}
          >
            <Ionicons
              name="language-outline"
              size={20}
              color={
                currentLanguage === language.code
                  ? colors.primary.contrast
                  : colors.text.secondary
              }
              style={styles.icon}
            />
            <Text
              style={[
                styles.fullText,
                currentLanguage === language.code && styles.activeText,
              ]}
            >
              {language.fullLabel}
            </Text>
          </AccessibleTouchable>
        ))}
      </View>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    compactContainer: {
      flexDirection: 'row',
      backgroundColor: colors.surface.secondary,
      borderRadius: getResponsiveSpacing(6),
      padding: getResponsiveSpacing(2),
    },
    compactButton: {
      paddingHorizontal: getResponsiveSpacing(12),
      paddingVertical: getResponsiveSpacing(6),
      borderRadius: getResponsiveSpacing(4),
      minHeight: getMinimumTouchTarget(),
      minWidth: getMinimumTouchTarget(),
      justifyContent: 'center',
      alignItems: 'center',
    },
    activeButton: {
      backgroundColor: colors.primary.default,
    },
    compactText: {
      fontSize: getResponsiveFontSize(12),
      fontWeight: '600',
      color: colors.text.secondary,
    },
    activeText: {
      color: colors.primary.contrast,
    },
    fullContainer: {
      marginVertical: getResponsiveSpacing(8),
    },
    label: {
      fontSize: getResponsiveFontSize(14),
      fontWeight: '600',
      color: colors.text.primary,
      marginBottom: getResponsiveSpacing(8),
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: getResponsiveSpacing(8),
    },
    fullButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: getResponsiveSpacing(16),
      paddingVertical: getResponsiveSpacing(12),
      borderRadius: getResponsiveSpacing(8),
      borderWidth: 1,
      borderColor: colors.border.default,
      backgroundColor: colors.surface.primary,
      minHeight: getMinimumTouchTarget(),
    },
    icon: {
      marginRight: getResponsiveSpacing(8),
    },
    fullText: {
      fontSize: getResponsiveFontSize(14),
      fontWeight: '500',
      color: colors.text.primary,
    },
  });
