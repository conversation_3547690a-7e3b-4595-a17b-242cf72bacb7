/**
 * Provider Availability Management - Availability Management Component
 * 
 * Phase 1 MVP Implementation:
 * - Functional calendar to set weekly availability
 * - Block off time functionality
 * - Basic schedule management
 * - Visual availability display
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  Alert,
  Switch,
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import { useI18n } from '../../../contexts/I18nContext';
import { Card } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/Button';
import { Icon } from '../../../components/ui/Icon';

// Availability interfaces
interface TimeSlot {
  start: string;
  end: string;
}

interface DayAvailability {
  day: string;
  isAvailable: boolean;
  timeSlots: TimeSlot[];
}

interface WeeklyAvailability {
  [key: string]: DayAvailability;
}

interface BlockedTime {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  reason: string;
}

const DAYS_OF_WEEK = [
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
  'sunday',
];

const TIME_SLOTS = [
  '06:00', '07:00', '08:00', '09:00', '10:00', '11:00',
  '12:00', '13:00', '14:00', '15:00', '16:00', '17:00',
  '18:00', '19:00', '20:00', '21:00', '22:00',
];

export const ProviderAvailabilityManagement: React.FC = () => {
  const { colors } = useTheme();
  const { t } = useI18n();
  
  // State
  const [weeklyAvailability, setWeeklyAvailability] = useState<WeeklyAvailability>({});
  const [blockedTimes, setBlockedTimes] = useState<BlockedTime[]>([]);
  const [selectedDay, setSelectedDay] = useState<string>('monday');
  const [isLoading, setIsLoading] = useState(true);
  const [hasChanges, setHasChanges] = useState(false);

  // Load availability data
  useEffect(() => {
    loadAvailability();
  }, []);

  const loadAvailability = async () => {
    try {
      setIsLoading(true);
      
      // Mock data for Phase 1 MVP
      const mockAvailability: WeeklyAvailability = {
        monday: {
          day: 'monday',
          isAvailable: true,
          timeSlots: [
            { start: '09:00', end: '12:00' },
            { start: '13:00', end: '17:00' },
          ],
        },
        tuesday: {
          day: 'tuesday',
          isAvailable: true,
          timeSlots: [
            { start: '09:00', end: '12:00' },
            { start: '13:00', end: '17:00' },
          ],
        },
        wednesday: {
          day: 'wednesday',
          isAvailable: true,
          timeSlots: [
            { start: '09:00', end: '12:00' },
            { start: '13:00', end: '17:00' },
          ],
        },
        thursday: {
          day: 'thursday',
          isAvailable: true,
          timeSlots: [
            { start: '09:00', end: '12:00' },
            { start: '13:00', end: '17:00' },
          ],
        },
        friday: {
          day: 'friday',
          isAvailable: true,
          timeSlots: [
            { start: '09:00', end: '12:00' },
            { start: '13:00', end: '17:00' },
          ],
        },
        saturday: {
          day: 'saturday',
          isAvailable: true,
          timeSlots: [
            { start: '10:00', end: '14:00' },
          ],
        },
        sunday: {
          day: 'sunday',
          isAvailable: false,
          timeSlots: [],
        },
      };

      const mockBlockedTimes: BlockedTime[] = [
        {
          id: '1',
          date: '2025-07-22',
          startTime: '10:00',
          endTime: '12:00',
          reason: 'Personal appointment',
        },
        {
          id: '2',
          date: '2025-07-25',
          startTime: '14:00',
          endTime: '16:00',
          reason: 'Equipment maintenance',
        },
      ];

      setWeeklyAvailability(mockAvailability);
      setBlockedTimes(mockBlockedTimes);
    } catch (error) {
      console.error('Error loading availability:', error);
      Alert.alert(
        t('common.error'),
        t('provider.availability.loadError')
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleDayToggle = (day: string, isAvailable: boolean) => {
    setWeeklyAvailability(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        isAvailable,
        timeSlots: isAvailable ? prev[day]?.timeSlots || [] : [],
      },
    }));
    setHasChanges(true);
  };

  const handleTimeSlotAdd = (day: string) => {
    const newTimeSlot: TimeSlot = { start: '09:00', end: '17:00' };
    
    setWeeklyAvailability(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        timeSlots: [...(prev[day]?.timeSlots || []), newTimeSlot],
      },
    }));
    setHasChanges(true);
  };

  const handleTimeSlotUpdate = (day: string, index: number, field: 'start' | 'end', value: string) => {
    setWeeklyAvailability(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        timeSlots: prev[day]?.timeSlots.map((slot, i) =>
          i === index ? { ...slot, [field]: value } : slot
        ) || [],
      },
    }));
    setHasChanges(true);
  };

  const handleTimeSlotRemove = (day: string, index: number) => {
    setWeeklyAvailability(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        timeSlots: prev[day]?.timeSlots.filter((_, i) => i !== index) || [],
      },
    }));
    setHasChanges(true);
  };

  const handleSaveChanges = async () => {
    try {
      // Save availability changes
      console.log('Saving availability:', weeklyAvailability);
      
      Alert.alert(
        t('common.success'),
        t('provider.availability.saveSuccess')
      );
      setHasChanges(false);
    } catch (error) {
      console.error('Error saving availability:', error);
      Alert.alert(
        t('common.error'),
        t('provider.availability.saveError')
      );
    }
  };

  const handleBlockTime = () => {
    // This would open a modal or navigate to a block time screen
    Alert.alert(
      t('provider.availability.blockTime'),
      t('provider.availability.blockTimeMessage'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('common.ok'), onPress: () => console.log('Block time') },
      ]
    );
  };

  const renderDayTab = (day: string) => {
    const isSelected = selectedDay === day;
    const dayAvailability = weeklyAvailability[day];
    
    return (
      <TouchableOpacity
        key={day}
        style={[
          styles.dayTab,
          {
            backgroundColor: isSelected ? colors.primary[500] : colors.background,
            borderColor: colors.primary[500],
          }
        ]}
        onPress={() => setSelectedDay(day)}
        accessibilityRole="button"
        accessibilityLabel={t(`common.days.${day}`)}
        accessibilityState={{ selected: isSelected }}
      >
        <Text
          style={[
            styles.dayTabText,
            {
              color: isSelected ? colors.white : colors.primary[500],
            }
          ]}
        >
          {t(`common.days.${day}.short`)}
        </Text>
        {dayAvailability?.isAvailable && (
          <View 
            style={[
              styles.availableIndicator,
              { backgroundColor: isSelected ? colors.white : colors.success[500] }
            ]}
          />
        )}
      </TouchableOpacity>
    );
  };

  const renderTimeSlot = (slot: TimeSlot, index: number, day: string) => (
    <Card key={index} style={styles.timeSlotCard}>
      <View style={styles.timeSlotHeader}>
        <Text style={[styles.timeSlotTitle, { color: colors.text.primary }]}>
          {t('provider.availability.timeSlot')} {index + 1}
        </Text>
        <TouchableOpacity
          onPress={() => handleTimeSlotRemove(day, index)}
          accessibilityRole="button"
          accessibilityLabel={t('provider.availability.removeTimeSlot')}
        >
          <Icon name="trash-2" size={20} color={colors.error[500]} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.timeSlotInputs}>
        <View style={styles.timeInput}>
          <Text style={[styles.timeLabel, { color: colors.text.secondary }]}>
            {t('provider.availability.startTime')}
          </Text>
          <TouchableOpacity
            style={[styles.timeButton, { borderColor: colors.border }]}
            onPress={() => {
              // This would open a time picker
              console.log('Open time picker for start time');
            }}
          >
            <Text style={[styles.timeButtonText, { color: colors.text.primary }]}>
              {slot.start}
            </Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.timeInput}>
          <Text style={[styles.timeLabel, { color: colors.text.secondary }]}>
            {t('provider.availability.endTime')}
          </Text>
          <TouchableOpacity
            style={[styles.timeButton, { borderColor: colors.border }]}
            onPress={() => {
              // This would open a time picker
              console.log('Open time picker for end time');
            }}
          >
            <Text style={[styles.timeButtonText, { color: colors.text.primary }]}>
              {slot.end}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Card>
  );

  const renderBlockedTime = (blockedTime: BlockedTime) => (
    <Card key={blockedTime.id} style={styles.blockedTimeCard}>
      <View style={styles.blockedTimeHeader}>
        <Text style={[styles.blockedTimeDate, { color: colors.text.primary }]}>
          {blockedTime.date}
        </Text>
        <TouchableOpacity
          onPress={() => {
            setBlockedTimes(prev => prev.filter(bt => bt.id !== blockedTime.id));
          }}
          accessibilityRole="button"
          accessibilityLabel={t('provider.availability.removeBlockedTime')}
        >
          <Icon name="x" size={20} color={colors.error[500]} />
        </TouchableOpacity>
      </View>
      <Text style={[styles.blockedTimeSlot, { color: colors.text.secondary }]}>
        {blockedTime.startTime} - {blockedTime.endTime}
      </Text>
      <Text style={[styles.blockedTimeReason, { color: colors.text.secondary }]}>
        {blockedTime.reason}
      </Text>
    </Card>
  );

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
          {t('provider.availability.loading')}
        </Text>
      </View>
    );
  }

  const selectedDayAvailability = weeklyAvailability[selectedDay];

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text.primary }]}>
          {t('provider.availability.title')}
        </Text>
        {hasChanges && (
          <Button
            title={t('common.save')}
            variant="primary"
            size="small"
            onPress={handleSaveChanges}
          />
        )}
      </View>

      {/* Day Tabs */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.dayTabs}
      >
        {DAYS_OF_WEEK.map(renderDayTab)}
      </ScrollView>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Day Availability Toggle */}
        <Card style={styles.dayToggleCard}>
          <View style={styles.dayToggleHeader}>
            <Text style={[styles.dayToggleTitle, { color: colors.text.primary }]}>
              {t('provider.availability.availableOn')} {t(`common.days.${selectedDay}`)}
            </Text>
            <Switch
              value={selectedDayAvailability?.isAvailable || false}
              onValueChange={(value) => handleDayToggle(selectedDay, value)}
              trackColor={{ 
                false: colors.gray[300], 
                true: colors.primary[200] 
              }}
              thumbColor={
                selectedDayAvailability?.isAvailable 
                  ? colors.primary[500] 
                  : colors.gray[500]
              }
            />
          </View>
        </Card>

        {/* Time Slots */}
        {selectedDayAvailability?.isAvailable && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
                {t('provider.availability.timeSlots')}
              </Text>
              <Button
                title={t('provider.availability.addTimeSlot')}
                variant="outline"
                size="small"
                onPress={() => handleTimeSlotAdd(selectedDay)}
              />
            </View>
            
            {selectedDayAvailability.timeSlots.length > 0 ? (
              selectedDayAvailability.timeSlots.map((slot, index) =>
                renderTimeSlot(slot, index, selectedDay)
              )
            ) : (
              <Card style={styles.emptyTimeSlots}>
                <Text style={[styles.emptyText, { color: colors.text.secondary }]}>
                  {t('provider.availability.noTimeSlots')}
                </Text>
              </Card>
            )}
          </View>
        )}

        {/* Blocked Times */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
              {t('provider.availability.blockedTimes')}
            </Text>
            <Button
              title={t('provider.availability.blockTime')}
              variant="outline"
              size="small"
              onPress={handleBlockTime}
            />
          </View>
          
          {blockedTimes.length > 0 ? (
            blockedTimes.map(renderBlockedTime)
          ) : (
            <Card style={styles.emptyBlockedTimes}>
              <Text style={[styles.emptyText, { color: colors.text.secondary }]}>
                {t('provider.availability.noBlockedTimes')}
              </Text>
            </Card>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  dayTabs: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  dayTab: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
    alignItems: 'center',
    minWidth: 60,
  },
  dayTabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  availableIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginTop: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  dayToggleCard: {
    padding: 16,
    marginBottom: 16,
  },
  dayToggleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dayToggleTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  timeSlotCard: {
    padding: 16,
    marginBottom: 12,
  },
  timeSlotHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  timeSlotTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  timeSlotInputs: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  timeInput: {
    flex: 1,
    marginHorizontal: 4,
  },
  timeLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  timeButton: {
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    alignItems: 'center',
  },
  timeButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  blockedTimeCard: {
    padding: 16,
    marginBottom: 12,
  },
  blockedTimeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  blockedTimeDate: {
    fontSize: 16,
    fontWeight: '600',
  },
  blockedTimeSlot: {
    fontSize: 14,
    marginBottom: 4,
  },
  blockedTimeReason: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  emptyTimeSlots: {
    padding: 24,
    alignItems: 'center',
  },
  emptyBlockedTimes: {
    padding: 24,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
});
