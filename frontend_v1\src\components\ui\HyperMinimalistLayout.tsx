/**
 * Hyper-Minimalist Layout Component
 * 
 * Implements extreme visual reduction and maximum white space utilization.
 * Provides clean, distraction-free layouts that focus on essential content.
 * 
 * Features:
 * - Extreme reduction of visual elements
 * - Maximum white space utilization
 * - Invisible interface philosophy
 * - Content-first design approach
 * - Responsive spacing system
 * - Accessibility compliant
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import { View, ScrollView, StyleSheet, ViewStyle } from 'react-native';
import { SafeAreaWrapper } from '../../components/ui/SafeAreaWrapper';
import { useTheme } from '../../contexts/ThemeContext';
import { useSafeTheme } from '../../hooks/useSafeTheme';
import { HyperMinimalistTheme } from '../../design-system/HyperMinimalistTheme';

// Layout variant types
export type LayoutVariant = 'page' | 'section' | 'container' | 'content';

// Spacing intensity types
export type SpacingIntensity = 'minimal' | 'comfortable' | 'generous' | 'maximum';

// Layout props interface
export interface HyperMinimalistLayoutProps {
  children: React.ReactNode;
  variant?: LayoutVariant;
  spacing?: SpacingIntensity;
  scrollable?: boolean;
  safeArea?: boolean;
  centered?: boolean;
  maxWidth?: boolean;
  style?: ViewStyle;
  contentStyle?: ViewStyle;
  testID?: string;
}

export const HyperMinimalistLayout: React.FC<HyperMinimalistLayoutProps> = ({
  children,
  variant = 'page',
  spacing = 'comfortable',
  scrollable = false,
  safeArea = true,
  centered = false,
  maxWidth = false,
  style,
  contentStyle,
  testID,
}) => {
  const { isDark } = useTheme();
  const { theme, colors, safeGet } = useSafeTheme();

  // Get spacing based on intensity
  const getSpacing = (): number => {
    switch (spacing) {
      case 'minimal':
        return safeGet('spacing.md', 8); // 8px
      case 'comfortable':
        return safeGet('spacing.xl', 24); // 24px
      case 'generous':
        return safeGet('spacing.3xl', 64); // 64px
      case 'maximum':
        return safeGet('spacing.5xl', 128); // 128px
      default:
        return safeGet('spacing.xl', 24);
    }
  };

  // Get variant-specific styles
  const getVariantStyles = (): ViewStyle => {
    const spacingValue = getSpacing();
    
    switch (variant) {
      case 'page':
        return {
          flex: 1,
          backgroundColor: colors.white,
          paddingHorizontal: spacingValue,
          paddingVertical: spacingValue / 2,
        };
        
      case 'section':
        return {
          paddingVertical: spacingValue,
          paddingHorizontal: spacingValue / 2,
        };
        
      case 'container':
        return {
          paddingHorizontal: spacingValue / 2,
          paddingVertical: spacingValue / 4,
        };
        
      case 'content':
        return {
          paddingHorizontal: theme.spacing.component.padding,
          paddingVertical: theme.spacing.component.padding / 2,
        };
        
      default:
        return {
          flex: 1,
          backgroundColor: colors.white,
          paddingHorizontal: spacingValue,
          paddingVertical: spacingValue / 2,
        };
    }
  };

  const variantStyles = getVariantStyles();
  
  // Base container styles
  const containerStyle: ViewStyle = [
    styles.base,
    variantStyles,
    centered && styles.centered,
    maxWidth && styles.maxWidth,
    style,
  ].filter(Boolean) as ViewStyle;

  // Content styles
  const contentStyles: ViewStyle = [
    styles.content,
    contentStyle,
  ].filter(Boolean) as ViewStyle;

  // Render scrollable layout
  if (scrollable) {
    const ScrollContainer = safeArea ? SafeAreaView : View;
    
    return (
      <ScrollContainer style={[styles.scrollContainer, { backgroundColor: colors.white }]} testID={testID}>
        <ScrollView
          style={containerStyle}
          contentContainerStyle={contentStyles}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          bounces={false}
          overScrollMode="never"
        >
          {children}
        </ScrollView>
      </ScrollContainer>
    );
  }

  // Render static layout
  const Container = safeArea ? SafeAreaView : View;
  
  return (
    <Container style={containerStyle} testID={testID}>
      <View style={contentStyles}>
        {children}
      </View>
    </Container>
  );
};

// Hyper-Minimalist Section Component
export interface HyperMinimalistSectionProps {
  children: React.ReactNode;
  spacing?: SpacingIntensity;
  divider?: boolean;
  style?: ViewStyle;
  testID?: string;
}

export const HyperMinimalistSection: React.FC<HyperMinimalistSectionProps> = ({
  children,
  spacing = 'comfortable',
  divider = false,
  style,
  testID,
}) => {
  const { isDark } = useTheme();
  const { theme, colors, safeGet } = useSafeTheme();

  const getSpacing = (): number => {
    switch (spacing) {
      case 'minimal':
        return safeGet('spacing.lg', 16); // 16px
      case 'comfortable':
        return safeGet('spacing.2xl', 40); // 40px
      case 'generous':
        return safeGet('spacing.3xl', 64); // 64px
      case 'maximum':
        return safeGet('spacing.4xl', 96); // 96px
      default:
        return safeGet('spacing.2xl', 40);
    }
  };

  const spacingValue = getSpacing();

  return (
    <View
      style={[
        {
          paddingVertical: spacingValue,
          borderBottomWidth: divider ? 1 : 0,
          borderBottomColor: colors.gray[100],
        },
        style,
      ]}
      testID={testID}
    >
      {children}
    </View>
  );
};

// Hyper-Minimalist Container Component
export interface HyperMinimalistContainerProps {
  children: React.ReactNode;
  maxWidth?: boolean;
  centered?: boolean;
  padding?: SpacingIntensity;
  style?: ViewStyle;
  testID?: string;
}

export const HyperMinimalistContainer: React.FC<HyperMinimalistContainerProps> = ({
  children,
  maxWidth = false,
  centered = false,
  padding = 'comfortable',
  style,
  testID,
}) => {
  const { safeGet } = useSafeTheme();

  const getPadding = (): number => {
    switch (padding) {
      case 'minimal':
        return safeGet('spacing.sm', 4); // 4px
      case 'comfortable':
        return safeGet('spacing.lg', 16); // 16px
      case 'generous':
        return safeGet('spacing.xl', 24); // 24px
      case 'maximum':
        return safeGet('spacing.2xl', 40); // 40px
      default:
        return safeGet('spacing.lg', 16);
    }
  };

  const paddingValue = getPadding();

  return (
    <View
      style={[
        {
          paddingHorizontal: paddingValue,
          paddingVertical: paddingValue / 2,
        },
        maxWidth && styles.maxWidth,
        centered && styles.centered,
        style,
      ]}
      testID={testID}
    >
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  base: {
    flex: 1,
  },
  
  scrollContainer: {
    flex: 1,
  },
  
  content: {
    flexGrow: 1,
  },
  
  centered: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  maxWidth: {
    maxWidth: HyperMinimalistTheme.layout.container.lg,
    width: '100%',
    alignSelf: 'center',
  },
});

export default HyperMinimalistLayout;
