/**
 * Booking Screen - Customer Booking Flow
 *
 * Component Contract:
 * - Provides booking interface for customers
 * - Supports date and time selection
 * - Displays service and provider information
 * - Handles booking confirmation and payment
 * - Integrates with backend API for booking creation
 * - Follows TDD methodology with comprehensive test coverage
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useNavigation, useRoute } from '@react-navigation/native';
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';

import { Box } from '../../components/atoms/Box';
import { Button } from '../../components/atoms/Button';
import { useBookingStore } from '../../store/bookingSlice';
import { MinimalistTooltip, InfoTooltip } from '../../components/ui/MinimalistTooltip';
import { CollapsibleSection, DetailsSection } from '../../components/ui/CollapsibleSection';

// Types
interface Service {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  duration: number;
  provider: {
    id: string;
    name: string;
    rating: number;
    location: string;
    address: string;
  };
}

interface TimeSlot {
  id: string;
  time: string;
  available: boolean;
}

interface BookingData {
  serviceId: string;
  providerId: string;
  date: string;
  timeSlot: string;
  notes?: string;
}

interface RouteParams {
  serviceId?: string;
  providerId?: string;
  service?: Service;
  provider?: any;
}

export const BookingScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { serviceId, providerId, service: passedService, provider: passedProvider } = route.params as RouteParams;

  // State
  const [service, setService] = useState<Service | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string>('');
  const [availableTimeSlots, setAvailableTimeSlots] = useState<TimeSlot[]>([]);
  const [isBooking, setIsBooking] = useState(false);

  // Get booking store
  const { createBooking } = useBookingStore();

  // Use passed service data or fallback to mock data
  const mockService: Service = passedService || {
    id: serviceId || '1',
    name: 'Hair Cut & Style',
    description: 'Professional haircut and styling service',
    category: 'Hair',
    price: 45,
    duration: 60,
    provider: passedProvider || {
      id: providerId || '1',
      name: 'Bella Beauty Salon',
      rating: 4.8,
      location: 'Downtown',
      address: '123 Main Street, Downtown',
    },
  };

  const mockTimeSlots: TimeSlot[] = [
    { id: '1', time: '9:00 AM', available: true },
    { id: '2', time: '10:30 AM', available: true },
    { id: '3', time: '12:00 PM', available: false },
    { id: '4', time: '2:00 PM', available: true },
    { id: '5', time: '3:30 PM', available: true },
    { id: '6', time: '5:00 PM', available: false },
  ];

  // Generate next 7 days for date selection
  const getAvailableDates = () => {
    const dates = [];
    const today = new Date();

    for (let i = 1; i <= 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push({
        value: date.toISOString().split('T')[0],
        label: date.toLocaleDateString('en-US', {
          weekday: 'short',
          month: 'short',
          day: 'numeric',
        }),
      });
    }

    return dates;
  };

  const availableDates = getAvailableDates();

  // Load service data on mount
  useEffect(() => {
    loadService();
  }, [serviceId]);

  // Load time slots when date is selected
  useEffect(() => {
    if (selectedDate) {
      loadTimeSlots(selectedDate);
    }
  }, [selectedDate]);

  const loadService = async () => {
    setIsLoading(true);
    try {
      // Simulate API call - shorter delay for tests
      const delay = process.env.NODE_ENV === 'test' ? 100 : 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
      setService(mockService);
    } catch (error) {
      console.error('Failed to load service:', error);
      Alert.alert('Error', 'Failed to load service details');
    } finally {
      setIsLoading(false);
    }
  };

  const loadTimeSlots = async (date: string) => {
    try {
      // Simulate API call to get available time slots for the date
      const delay = process.env.NODE_ENV === 'test' ? 50 : 500;
      await new Promise(resolve => setTimeout(resolve, delay));
      setAvailableTimeSlots(mockTimeSlots);
    } catch (error) {
      console.error('Failed to load time slots:', error);
      Alert.alert('Error', 'Failed to load available time slots');
    }
  };

  const handleDateSelect = (date: string) => {
    setSelectedDate(date);
    setSelectedTimeSlot(''); // Reset time slot when date changes
  };

  const handleTimeSlotSelect = (timeSlot: string) => {
    setSelectedTimeSlot(timeSlot);
  };

  const handleBooking = async () => {
    if (!service || !selectedDate || !selectedTimeSlot) {
      Alert.alert('Error', 'Please select a date and time slot');
      return;
    }

    setIsBooking(true);
    try {
      const bookingData: BookingData = {
        serviceId: service.id,
        providerId: service.provider.id,
        date: selectedDate,
        timeSlot: selectedTimeSlot,
      };

      // Create booking using real API
      const booking = await createBooking(bookingData);

      // Navigate to booking confirmation with the created booking
      navigation.navigate('BookingConfirmation', {
        bookingData,
        booking,
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Booking failed';
      Alert.alert('Error', errorMessage);
    } finally {
      setIsBooking(false);
    }
  };

  const renderDateSelector = () => (
    <Box style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>Select Date</Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.dateScroll}>
        {availableDates.map(date => (
          <TouchableOpacity
            key={date.value}
            style={[
              styles.dateCard,
              selectedDate === date.value && styles.selectedDateCard,
            ]}
            onPress={() => handleDateSelect(date.value)}
            testID={`date-${date.value}`}>
            <Text
              style={[
                styles.dateText,
                selectedDate === date.value && styles.selectedDateText,
              ]}>
              {date.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </Box>
  );

  const renderTimeSlotSelector = () => (
    <Box style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>Select Time</Text>
      {selectedDate ? (
        <View style={styles.timeSlotsGrid}>
          {availableTimeSlots.map(slot => (
            <TouchableOpacity
              key={slot.id}
              style={[
                styles.timeSlotCard,
                !slot.available && styles.unavailableTimeSlot,
                selectedTimeSlot === slot.time && styles.selectedTimeSlot,
              ]}
              onPress={() => slot.available && handleTimeSlotSelect(slot.time)}
              disabled={!slot.available}
              testID={`time-slot-${slot.id}`}>
              <Text
                style={[
                  styles.timeSlotText,
                  !slot.available && styles.unavailableTimeSlotText,
                  selectedTimeSlot === slot.time && styles.selectedTimeSlotText,
                ]}>
                {slot.time}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      ) : (
        <Text style={styles.selectDatePrompt}>Please select a date first</Text>
      )}
    </Box>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer} testID="loading-indicator">
        <ActivityIndicator size="large" color="#2A4B32" />
        <Text style={styles.loadingText}>Loading booking details...</Text>
      </View>
    );
  }

  if (!service) {
    return (
      <View style={styles.errorContainer} testID="error-container">
        <Text style={styles.errorText}>Service not found</Text>
        <Button onPress={() => navigation.goBack()}>Go Back</Button>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} testID="booking-screen">
      {/* Service Information - Minimalist */}
      <Box style={styles.serviceInfo}>
        <View style={styles.serviceHeader}>
          <Text style={styles.serviceName}>{service.name}</Text>
          <InfoTooltip
            content={service.description}
            title="Service Details"
          />
        </View>

        <View style={styles.providerInfo}>
          <Text style={styles.providerName}>{service.provider.name}</Text>
          <Text style={styles.servicePrice}>${service.price}</Text>
        </View>

        {/* Collapsible detailed information */}
        <DetailsSection
          title="More Details"
          initiallyExpanded={false}
          details={[
            { label: "Duration", value: `${service.duration} minutes` },
            { label: "Location", value: service.provider.address },
            { label: "Rating", value: `${service.provider.rating}/5.0` },
            { label: "Category", value: service.category },
          ]}
        />
      </Box>

      {/* Date Selection */}
      {renderDateSelector()}

      {/* Time Slot Selection */}
      {renderTimeSlotSelector()}

      {/* Minimalist Booking Summary */}
      {selectedDate && selectedTimeSlot && (
        <Box style={styles.summaryContainer}>
          <View style={styles.summaryHeader}>
            <Text style={styles.summaryTitle}>Ready to Book</Text>
            <Text style={styles.summaryPrice}>${service.price}</Text>
          </View>

          <View style={styles.summaryQuick}>
            <Text style={styles.summaryQuickText}>
              {availableDates.find(d => d.value === selectedDate)?.label} at {selectedTimeSlot}
            </Text>
            <InfoTooltip
              content={`Service: ${service.name}\nProvider: ${service.provider.name}\nDuration: ${service.duration} minutes\nLocation: ${service.provider.address}`}
              title="Booking Details"
            />
          </View>
        </Box>
      )}

      {/* Book Button */}
      <Box style={styles.bookingActions}>
        <Button
          testID="book-service-button"
          onPress={handleBooking}
          disabled={!selectedDate || !selectedTimeSlot || isBooking}
          style={styles.bookButton}>
          {isBooking ? 'Booking...' : 'Book Service'}
        </Button>
      </Box>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 24,
  },
  errorText: {
    fontSize: 18,
    color: '#EF4444',
    marginBottom: 24,
    textAlign: 'center',
  },
  serviceInfo: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  serviceName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    flex: 1,
  },
  providerInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  providerName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2A4B32',
  },
  providerAddress: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
  },
  serviceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  servicePrice: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  serviceDuration: {
    fontSize: 16,
    color: '#6B7280',
  },
  sectionContainer: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  dateScroll: {
    flexDirection: 'row',
  },
  dateCard: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 12,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    minWidth: 80,
    alignItems: 'center',
  },
  selectedDateCard: {
    backgroundColor: '#2A4B32',
    borderColor: '#2A4B32',
  },
  dateText: {
    fontSize: 14,
    color: '#374151',
    textAlign: 'center',
  },
  selectedDateText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  timeSlotsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  timeSlotCard: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    minWidth: 100,
    alignItems: 'center',
  },
  selectedTimeSlot: {
    backgroundColor: '#2A4B32',
    borderColor: '#2A4B32',
  },
  unavailableTimeSlot: {
    backgroundColor: '#F3F4F6',
    borderColor: '#D1D5DB',
    opacity: 0.5,
  },
  timeSlotText: {
    fontSize: 14,
    color: '#374151',
  },
  selectedTimeSlotText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  unavailableTimeSlotText: {
    color: '#9CA3AF',
  },
  selectDatePrompt: {
    fontSize: 14,
    color: '#6B7280',
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 20,
  },
  summaryContainer: {
    padding: 20,
    backgroundColor: '#F9FAFB',
    margin: 20,
    borderRadius: 8,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  summaryQuick: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  summaryQuickText: {
    fontSize: 14,
    color: '#6B7280',
    flex: 1,
  },
  summaryPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2A4B32',
  },
  bookingActions: {
    padding: 20,
  },
  bookButton: {
    minHeight: 48,
  },
});
