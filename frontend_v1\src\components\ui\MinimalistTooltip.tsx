/**
 * Minimalist Tooltip Component
 * 
 * Provides clean, accessible tooltips for decluttering interfaces
 * by moving secondary information to on-demand display.
 * 
 * Features:
 * - WCAG 2.2 AA compliant
 * - Touch and hover support
 * - Customizable positioning
 * - Smooth animations
 * - Screen reader support
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Animated,
  Dimensions,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { HyperMinimalistTheme } from '../../design-system/HyperMinimalistTheme';
import { AccessibilityUtils } from '../../utils/accessibilityUtils';

// Tooltip position options
export type TooltipPosition = 'top' | 'bottom' | 'left' | 'right' | 'auto';

// Tooltip trigger types
export type TooltipTrigger = 'press' | 'longPress' | 'hover';

export interface MinimalistTooltipProps {
  // Content
  content: string | React.ReactNode;
  title?: string;
  
  // Trigger element
  children: React.ReactNode;
  
  // Behavior
  trigger?: TooltipTrigger;
  position?: TooltipPosition;
  disabled?: boolean;
  
  // Styling
  maxWidth?: number;
  backgroundColor?: string;
  textColor?: string;
  
  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  
  // Callbacks
  onShow?: () => void;
  onHide?: () => void;
}

export const MinimalistTooltip: React.FC<MinimalistTooltipProps> = ({
  content,
  title,
  children,
  trigger = 'press',
  position = 'auto',
  disabled = false,
  maxWidth = 280,
  backgroundColor,
  textColor,
  accessibilityLabel,
  accessibilityHint,
  onShow,
  onHide,
}) => {
  const { isDark } = useTheme();
  const theme = HyperMinimalistTheme;
  const colors = isDark ? theme.darkColors : theme.colors;
  
  const [isVisible, setIsVisible] = useState(false);
  const [tooltipLayout, setTooltipLayout] = useState({ width: 0, height: 0 });
  const [triggerLayout, setTriggerLayout] = useState({ x: 0, y: 0, width: 0, height: 0 });
  
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const triggerRef = useRef<View>(null);

  const showTooltip = () => {
    if (disabled) return;
    
    // Measure trigger element position
    triggerRef.current?.measure((x, y, width, height, pageX, pageY) => {
      setTriggerLayout({ x: pageX, y: pageY, width, height });
    });
    
    setIsVisible(true);
    onShow?.();
    
    // Animate in
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
    
    // Announce for screen readers
    if (typeof content === 'string') {
      AccessibilityUtils.ScreenReaderUtils.announceForAccessibility(`Tooltip: ${content}`);
    }
  };

  const hideTooltip = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.8,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsVisible(false);
      onHide?.();
    });
  };

  // Calculate tooltip position
  const calculatePosition = () => {
    const screenWidth = Dimensions.get('window').width;
    const screenHeight = Dimensions.get('window').height;
    const padding = 16;
    
    let tooltipX = triggerLayout.x + (triggerLayout.width / 2) - (tooltipLayout.width / 2);
    let tooltipY = triggerLayout.y - tooltipLayout.height - 8;
    
    // Auto-adjust position if it goes off screen
    if (position === 'auto' || position === 'top') {
      // Check if tooltip goes off top of screen
      if (tooltipY < padding) {
        tooltipY = triggerLayout.y + triggerLayout.height + 8; // Show below
      }
    }
    
    if (position === 'bottom') {
      tooltipY = triggerLayout.y + triggerLayout.height + 8;
    }
    
    // Ensure tooltip doesn't go off sides of screen
    if (tooltipX < padding) {
      tooltipX = padding;
    } else if (tooltipX + tooltipLayout.width > screenWidth - padding) {
      tooltipX = screenWidth - tooltipLayout.width - padding;
    }
    
    return { x: tooltipX, y: tooltipY };
  };

  const tooltipPosition = calculatePosition();
  
  const triggerProps = {
    ref: triggerRef,
    ...(trigger === 'press' && { onPress: showTooltip }),
    ...(trigger === 'longPress' && { onLongPress: showTooltip }),
    ...(Platform.OS === 'web' && trigger === 'hover' && {
      onMouseEnter: showTooltip,
      onMouseLeave: hideTooltip,
    }),
    accessibilityLabel: accessibilityLabel || 'Show more information',
    accessibilityHint: accessibilityHint || 'Tap to view additional details',
    accessibilityRole: 'button' as const,
  };

  const renderTooltipContent = () => (
    <Animated.View
      style={[
        styles.tooltip,
        {
          backgroundColor: backgroundColor || colors.gray[900],
          maxWidth,
          left: tooltipPosition.x,
          top: tooltipPosition.y,
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
      onLayout={(event) => {
        const { width, height } = event.nativeEvent.layout;
        setTooltipLayout({ width, height });
      }}
    >
      {title && (
        <Text style={[styles.tooltipTitle, { color: textColor || colors.white }]}>
          {title}
        </Text>
      )}
      {typeof content === 'string' ? (
        <Text style={[styles.tooltipText, { color: textColor || colors.white }]}>
          {content}
        </Text>
      ) : (
        content
      )}
    </Animated.View>
  );

  return (
    <>
      <TouchableOpacity {...triggerProps} activeOpacity={0.7}>
        {children}
      </TouchableOpacity>
      
      <Modal
        visible={isVisible}
        transparent
        animationType="none"
        onRequestClose={hideTooltip}
      >
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={hideTooltip}
          accessibilityLabel="Close tooltip"
          accessibilityRole="button"
        >
          {renderTooltipContent()}
        </TouchableOpacity>
      </Modal>
    </>
  );
};

// Helper component for info icon tooltips
export const InfoTooltip: React.FC<{
  content: string;
  title?: string;
  size?: number;
  color?: string;
}> = ({ content, title, size = 16, color }) => {
  const { isDark } = useTheme();
  const theme = HyperMinimalistTheme;
  const colors = isDark ? theme.darkColors : theme.colors;
  
  return (
    <MinimalistTooltip content={content} title={title}>
      <Ionicons
        name="information-circle-outline"
        size={size}
        color={color || colors.gray[500]}
        style={styles.infoIcon}
      />
    </MinimalistTooltip>
  );
};

// Helper component for learn more links
export const LearnMoreLink: React.FC<{
  content: string;
  title?: string;
  linkText?: string;
}> = ({ content, title, linkText = "Learn more" }) => {
  const { isDark } = useTheme();
  const theme = HyperMinimalistTheme;
  const colors = isDark ? theme.darkColors : theme.colors;
  
  return (
    <MinimalistTooltip content={content} title={title}>
      <Text style={[styles.learnMoreText, { color: colors.primary[500] }]}>
        {linkText}
      </Text>
    </MinimalistTooltip>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  tooltip: {
    position: 'absolute',
    padding: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    zIndex: 1000,
  },
  tooltipTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  tooltipText: {
    fontSize: 13,
    lineHeight: 18,
  },
  infoIcon: {
    marginLeft: 4,
  },
  learnMoreText: {
    fontSize: 13,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
});
