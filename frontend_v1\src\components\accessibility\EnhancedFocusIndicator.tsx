/**
 * Enhanced Focus Indicator System
 * 
 * Provides WCAG 2.2 AA compliant focus indicators that are never obscured
 * by other content and maintain high contrast in all contexts.
 * 
 * Features:
 * - High contrast focus indicators (minimum 3:1 ratio)
 * - Never obscured by other content (z-index management)
 * - Smooth animations and transitions
 * - Multiple indicator styles
 * - Automatic contrast adjustment
 * - Screen reader announcements
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useRef, useEffect, useState } from 'react';
import {
  View,
  Animated,
  StyleSheet,
  Platform,
  Dimensions,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { HyperMinimalistTheme } from '../../design-system/HyperMinimalistTheme';
import { accessibilityUtils } from '../../utils/accessibilityUtils';

// Focus indicator styles
export type FocusIndicatorStyle = 'outline' | 'glow' | 'border' | 'shadow' | 'combined';

// Focus indicator configuration
export interface FocusIndicatorConfig {
  style: FocusIndicatorStyle;
  color?: string;
  width?: number;
  offset?: number;
  highContrast?: boolean;
  preventObscure?: boolean;
  animated?: boolean;
  announceChanges?: boolean;
}

// Default configuration
const DEFAULT_CONFIG: FocusIndicatorConfig = {
  style: 'combined',
  width: 3,
  offset: 2,
  highContrast: true,
  preventObscure: true,
  animated: true,
  announceChanges: true,
};

export interface EnhancedFocusIndicatorProps {
  visible: boolean;
  target?: any;
  config?: Partial<FocusIndicatorConfig>;
  style?: any;
  onFocusChange?: (focused: boolean) => void;
}

/**
 * Enhanced Focus Indicator Component
 */
export const EnhancedFocusIndicator: React.FC<EnhancedFocusIndicatorProps> = ({
  visible,
  target,
  config = {},
  style,
  onFocusChange,
}) => {
  const { isDark } = useTheme();
  const theme = HyperMinimalistTheme;
  const colors = isDark ? theme.darkColors : theme.colors;
  
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  // Animation values
  const animatedOpacity = useRef(new Animated.Value(0)).current;
  const animatedScale = useRef(new Animated.Value(0.95)).current;
  const animatedGlow = useRef(new Animated.Value(0)).current;
  
  // State
  const [indicatorColor, setIndicatorColor] = useState<string>('');

  // Calculate high contrast color
  useEffect(() => {
    const backgroundColor = isDark ? colors.gray[900] : colors.white;
    const baseColor = finalConfig.color || colors.primary[500];
    
    if (finalConfig.highContrast) {
      // Ensure minimum 3:1 contrast ratio
      const contrastColor = accessibilityUtils.getWCAGCompliantColor(
        baseColor,
        backgroundColor,
        'AA'
      );
      setIndicatorColor(contrastColor);
    } else {
      setIndicatorColor(baseColor);
    }
  }, [isDark, finalConfig.color, finalConfig.highContrast]);

  // Handle visibility changes
  useEffect(() => {
    if (finalConfig.animated) {
      if (visible) {
        // Animate in
        Animated.parallel([
          Animated.timing(animatedOpacity, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.spring(animatedScale, {
            toValue: 1,
            tension: 300,
            friction: 20,
            useNativeDriver: true,
          }),
          Animated.loop(
            Animated.sequence([
              Animated.timing(animatedGlow, {
                toValue: 1,
                duration: 1000,
                useNativeDriver: true,
              }),
              Animated.timing(animatedGlow, {
                toValue: 0.7,
                duration: 1000,
                useNativeDriver: true,
              }),
            ])
          ),
        ]).start();
      } else {
        // Animate out
        Animated.parallel([
          Animated.timing(animatedOpacity, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true,
          }),
          Animated.timing(animatedScale, {
            toValue: 0.95,
            duration: 150,
            useNativeDriver: true,
          }),
        ]).start();
        
        animatedGlow.stopAnimation();
        animatedGlow.setValue(0);
      }
    } else {
      animatedOpacity.setValue(visible ? 1 : 0);
      animatedScale.setValue(visible ? 1 : 0.95);
    }

    // Announce focus changes
    if (finalConfig.announceChanges && Platform.OS === 'web') {
      if (visible) {
        accessibilityUtils.announceForScreenReader('Element focused');
      }
    }

    // Callback
    onFocusChange?.(visible);
  }, [visible, finalConfig.animated, finalConfig.announceChanges]);

  // Generate styles based on configuration
  const getIndicatorStyles = () => {
    const baseStyles = {
      position: 'absolute' as const,
      top: -finalConfig.offset!,
      left: -finalConfig.offset!,
      right: -finalConfig.offset!,
      bottom: -finalConfig.offset!,
      borderRadius: 8,
      // Ensure indicator is never obscured
      zIndex: finalConfig.preventObscure ? 9999 : 1,
      elevation: finalConfig.preventObscure ? 9999 : 1,
    };

    switch (finalConfig.style) {
      case 'outline':
        return {
          ...baseStyles,
          borderWidth: finalConfig.width,
          borderColor: indicatorColor,
          borderStyle: 'solid' as const,
        };

      case 'glow':
        return {
          ...baseStyles,
          shadowColor: indicatorColor,
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.8,
          shadowRadius: finalConfig.width! * 2,
          ...(Platform.OS === 'web' && {
            boxShadow: `0 0 ${finalConfig.width! * 4}px ${indicatorColor}`,
          }),
        };

      case 'border':
        return {
          ...baseStyles,
          borderWidth: finalConfig.width,
          borderColor: indicatorColor,
        };

      case 'shadow':
        return {
          ...baseStyles,
          shadowColor: indicatorColor,
          shadowOffset: { width: 2, height: 2 },
          shadowOpacity: 0.6,
          shadowRadius: finalConfig.width,
          ...(Platform.OS === 'web' && {
            boxShadow: `2px 2px ${finalConfig.width! * 2}px ${indicatorColor}`,
          }),
        };

      case 'combined':
      default:
        return {
          ...baseStyles,
          borderWidth: finalConfig.width,
          borderColor: indicatorColor,
          shadowColor: indicatorColor,
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.5,
          shadowRadius: finalConfig.width! * 1.5,
          ...(Platform.OS === 'web' && {
            boxShadow: `0 0 ${finalConfig.width! * 3}px ${indicatorColor}`,
            outline: `${finalConfig.width}px solid ${indicatorColor}`,
            outlineOffset: `${finalConfig.offset}px`,
          }),
        };
    }
  };

  if (!visible) return null;

  return (
    <Animated.View
      style={[
        getIndicatorStyles(),
        {
          opacity: animatedOpacity,
          transform: [{ scale: animatedScale }],
        },
        finalConfig.style === 'glow' && {
          opacity: animatedGlow,
        },
        style,
      ]}
      pointerEvents="none"
      accessibilityElementsHidden={true}
      importantForAccessibility="no-hide-descendants"
    />
  );
};

/**
 * Focus Indicator Hook
 * Provides easy integration with focus management
 */
export const useFocusIndicator = (config?: Partial<FocusIndicatorConfig>) => {
  const [isFocused, setIsFocused] = useState(false);
  const [target, setTarget] = useState<any>(null);

  const focusProps = {
    onFocus: () => setIsFocused(true),
    onBlur: () => setIsFocused(false),
    ref: setTarget,
  };

  const FocusIndicatorComponent = () => (
    <EnhancedFocusIndicator
      visible={isFocused}
      target={target}
      config={config}
    />
  );

  return {
    isFocused,
    focusProps,
    FocusIndicatorComponent,
    setFocused: setIsFocused,
  };
};

/**
 * Global Focus Indicator Manager
 * Manages focus indicators across the entire application
 */
export class GlobalFocusIndicatorManager {
  private activeIndicators: Map<string, boolean> = new Map();
  private config: FocusIndicatorConfig = DEFAULT_CONFIG;

  updateGlobalConfig(newConfig: Partial<FocusIndicatorConfig>) {
    this.config = { ...this.config, ...newConfig };
  }

  registerIndicator(id: string) {
    this.activeIndicators.set(id, false);
  }

  setIndicatorVisible(id: string, visible: boolean) {
    this.activeIndicators.set(id, visible);
  }

  getIndicatorState(id: string): boolean {
    return this.activeIndicators.get(id) || false;
  }

  clearAllIndicators() {
    this.activeIndicators.forEach((_, id) => {
      this.activeIndicators.set(id, false);
    });
  }

  getGlobalConfig(): FocusIndicatorConfig {
    return this.config;
  }
}

// Global instance
export const globalFocusIndicatorManager = new GlobalFocusIndicatorManager();

// Styles for web platform
const webStyles = Platform.OS === 'web' ? StyleSheet.create({
  // Global CSS for focus indicators
  focusVisible: {
    outline: 'none', // Remove default outline
  },
}) : {};

export { webStyles as focusIndicatorWebStyles };
