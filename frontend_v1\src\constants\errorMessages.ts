/**
 * Centralized Error Messages - Constructive & Solution-Oriented
 * 
 * This file contains all user-facing error messages that follow the principle
 * of being constructive, solution-oriented, and empathetic.
 * 
 * Message Structure: [ACKNOWLEDGMENT] + [PROBLEM] + [SOLUTION] + [ALTERNATIVE]
 * 
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

// Connection & Network Errors
export const CONNECTION_ERRORS = {
  OFFLINE: "You're currently offline. Please check your internet connection and try again.",
  TIMEOUT: "This is taking longer than usual. Please check your connection and try again.",
  SERVER_DOWN: "Our servers are temporarily unavailable. Please try again in a few minutes.",
  SLOW_CONNECTION: "Your connection seems slow. Please wait a moment or try again with a better connection.",
  API_UNREACHABLE: "We couldn't connect to our servers. Please check your internet connection and try again.",
} as const;

// Authentication Errors
export const AUTH_ERRORS = {
  INVALID_CREDENTIALS: "We couldn't sign you in with those credentials. Please double-check your email and password, or use \"Forgot Password\" if you need to reset it.",
  SESSION_EXPIRED: "Your session has expired for security. Please sign in again to continue where you left off.",
  TOKEN_INVALID: "We couldn't verify your identity. Please sign in again, or contact support if this keeps happening.",
  BIOMETRIC_UNAVAILABLE: "Biometric sign-in isn't available on this device. Please use your email and password instead.",
  BIOMETRIC_NOT_ENROLLED: "No fingerprint or face ID is set up on this device. Please set one up in your device settings or use your password.",
  PIN_INCORRECT: "That PIN isn't correct. Please try again or use \"Forgot PIN\" if you need help.",
  PIN_LOCKED: "Too many incorrect attempts. Please wait a few minutes and try again, or use \"Forgot PIN\" to reset it.",
  ACCOUNT_LOCKED: "Your account has been temporarily locked for security. Please contact support or try again later.",
} as const;

// Form Validation Errors
export const VALIDATION_ERRORS = {
  REQUIRED: (fieldName: string) => `Please fill in your ${fieldName} to continue`,
  EMAIL_FORMAT: "Please enter your email in <NAME_EMAIL>",
  EMAIL_REQUIRED: "Please enter your email address to continue",
  PASSWORD_TOO_SHORT: (minLength: number, currentLength: number) => 
    `Your password needs at least ${minLength} characters. Please add ${minLength - currentLength} more character${minLength - currentLength > 1 ? 's' : ''}.`,
  PASSWORD_MISSING_UPPERCASE: "Your password needs at least one uppercase letter (A-Z). Please add one to continue.",
  PASSWORD_MISSING_LOWERCASE: "Your password needs at least one lowercase letter (a-z). Please add one to continue.",
  PASSWORD_MISSING_NUMBER: "Your password needs at least one number (0-9). Please add one to continue.",
  PASSWORD_MISSING_SPECIAL: "Your password needs at least one special character (!@#$%). Please add one to continue.",
  PHONE_FORMAT: "Please enter your phone number with area code (e.g., ************)",
  NAME_FORMAT: "Please enter your name using only letters, spaces, hyphens, and apostrophes (2-50 characters)",
  PASSWORDS_DONT_MATCH: "Your passwords don't match. Please make sure both password fields are identical.",
} as const;

// Business Logic Errors
export const BUSINESS_ERRORS = {
  BOOKING_CONFLICT: "This time slot is no longer available. Please choose a different time or check for other available slots.",
  BOOKING_FAILED: "We couldn't complete your booking right now. Your information is saved safely, and you can try again.",
  PAYMENT_DECLINED: "Your payment was declined. Please check your card details or try a different payment method.",
  PAYMENT_FAILED: "Your payment couldn't be processed. No charges were made to your account. Please try a different payment method.",
  SERVICE_UNAVAILABLE: "This service isn't available in your area yet. Please try a different location or browse other services.",
  PROVIDER_BUSY: "This provider is currently unavailable. Please try booking with another provider or check back later.",
  SLOT_TAKEN: "Someone just booked this time slot. Please choose another available time.",
} as const;

// Search & Discovery Errors
export const SEARCH_ERRORS = {
  NO_RESULTS: "No services match your search. Try different keywords, expand your location range, or browse our categories.",
  SEARCH_FAILED: "We couldn't search right now. Please check your internet connection and try again.",
  LOCATION_NOT_FOUND: "We couldn't find that location. Please check the spelling, try a nearby city, or use your current location.",
  FILTERS_TOO_RESTRICTIVE: "Your filters are very specific and no services match. Try removing some filters or expanding your search area.",
  LOCATION_PERMISSION_DENIED: "We need location access to find services near you. Please enable location in your device settings.",
} as const;

// System & Technical Errors
export const SYSTEM_ERRORS = {
  GENERIC: "Something unexpected happened. Please try again, and contact support if this continues.",
  PAGE_NOT_WORKING: "This page isn't working right now. Please go back and try again, or restart the app if the problem continues.",
  APP_NEEDS_RESTART: "The app needs to recover from an error. Please restart the app to continue safely.",
  FEATURE_UNAVAILABLE: "This feature isn't available right now. Please try again later or contact support.",
  UPDATE_REQUIRED: "Please update the app to continue using this feature. You can find the update in your app store.",
} as const;

// Storage & Data Errors
export const STORAGE_ERRORS = {
  SAVE_FAILED: "We couldn't save your changes. Please check your device storage space and try again.",
  LOAD_FAILED: "We couldn't load your information. Please check your internet connection and pull down to refresh.",
  SYNC_FAILED: "We couldn't sync your data. Your changes are saved locally and will sync when connection improves.",
  STORAGE_FULL: "Your device storage is full. Please free up some space and try again.",
  PERMISSION_DENIED: "We need storage permission to save your data. Please enable it in your device settings.",
} as const;

// Component-Specific Errors
export const COMPONENT_ERRORS = {
  IMAGE_LOAD_FAILED: "We couldn't load this image. Please check your connection and try again.",
  VIDEO_LOAD_FAILED: "We couldn't load this video. Please check your connection and try again.",
  MAP_LOAD_FAILED: "We couldn't load the map. Please check your connection and try again.",
  CAMERA_UNAVAILABLE: "Camera isn't available right now. Please check your device permissions and try again.",
  MICROPHONE_UNAVAILABLE: "Microphone isn't available right now. Please check your device permissions and try again.",
} as const;

// Recovery Action Messages
export const RECOVERY_ACTIONS = {
  TRY_AGAIN: "Try Again",
  GO_BACK: "Go Back",
  RESTART_APP: "Restart App",
  CONTACT_SUPPORT: "Contact Support",
  CHECK_CONNECTION: "Check Connection",
  REFRESH_PAGE: "Refresh Page",
  CLEAR_CACHE: "Clear Cache",
  UPDATE_APP: "Update App",
  ENABLE_PERMISSIONS: "Enable Permissions",
  FREE_STORAGE: "Free Up Storage",
} as const;

// Success Recovery Messages
export const RECOVERY_SUCCESS = {
  CONNECTION_RESTORED: "Great! Your connection is back. You can continue where you left off.",
  AUTH_RESTORED: "Welcome back! You're signed in and ready to continue.",
  SYNC_COMPLETED: "All your data is now synced and up to date.",
  PAYMENT_PROCESSED: "Perfect! Your payment went through successfully.",
  BOOKING_CONFIRMED: "Excellent! Your booking is confirmed and you'll receive a confirmation email.",
} as const;

// Helper function to get contextual error message
export const getContextualErrorMessage = (
  errorType: string,
  context?: { fieldName?: string; minLength?: number; currentLength?: number }
): string => {
  switch (errorType) {
    case 'required':
      return context?.fieldName 
        ? VALIDATION_ERRORS.REQUIRED(context.fieldName)
        : 'Please fill in this field to continue';
    
    case 'password_too_short':
      return context?.minLength && context?.currentLength
        ? VALIDATION_ERRORS.PASSWORD_TOO_SHORT(context.minLength, context.currentLength)
        : 'Your password is too short. Please add more characters.';
    
    default:
      return SYSTEM_ERRORS.GENERIC;
  }
};

// Error severity levels for appropriate handling
export enum ErrorSeverity {
  LOW = 'low',        // Validation errors, minor issues
  MEDIUM = 'medium',  // Network errors, business logic errors
  HIGH = 'high',      // Authentication errors, payment failures
  CRITICAL = 'critical' // System errors, app crashes
}

// Error categories for analytics and handling
export enum ErrorCategory {
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  NETWORK = 'network',
  BUSINESS = 'business',
  SYSTEM = 'system',
  STORAGE = 'storage',
  COMPONENT = 'component'
}
