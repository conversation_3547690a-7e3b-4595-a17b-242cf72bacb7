/**
 * Request Optimization & Deduplication System
 * 
 * This module provides intelligent request optimization including deduplication,
 * caching, and performance monitoring for network requests.
 * 
 * Features:
 * - Request deduplication to prevent duplicate API calls
 * - Intelligent caching with TTL and invalidation
 * - Request batching for efficiency
 * - Performance monitoring and analytics
 * - Offline-first caching strategies
 * 
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

// Request cache interface
interface CachedRequest {
  data: any;
  timestamp: number;
  ttl: number;
  etag?: string;
  lastModified?: string;
}

// Request deduplication interface
interface PendingRequest {
  promise: Promise<any>;
  timestamp: number;
  abortController: AbortController;
}

// Performance metrics interface
interface RequestMetrics {
  url: string;
  method: string;
  duration: number;
  cacheHit: boolean;
  deduplicated: boolean;
  size: number;
  timestamp: number;
}

class RequestOptimizer {
  private cache = new Map<string, CachedRequest>();
  private pendingRequests = new Map<string, PendingRequest>();
  private metrics: RequestMetrics[] = [];
  private batchQueue = new Map<string, any[]>();
  private batchTimeout: NodeJS.Timeout | null = null;

  // Configuration
  private config = {
    defaultTTL: 5 * 60 * 1000, // 5 minutes
    maxCacheSize: 100,
    batchDelay: 50, // ms
    enableMetrics: true,
    enableDeduplication: true,
    enableCaching: true,
  };

  /**
   * Optimized fetch with deduplication and caching
   */
  async fetch(url: string, options: RequestInit = {}): Promise<any> {
    const startTime = performance.now();
    const method = options.method || 'GET';
    const cacheKey = this.generateCacheKey(url, options);
    
    try {
      // Check for cached response first
      if (this.config.enableCaching && method === 'GET') {
        const cached = this.getCachedResponse(cacheKey);
        if (cached) {
          this.recordMetrics(url, method, performance.now() - startTime, true, false, 0);
          return cached;
        }
      }

      // Check for pending duplicate request
      if (this.config.enableDeduplication) {
        const pending = this.pendingRequests.get(cacheKey);
        if (pending) {
          this.recordMetrics(url, method, performance.now() - startTime, false, true, 0);
          return pending.promise;
        }
      }

      // Create new request
      const abortController = new AbortController();
      const requestOptions = {
        ...options,
        signal: abortController.signal,
      };

      const requestPromise = this.executeRequest(url, requestOptions);
      
      // Store pending request for deduplication
      if (this.config.enableDeduplication) {
        this.pendingRequests.set(cacheKey, {
          promise: requestPromise,
          timestamp: Date.now(),
          abortController,
        });
      }

      const response = await requestPromise;
      
      // Cache successful GET responses
      if (this.config.enableCaching && method === 'GET' && response) {
        this.cacheResponse(cacheKey, response, options);
      }

      // Clean up pending request
      this.pendingRequests.delete(cacheKey);
      
      const responseSize = this.estimateResponseSize(response);
      this.recordMetrics(url, method, performance.now() - startTime, false, false, responseSize);
      
      return response;
    } catch (error) {
      // Clean up on error
      this.pendingRequests.delete(cacheKey);
      throw error;
    }
  }

  /**
   * Execute the actual HTTP request
   */
  private async executeRequest(url: string, options: RequestInit): Promise<any> {
    const response = await fetch(url, options);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return response.json();
    }
    
    return response.text();
  }

  /**
   * Generate cache key for request
   */
  private generateCacheKey(url: string, options: RequestInit): string {
    const method = options.method || 'GET';
    const body = options.body ? JSON.stringify(options.body) : '';
    const headers = options.headers ? JSON.stringify(options.headers) : '';
    
    return `${method}:${url}:${body}:${headers}`;
  }

  /**
   * Get cached response if valid
   */
  private getCachedResponse(cacheKey: string): any | null {
    const cached = this.cache.get(cacheKey);
    
    if (!cached) {
      return null;
    }
    
    // Check if cache is expired
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(cacheKey);
      return null;
    }
    
    return cached.data;
  }

  /**
   * Cache response data
   */
  private cacheResponse(cacheKey: string, data: any, options: RequestInit): void {
    // Implement cache size limit
    if (this.cache.size >= this.config.maxCacheSize) {
      // Remove oldest entries
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const toRemove = Math.floor(this.config.maxCacheSize * 0.2); // Remove 20%
      for (let i = 0; i < toRemove; i++) {
        this.cache.delete(entries[i][0]);
      }
    }
    
    const ttl = this.getTTLForRequest(options);
    
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  /**
   * Get TTL for specific request
   */
  private getTTLForRequest(options: RequestInit): number {
    // Custom TTL logic based on request type
    const url = (options as any).url || '';
    
    if (url.includes('/user/profile')) {
      return 10 * 60 * 1000; // 10 minutes for profile data
    }
    
    if (url.includes('/services/search')) {
      return 2 * 60 * 1000; // 2 minutes for search results
    }
    
    return this.config.defaultTTL;
  }

  /**
   * Estimate response size for metrics
   */
  private estimateResponseSize(data: any): number {
    try {
      return JSON.stringify(data).length;
    } catch {
      return 0;
    }
  }

  /**
   * Record performance metrics
   */
  private recordMetrics(
    url: string,
    method: string,
    duration: number,
    cacheHit: boolean,
    deduplicated: boolean,
    size: number
  ): void {
    if (!this.config.enableMetrics) return;
    
    this.metrics.push({
      url,
      method,
      duration,
      cacheHit,
      deduplicated,
      size,
      timestamp: Date.now(),
    });
    
    // Keep only last 1000 metrics
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
    
    // Log slow requests
    if (duration > 1000) {
      console.warn(`[Performance] Slow request: ${method} ${url} took ${duration.toFixed(2)}ms`);
    }
  }

  /**
   * Batch multiple requests together
   */
  async batchRequests(requests: Array<{ url: string; options?: RequestInit }>): Promise<any[]> {
    const batchKey = 'batch_' + Date.now();
    
    return new Promise((resolve, reject) => {
      this.batchQueue.set(batchKey, requests);
      
      if (this.batchTimeout) {
        clearTimeout(this.batchTimeout);
      }
      
      this.batchTimeout = setTimeout(async () => {
        try {
          const batchedRequests = this.batchQueue.get(batchKey) || [];
          this.batchQueue.delete(batchKey);
          
          const results = await Promise.allSettled(
            batchedRequests.map(req => this.fetch(req.url, req.options))
          );
          
          resolve(results.map(result => 
            result.status === 'fulfilled' ? result.value : null
          ));
        } catch (error) {
          reject(error);
        }
      }, this.config.batchDelay);
    });
  }

  /**
   * Invalidate cache entries
   */
  invalidateCache(pattern?: string): void {
    if (!pattern) {
      this.cache.clear();
      return;
    }
    
    const regex = new RegExp(pattern);
    for (const [key] of this.cache) {
      if (regex.test(key)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats(): {
    totalRequests: number;
    cacheHitRate: number;
    deduplicationRate: number;
    averageResponseTime: number;
    slowRequests: number;
  } {
    if (this.metrics.length === 0) {
      return {
        totalRequests: 0,
        cacheHitRate: 0,
        deduplicationRate: 0,
        averageResponseTime: 0,
        slowRequests: 0,
      };
    }
    
    const cacheHits = this.metrics.filter(m => m.cacheHit).length;
    const deduplicated = this.metrics.filter(m => m.deduplicated).length;
    const totalDuration = this.metrics.reduce((sum, m) => sum + m.duration, 0);
    const slowRequests = this.metrics.filter(m => m.duration > 1000).length;
    
    return {
      totalRequests: this.metrics.length,
      cacheHitRate: (cacheHits / this.metrics.length) * 100,
      deduplicationRate: (deduplicated / this.metrics.length) * 100,
      averageResponseTime: totalDuration / this.metrics.length,
      slowRequests,
    };
  }

  /**
   * Configure the optimizer
   */
  configure(newConfig: Partial<typeof this.config>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// Global instance
export const requestOptimizer = new RequestOptimizer();

// Convenience functions
export const optimizedFetch = (url: string, options?: RequestInit) => 
  requestOptimizer.fetch(url, options);

export const batchRequests = (requests: Array<{ url: string; options?: RequestInit }>) =>
  requestOptimizer.batchRequests(requests);

export const invalidateCache = (pattern?: string) =>
  requestOptimizer.invalidateCache(pattern);

export const getRequestStats = () =>
  requestOptimizer.getPerformanceStats();

export default requestOptimizer;
