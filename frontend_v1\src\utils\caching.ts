/**
 * Caching Utilities - Performance Optimization
 *
 * Component Contract:
 * - Provides comprehensive caching strategies for API responses
 * - Implements memory and persistent storage caching
 * - Handles cache invalidation and expiration
 * - Provides cache warming and preloading strategies
 * - Optimizes network requests and data access
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  expiresAt: number;
  version: string;
  tags?: string[];
}

interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  version?: string;
  tags?: string[];
  persistent?: boolean; // Whether to persist to AsyncStorage
}

interface CacheStats {
  hits: number;
  misses: number;
  size: number;
  hitRate: number;
}

/**
 * Memory cache implementation
 */
class MemoryCache {
  private cache = new Map<string, CacheItem>();
  private maxSize: number;
  private stats = { hits: 0, misses: 0 };

  constructor(maxSize: number = 100) {
    this.maxSize = maxSize;
  }

  set<T>(key: string, data: T, options: CacheOptions = {}): void {
    const {
      ttl = 5 * 60 * 1000, // 5 minutes default
      version = '1.0',
      tags = [],
    } = options;

    // Remove oldest items if cache is full
    if (this.cache.size >= this.maxSize) {
      this.evictOldest();
    }

    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + ttl,
      version,
      tags,
    };

    this.cache.set(key, item);
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);

    if (!item) {
      this.stats.misses++;
      return null;
    }

    // Check if expired
    if (Date.now() > item.expiresAt) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }

    this.stats.hits++;
    return item.data as T;
  }

  has(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;

    // Check if expired
    if (Date.now() > item.expiresAt) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
    this.stats = { hits: 0, misses: 0 };
  }

  invalidateByTag(tag: string): number {
    let invalidated = 0;
    for (const [key, item] of this.cache.entries()) {
      if (item.tags?.includes(tag)) {
        this.cache.delete(key);
        invalidated++;
      }
    }
    return invalidated;
  }

  private evictOldest(): void {
    let oldestKey: string | null = null;
    let oldestTimestamp = Date.now();

    for (const [key, item] of this.cache.entries()) {
      if (item.timestamp < oldestTimestamp) {
        oldestTimestamp = item.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  getStats(): CacheStats {
    const total = this.stats.hits + this.stats.misses;
    return {
      ...this.stats,
      size: this.cache.size,
      hitRate: total > 0 ? this.stats.hits / total : 0,
    };
  }
}

/**
 * Persistent cache implementation using AsyncStorage
 */
class PersistentCache {
  private prefix: string;

  constructor(prefix: string = 'vierla_cache_') {
    this.prefix = prefix;
  }

  async set<T>(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    const {
      ttl = 24 * 60 * 60 * 1000, // 24 hours default for persistent cache
      version = '1.0',
      tags = [],
    } = options;

    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + ttl,
      version,
      tags,
    };

    try {
      await AsyncStorage.setItem(
        `${this.prefix}${key}`,
        JSON.stringify(item)
      );
    } catch (error) {
      if (__DEV__) {
        console.warn('[PersistentCache] Failed to set item:', error);
      }
    }
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const itemString = await AsyncStorage.getItem(`${this.prefix}${key}`);
      if (!itemString) return null;

      const item: CacheItem<T> = JSON.parse(itemString);

      // Check if expired
      if (Date.now() > item.expiresAt) {
        await this.delete(key);
        return null;
      }

      return item.data;
    } catch (error) {
      if (__DEV__) {
        console.warn('[PersistentCache] Failed to get item:', error);
      }
      return null;
    }
  }

  async has(key: string): Promise<boolean> {
    try {
      const itemString = await AsyncStorage.getItem(`${this.prefix}${key}`);
      if (!itemString) return false;

      const item: CacheItem = JSON.parse(itemString);

      // Check if expired
      if (Date.now() > item.expiresAt) {
        await this.delete(key);
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  async delete(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(`${this.prefix}${key}`);
    } catch (error) {
      if (__DEV__) {
        console.warn('[PersistentCache] Failed to delete item:', error);
      }
    }
  }

  async clear(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(this.prefix));
      await AsyncStorage.multiRemove(cacheKeys);
    } catch (error) {
      if (__DEV__) {
        console.warn('[PersistentCache] Failed to clear cache:', error);
      }
    }
  }

  async invalidateByTag(tag: string): Promise<number> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(this.prefix));
      let invalidated = 0;

      for (const key of cacheKeys) {
        const itemString = await AsyncStorage.getItem(key);
        if (itemString) {
          const item: CacheItem = JSON.parse(itemString);
          if (item.tags?.includes(tag)) {
            await AsyncStorage.removeItem(key);
            invalidated++;
          }
        }
      }

      return invalidated;
    } catch (error) {
      if (__DEV__) {
        console.warn('[PersistentCache] Failed to invalidate by tag:', error);
      }
      return 0;
    }
  }
}

/**
 * Unified cache manager
 */
export class CacheManager {
  private memoryCache: MemoryCache;
  private persistentCache: PersistentCache;

  constructor(maxMemorySize: number = 100) {
    this.memoryCache = new MemoryCache(maxMemorySize);
    this.persistentCache = new PersistentCache();
  }

  async set<T>(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    // Always set in memory cache
    this.memoryCache.set(key, data, options);

    // Set in persistent cache if requested
    if (options.persistent) {
      await this.persistentCache.set(key, data, options);
    }
  }

  async get<T>(key: string): Promise<T | null> {
    // Try memory cache first
    let data = this.memoryCache.get<T>(key);
    if (data !== null) {
      return data;
    }

    // Try persistent cache
    data = await this.persistentCache.get<T>(key);
    if (data !== null) {
      // Warm memory cache
      this.memoryCache.set(key, data);
      return data;
    }

    return null;
  }

  async has(key: string): Promise<boolean> {
    return this.memoryCache.has(key) || await this.persistentCache.has(key);
  }

  async delete(key: string): Promise<void> {
    this.memoryCache.delete(key);
    await this.persistentCache.delete(key);
  }

  async clear(): Promise<void> {
    this.memoryCache.clear();
    await this.persistentCache.clear();
  }

  async invalidateByTag(tag: string): Promise<number> {
    const memoryInvalidated = this.memoryCache.invalidateByTag(tag);
    const persistentInvalidated = await this.persistentCache.invalidateByTag(tag);
    return memoryInvalidated + persistentInvalidated;
  }

  getStats(): CacheStats {
    return this.memoryCache.getStats();
  }
}

// Global cache instance
export const globalCache = new CacheManager();

// Cache utilities
export const cacheUtils = {
  /**
   * Create a cache key from multiple parts
   */
  createKey: (...parts: (string | number)[]): string => {
    return parts.join(':');
  },

  /**
   * Cache a function result
   */
  memoize: <T extends (...args: any[]) => any>(
    fn: T,
    keyFn?: (...args: Parameters<T>) => string,
    options?: CacheOptions
  ) => {
    return async (...args: Parameters<T>): Promise<ReturnType<T>> => {
      const key = keyFn ? keyFn(...args) : JSON.stringify(args);
      
      let result = await globalCache.get<ReturnType<T>>(key);
      if (result !== null) {
        return result;
      }

      result = await fn(...args);
      await globalCache.set(key, result, options);
      return result;
    };
  },
};
