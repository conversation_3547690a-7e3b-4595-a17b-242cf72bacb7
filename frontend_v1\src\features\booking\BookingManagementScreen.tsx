/**
 * Booking Management Screen - Comprehensive booking management interface
 *
 * Screen Contract:
 * - Display all user bookings with filtering and sorting options
 * - Provide booking details, status updates, and action buttons
 * - Support booking cancellation, rescheduling, and modifications
 * - Show booking history and upcoming appointments
 * - Real-time status updates and notifications
 * - Integration with payment and communication systems
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  Alert,
  Dimensions,
  Image,
} from 'react-native';
import { SafeAreaWrapper } from '../../components/ui/SafeAreaWrapper';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import styled from 'styled-components/native';

import { useBookingStore, Booking, BookingStatus } from '../../store/bookingSlice';
import { useAuthStore } from '../../store/authSlice';
import { Colors } from '../../constants/Colors';
import { LoadingSpinner } from '../../components/common/LoadingSpinner';
import { ErrorMessage } from '../../components/common/ErrorMessage';
import { useBookingFeedback } from '../../hooks/useActionFeedbackHooks';
import { useActionFeedback } from '../../components/ui/ActionFeedbackSystem';

const { width } = Dimensions.get('window');

interface BookingCardProps {
  booking: Booking;
  onPress: () => void;
  onCancel: () => void;
  onReschedule: () => void;
  onContact: () => void;
}

const BookingManagementScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAuthStore();
  const {
    bookings,
    upcomingBookings,
    pastBookings,
    isLoading,
    error,
    statusFilter,
    loadBookings,
    loadUpcomingBookings,
    loadPastBookings,
    cancelBooking,
    setStatusFilter,
    clearError,
  } = useBookingStore();

  // Action feedback hooks
  const { cancelBooking: cancelBookingWithFeedback, rescheduleBooking } = useBookingFeedback();
  const { showConfirmation } = useActionFeedback();

  const [activeTab, setActiveTab] = useState<'upcoming' | 'past' | 'all'>('upcoming');
  const [refreshing, setRefreshing] = useState(false);

  useFocusEffect(
    useCallback(() => {
      if (user?.id) {
        loadData();
      }
    }, [user?.id])
  );

  const loadData = async () => {
    if (!user?.id) return;

    try {
      await Promise.all([
        loadBookings(user.id),
        loadUpcomingBookings(user.id),
        loadPastBookings(user.id),
      ]);
    } catch (error) {
      console.error('Failed to load bookings:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadData();
    } catch (error) {
      console.error('Failed to refresh bookings:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleCancelBooking = async (booking: Booking) => {
    try {
      await cancelBookingWithFeedback(booking.id);
      // Refresh the bookings list after successful cancellation
      await loadData();
    } catch (error) {
      console.error('Failed to cancel booking:', error);
    }
  };

  const showCancellationReasons = (booking: Booking) => {
    const reasons = [
      'Schedule conflict',
      'Personal emergency',
      'Illness',
      'Found another provider',
      'Service no longer needed',
      'Other',
    ];

    Alert.alert(
      'Cancellation Reason',
      'Please select a reason for cancellation:',
      [
        ...reasons.map(reason => ({
          text: reason,
          onPress: () => confirmCancellation(booking, reason),
        })),
        { text: 'Back', style: 'cancel' },
      ]
    );
  };

  const confirmCancellation = async (booking: Booking, reason: string) => {
    try {
      // Use feedback system for booking cancellation
      await cancelBookingWithFeedback(booking.id, reason, () => {
        // Reload data on successful cancellation
        loadData();
      });
    } catch (error) {
      // Error handling is managed by the feedback system
      console.error('Failed to cancel booking:', error);
    }
  };

  const handleRescheduleBooking = async (booking: Booking) => {
    // For now, navigate to reschedule screen
    // In a real implementation, this could show a date picker modal
    // and use the rescheduleBooking feedback function
    navigation.navigate('RescheduleBooking' as never, { bookingId: booking.id });

    // Example of using the feedback system for rescheduling:
    // try {
    //   const newDate = new Date(); // This would come from a date picker
    //   await rescheduleBooking(booking.id, newDate);
    //   await loadData();
    // } catch (error) {
    //   console.error('Failed to reschedule booking:', error);
    // }
  };

  const handleContactProvider = (booking: Booking) => {
    navigation.navigate('ProviderChat' as never, { 
      providerId: booking.provider.id,
      bookingId: booking.id,
    });
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-CA', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (timeString: string): string => {
    return timeString;
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  const getStatusColor = (status: BookingStatus): string => {
    switch (status) {
      case 'confirmed':
        return Colors.light.success;
      case 'pending':
        return Colors.light.warning;
      case 'in_progress':
        return Colors.light.info;
      case 'completed':
        return Colors.light.success;
      case 'cancelled':
        return Colors.light.error;
      case 'no_show':
        return Colors.light.error;
      default:
        return Colors.light.textSecondary;
    }
  };

  const getStatusText = (status: BookingStatus): string => {
    switch (status) {
      case 'confirmed':
        return 'Confirmed';
      case 'pending':
        return 'Pending';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      case 'no_show':
        return 'No Show';
      default:
        return status;
    }
  };

  const BookingCard: React.FC<BookingCardProps> = ({
    booking,
    onPress,
    onCancel,
    onReschedule,
    onContact,
  }) => (
    <BookingCardContainer onPress={onPress}>
      <BookingHeader>
        <ProviderInfo>
          {booking.provider.avatar ? (
            <ProviderAvatar source={{ uri: booking.provider.avatar }} />
          ) : (
            <ProviderAvatarPlaceholder>
              <Ionicons name="business-outline" size={20} color={Colors.light.textSecondary} />
            </ProviderAvatarPlaceholder>
          )}
          <ProviderDetails>
            <ProviderName>{booking.provider.businessName}</ProviderName>
            <ServiceName>{booking.service.name}</ServiceName>
          </ProviderDetails>
        </ProviderInfo>
        <StatusBadge status={booking.status}>
          <StatusText status={booking.status}>{getStatusText(booking.status)}</StatusText>
        </StatusBadge>
      </BookingHeader>

      <BookingDetails>
        <DetailRow>
          <DetailIcon name="calendar-outline" size={16} color={Colors.light.primary} />
          <DetailText>{formatDate(booking.appointment.date)}</DetailText>
        </DetailRow>
        <DetailRow>
          <DetailIcon name="time-outline" size={16} color={Colors.light.info} />
          <DetailText>{formatTime(booking.appointment.time)} - {formatTime(booking.appointment.endTime)}</DetailText>
        </DetailRow>
        <DetailRow>
          <DetailIcon name="location-outline" size={16} color={Colors.light.success} />
          <DetailText>{booking.appointment.location.address}</DetailText>
        </DetailRow>
        <DetailRow>
          <DetailIcon name="pricetag-outline" size={16} color={Colors.light.warning} />
          <DetailText>{formatCurrency(booking.payment.amount)}</DetailText>
        </DetailRow>
      </BookingDetails>

      {booking.notes && (
        <NotesContainer>
          <NotesLabel>Notes:</NotesLabel>
          <NotesText>{booking.notes}</NotesText>
        </NotesContainer>
      )}

      <BookingActions>
        <ActionButton onPress={onContact} variant="secondary">
          <Ionicons name="chatbubble-outline" size={16} color={Colors.light.primary} />
          <ActionButtonText variant="secondary">Message</ActionButtonText>
        </ActionButton>
        
        {(booking.status === 'confirmed' || booking.status === 'pending') && (
          <>
            <ActionButton onPress={onReschedule} variant="secondary">
              <Ionicons name="calendar-outline" size={16} color={Colors.light.info} />
              <ActionButtonText variant="secondary">Reschedule</ActionButtonText>
            </ActionButton>
            <ActionButton onPress={onCancel} variant="danger">
              <Ionicons name="close-outline" size={16} color={Colors.light.error} />
              <ActionButtonText variant="danger">Cancel</ActionButtonText>
            </ActionButton>
          </>
        )}
      </BookingActions>
    </BookingCardContainer>
  );

  const getCurrentBookings = () => {
    switch (activeTab) {
      case 'upcoming':
        return upcomingBookings;
      case 'past':
        return pastBookings;
      case 'all':
      default:
        return bookings;
    }
  };

  if (isLoading && bookings.length === 0) {
    return (
      <Container>
        <LoadingSpinner size="large" />
      </Container>
    );
  }

  return (
    <Container>
      <SafeAreaWrapper style={{ flex: 1 }}>
        <Header>
          <HeaderContent>
            <BackButton onPress={() => navigation.goBack()}>
              <Ionicons name="arrow-back" size={24} color={Colors.light.text} />
            </BackButton>
            <HeaderTitle>My Bookings</HeaderTitle>
          </HeaderContent>
          <HeaderActions>
            <HeaderButton onPress={() => navigation.navigate('BookingHistory' as never)}>
              <Ionicons name="time-outline" size={20} color={Colors.light.text} />
            </HeaderButton>
            <HeaderButton onPress={() => navigation.navigate('BookingSettings' as never)}>
              <Ionicons name="settings-outline" size={20} color={Colors.light.text} />
            </HeaderButton>
          </HeaderActions>
        </Header>

        <TabContainer>
          <TabButton
            active={activeTab === 'upcoming'}
            onPress={() => setActiveTab('upcoming')}
          >
            <TabButtonText active={activeTab === 'upcoming'}>
              Upcoming ({upcomingBookings.length})
            </TabButtonText>
          </TabButton>
          <TabButton
            active={activeTab === 'past'}
            onPress={() => setActiveTab('past')}
          >
            <TabButtonText active={activeTab === 'past'}>
              Past ({pastBookings.length})
            </TabButtonText>
          </TabButton>
          <TabButton
            active={activeTab === 'all'}
            onPress={() => setActiveTab('all')}
          >
            <TabButtonText active={activeTab === 'all'}>
              All ({bookings.length})
            </TabButtonText>
          </TabButton>
        </TabContainer>

        {error && (
          <ErrorMessage
            message={error}
            onRetry={loadData}
            onDismiss={clearError}
          />
        )}

        {getCurrentBookings().length === 0 ? (
          <EmptyStateContainer>
            <EmptyStateIcon name="calendar-outline" size={64} color={Colors.light.textSecondary} />
            <EmptyStateTitle>
              {activeTab === 'upcoming' ? 'No Upcoming Bookings' : 
               activeTab === 'past' ? 'No Past Bookings' : 'No Bookings Yet'}
            </EmptyStateTitle>
            <EmptyStateText>
              {activeTab === 'upcoming' 
                ? 'You don\'t have any upcoming appointments scheduled.'
                : activeTab === 'past'
                ? 'You haven\'t completed any bookings yet.'
                : 'Start by booking your first service to see your appointments here.'
              }
            </EmptyStateText>
            {activeTab !== 'past' && (
              <BookServiceButton onPress={() => navigation.navigate('ServiceSearch' as never)}>
                <BookServiceButtonText>Book a Service</BookServiceButtonText>
              </BookServiceButton>
            )}
          </EmptyStateContainer>
        ) : (
          <FlatList
            data={getCurrentBookings()}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <BookingCard
                booking={item}
                onPress={() => navigation.navigate('BookingDetails' as never, { bookingId: item.id })}
                onCancel={() => handleCancelBooking(item)}
                onReschedule={() => handleRescheduleBooking(item)}
                onContact={() => handleContactProvider(item)}
              />
            )}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[Colors.light.primary]}
              />
            }
            contentContainerStyle={{ padding: 16 }}
          />
        )}
      </SafeAreaWrapper>
    </Container>
  );
};

const Container = styled.View`
  flex: 1;
  background-color: ${Colors.light.background};
`;

const Header = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: ${Colors.light.surface};
  border-bottom-width: 1px;
  border-bottom-color: ${Colors.light.border};
`;

const HeaderContent = styled.View`
  flex-direction: row;
  align-items: center;
  flex: 1;
`;

const BackButton = styled.TouchableOpacity`
  padding: 8px;
  margin-right: 12px;
`;

const HeaderTitle = styled.Text`
  font-size: 20px;
  font-weight: bold;
  color: ${Colors.light.text};
`;

const HeaderActions = styled.View`
  flex-direction: row;
  gap: 8px;
`;

const HeaderButton = styled.TouchableOpacity`
  padding: 8px;
  border-radius: 6px;
  background-color: ${Colors.light.background};
`;

const TabContainer = styled.View`
  flex-direction: row;
  background-color: ${Colors.light.surface};
  border-bottom-width: 1px;
  border-bottom-color: ${Colors.light.border};
`;

const TabButton = styled.TouchableOpacity<{ active: boolean }>`
  flex: 1;
  padding: 16px;
  align-items: center;
  border-bottom-width: 2px;
  border-bottom-color: ${props => props.active ? Colors.light.primary : 'transparent'};
`;

const TabButtonText = styled.Text<{ active: boolean }>`
  font-size: 14px;
  font-weight: ${props => props.active ? '600' : '500'};
  color: ${props => props.active ? Colors.light.primary : Colors.light.textSecondary};
`;

const BookingCardContainer = styled.TouchableOpacity`
  background-color: ${Colors.light.surface};
  border-radius: 12px;
  margin-bottom: 16px;
  border-width: 1px;
  border-color: ${Colors.light.border};
  padding: 16px;
`;

const BookingHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
`;

const ProviderInfo = styled.View`
  flex-direction: row;
  align-items: center;
  flex: 1;
`;

const ProviderAvatar = styled.Image`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 12px;
`;

const ProviderAvatarPlaceholder = styled.View`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: ${Colors.light.background};
  justify-content: center;
  align-items: center;
  margin-right: 12px;
`;

const ProviderDetails = styled.View`
  flex: 1;
`;

const ProviderName = styled.Text`
  font-size: 16px;
  font-weight: bold;
  color: ${Colors.light.text};
  margin-bottom: 2px;
`;

const ServiceName = styled.Text`
  font-size: 14px;
  color: ${Colors.light.textSecondary};
`;

const StatusBadge = styled.View<{ status: BookingStatus }>`
  background-color: ${props => getStatusColor(props.status)}20;
  border-radius: 12px;
  padding: 4px 8px;
  border-width: 1px;
  border-color: ${props => getStatusColor(props.status)};
`;

const StatusText = styled.Text<{ status: BookingStatus }>`
  font-size: 12px;
  font-weight: 600;
  color: ${props => getStatusColor(props.status)};
`;

const BookingDetails = styled.View`
  margin-bottom: 12px;
`;

const DetailRow = styled.View`
  flex-direction: row;
  align-items: center;
  margin-bottom: 6px;
`;

const DetailIcon = styled(Ionicons)`
  margin-right: 8px;
`;

const DetailText = styled.Text`
  font-size: 14px;
  color: ${Colors.light.text};
  flex: 1;
`;

const NotesContainer = styled.View`
  background-color: ${Colors.light.background};
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 12px;
`;

const NotesLabel = styled.Text`
  font-size: 12px;
  font-weight: 600;
  color: ${Colors.light.textSecondary};
  margin-bottom: 4px;
`;

const NotesText = styled.Text`
  font-size: 14px;
  color: ${Colors.light.text};
  line-height: 18px;
`;

const BookingActions = styled.View`
  flex-direction: row;
  gap: 8px;
`;

const ActionButton = styled.TouchableOpacity<{ variant: 'primary' | 'secondary' | 'danger' }>`
  flex-direction: row;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  background-color: ${props => 
    props.variant === 'primary' ? Colors.light.primary :
    props.variant === 'danger' ? Colors.light.error + '20' :
    Colors.light.background
  };
  border-width: 1px;
  border-color: ${props => 
    props.variant === 'primary' ? Colors.light.primary :
    props.variant === 'danger' ? Colors.light.error :
    Colors.light.border
  };
`;

const ActionButtonText = styled.Text<{ variant: 'primary' | 'secondary' | 'danger' }>`
  font-size: 12px;
  font-weight: 500;
  color: ${props => 
    props.variant === 'primary' ? 'white' :
    props.variant === 'danger' ? Colors.light.error :
    Colors.light.text
  };
  margin-left: 4px;
`;

const EmptyStateContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 32px;
`;

const EmptyStateIcon = styled(Ionicons)`
  margin-bottom: 16px;
`;

const EmptyStateTitle = styled.Text`
  font-size: 20px;
  font-weight: bold;
  color: ${Colors.light.text};
  margin-bottom: 8px;
  text-align: center;
`;

const EmptyStateText = styled.Text`
  font-size: 16px;
  color: ${Colors.light.textSecondary};
  text-align: center;
  line-height: 24px;
  margin-bottom: 24px;
`;

const BookServiceButton = styled.TouchableOpacity`
  background-color: ${Colors.light.primary};
  border-radius: 8px;
  padding: 16px 32px;
`;

const BookServiceButtonText = styled.Text`
  color: white;
  font-weight: 600;
  font-size: 16px;
`;

export default BookingManagementScreen;
