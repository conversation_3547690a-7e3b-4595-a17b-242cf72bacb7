# PowerShell script to fix all remaining SafeAreaView usages
Write-Host "Fixing all remaining SafeAreaView usages..." -ForegroundColor Green

# Get all files that still use SafeAreaView from react-native-safe-area-context
$files = Get-ChildItem -Path "src" -Recurse -Include "*.tsx", "*.ts" | Where-Object {
    $content = Get-Content $_.FullName -Raw
    $content -match "SafeAreaView.*react-native-safe-area-context"
}

Write-Host "Found $($files.Count) files with SafeAreaView from react-native-safe-area-context" -ForegroundColor Yellow

foreach ($file in $files) {
    Write-Host "Processing: $($file.Name)" -ForegroundColor Cyan

    $content = Get-Content $file.FullName -Raw
    $modified = $false

    # Replace import statement
    if ($content -match "import.*SafeAreaView.*from 'react-native-safe-area-context'") {
        $content = $content -replace "import \{ SafeAreaView \} from 'react-native-safe-area-context';", "import { SafeAreaWrapper } from '../../components/ui/SafeAreaWrapper';"
        $modified = $true
        Write-Host "  Fixed import" -ForegroundColor Green
    }

    # Replace JSX usage
    if ($content -match "<SafeAreaView") {
        $content = $content -replace "<SafeAreaView", "<SafeAreaWrapper"
        $content = $content -replace "</SafeAreaView>", "</SafeAreaWrapper>"
        $modified = $true
        Write-Host "  Fixed JSX usage" -ForegroundColor Green
    }

    # Save the file if modified
    if ($modified) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "Fixed: $($file.Name)" -ForegroundColor Green
    }
}

Write-Host "All SafeAreaView issues have been fixed!" -ForegroundColor Green
