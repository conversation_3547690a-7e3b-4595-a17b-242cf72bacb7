/**
 * Provider Navigation - Navigation Component for Provider Portal
 * 
 * Provides navigation between different sections of the provider portal
 * with visual indicators and accessibility support.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import { useI18n } from '../../../contexts/I18nContext';
import { Icon } from '../../../components/ui/Icon';

export type ProviderSection = 
  | 'dashboard' 
  | 'jobs' 
  | 'availability' 
  | 'earnings' 
  | 'profile' 
  | 'settings';

interface NavigationItem {
  id: ProviderSection;
  title: string;
  icon: string;
  badge?: number;
}

interface ProviderNavigationProps {
  activeSection: ProviderSection;
  onSectionChange: (section: ProviderSection) => void;
  style?: any;
}

export const ProviderNavigation: React.FC<ProviderNavigationProps> = ({
  activeSection,
  onSectionChange,
  style,
}) => {
  const { colors } = useTheme();
  const { t } = useI18n();

  const navigationItems: NavigationItem[] = [
    {
      id: 'dashboard',
      title: t('provider.navigation.dashboard'),
      icon: 'home',
    },
    {
      id: 'jobs',
      title: t('provider.navigation.jobs'),
      icon: 'briefcase',
      badge: 3, // Mock badge for pending jobs
    },
    {
      id: 'availability',
      title: t('provider.navigation.availability'),
      icon: 'calendar',
    },
    {
      id: 'earnings',
      title: t('provider.navigation.earnings'),
      icon: 'dollar-sign',
    },
    {
      id: 'profile',
      title: t('provider.navigation.profile'),
      icon: 'user',
    },
    {
      id: 'settings',
      title: t('provider.navigation.settings'),
      icon: 'settings',
    },
  ];

  const renderNavigationItem = (item: NavigationItem) => {
    const isActive = activeSection === item.id;
    
    return (
      <TouchableOpacity
        key={item.id}
        style={[
          styles.navigationItem,
          {
            backgroundColor: isActive ? colors.primary[100] : 'transparent',
            borderColor: isActive ? colors.primary[500] : 'transparent',
          }
        ]}
        onPress={() => onSectionChange(item.id)}
        accessibilityRole="button"
        accessibilityLabel={item.title}
        accessibilityState={{ selected: isActive }}
      >
        <View style={styles.navigationItemContent}>
          <View style={styles.iconContainer}>
            <Icon 
              name={item.icon} 
              size={20} 
              color={isActive ? colors.primary[700] : colors.text.secondary}
            />
            {item.badge && item.badge > 0 && (
              <View 
                style={[
                  styles.badge,
                  { backgroundColor: colors.error[500] }
                ]}
              >
                <Text style={[styles.badgeText, { color: colors.white }]}>
                  {item.badge > 99 ? '99+' : item.badge.toString()}
                </Text>
              </View>
            )}
          </View>
          <Text
            style={[
              styles.navigationItemText,
              {
                color: isActive ? colors.primary[700] : colors.text.secondary,
                fontWeight: isActive ? '600' : '400',
              }
            ]}
          >
            {item.title}
          </Text>
        </View>
        
        {isActive && (
          <View 
            style={[
              styles.activeIndicator,
              { backgroundColor: colors.primary[500] }
            ]}
          />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }, style]}>
      <ScrollView 
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {navigationItems.map(renderNavigationItem)}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  navigationItem: {
    position: 'relative',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 8,
    borderRadius: 8,
    borderWidth: 1,
    minWidth: 100,
  },
  navigationItemContent: {
    alignItems: 'center',
  },
  iconContainer: {
    position: 'relative',
    marginBottom: 4,
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: -8,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  navigationItemText: {
    fontSize: 12,
    textAlign: 'center',
  },
  activeIndicator: {
    position: 'absolute',
    bottom: -1,
    left: 0,
    right: 0,
    height: 3,
    borderRadius: 1.5,
  },
});
