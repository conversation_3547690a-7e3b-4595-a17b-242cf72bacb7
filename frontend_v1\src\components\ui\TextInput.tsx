/**
 * TextInput Component - Basic Text Input UI Component
 * 
 * A reusable text input component with consistent styling,
 * validation support, and accessibility features.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState } from 'react';
import {
  View,
  TextInput as RNTextInput,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TextInputProps as RNTextInputProps,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface TextInputProps extends Omit<RNTextInputProps, 'style'> {
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'outlined' | 'filled';
  size?: 'small' | 'medium' | 'large';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  required?: boolean;
}

export const TextInput: React.FC<TextInputProps> = ({
  label,
  error,
  helperText,
  variant = 'default',
  size = 'medium',
  leftIcon,
  rightIcon,
  containerStyle,
  inputStyle,
  labelStyle,
  required = false,
  editable = true,
  ...textInputProps
}) => {
  const { colors } = useTheme();
  const [isFocused, setIsFocused] = useState(false);

  const getSizeConfig = () => {
    switch (size) {
      case 'small':
        return {
          height: 40,
          fontSize: 14,
          paddingHorizontal: 12,
          paddingVertical: 8,
        };
      case 'large':
        return {
          height: 56,
          fontSize: 18,
          paddingHorizontal: 16,
          paddingVertical: 16,
        };
      default:
        return {
          height: 48,
          fontSize: 16,
          paddingHorizontal: 14,
          paddingVertical: 12,
        };
    }
  };

  const sizeConfig = getSizeConfig();

  const getInputContainerStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: 8,
      minHeight: sizeConfig.height,
    };

    const borderColor = error 
      ? colors.error[500] 
      : isFocused 
      ? colors.primary[500] 
      : colors.border;

    switch (variant) {
      case 'outlined':
        return {
          ...baseStyles,
          borderWidth: 1,
          borderColor,
          backgroundColor: colors.white,
        };
      case 'filled':
        return {
          ...baseStyles,
          backgroundColor: colors.gray[50],
          borderBottomWidth: 2,
          borderBottomColor: borderColor,
        };
      default:
        return {
          ...baseStyles,
          borderBottomWidth: 1,
          borderBottomColor: borderColor,
          backgroundColor: 'transparent',
        };
    }
  };

  const getInputStyles = (): TextStyle => ({
    flex: 1,
    fontSize: sizeConfig.fontSize,
    paddingHorizontal: sizeConfig.paddingHorizontal,
    paddingVertical: sizeConfig.paddingVertical,
    color: editable ? colors.text.primary : colors.text.secondary,
    textAlignVertical: textInputProps.multiline ? 'top' : 'center',
  });

  const getLabelStyles = (): TextStyle => ({
    fontSize: 14,
    fontWeight: '500',
    color: error ? colors.error[500] : colors.text.primary,
    marginBottom: 8,
  });

  const getHelperTextStyles = (): TextStyle => ({
    fontSize: 12,
    color: error ? colors.error[500] : colors.text.secondary,
    marginTop: 4,
  });

  return (
    <View style={[styles.container, containerStyle]}>
      {/* Label */}
      {label && (
        <Text style={[getLabelStyles(), labelStyle]}>
          {label}
          {required && (
            <Text style={{ color: colors.error[500] }}> *</Text>
          )}
        </Text>
      )}

      {/* Input Container */}
      <View style={getInputContainerStyles()}>
        {/* Left Icon */}
        {leftIcon && (
          <View style={styles.iconContainer}>
            {leftIcon}
          </View>
        )}

        {/* Text Input */}
        <RNTextInput
          {...textInputProps}
          style={[getInputStyles(), inputStyle]}
          placeholderTextColor={colors.text.secondary}
          editable={editable}
          onFocus={(e) => {
            setIsFocused(true);
            textInputProps.onFocus?.(e);
          }}
          onBlur={(e) => {
            setIsFocused(false);
            textInputProps.onBlur?.(e);
          }}
          accessibilityLabel={label || textInputProps.placeholder}
          accessibilityHint={helperText || error}
          accessibilityState={{
            disabled: !editable,
          }}
        />

        {/* Right Icon */}
        {rightIcon && (
          <View style={styles.iconContainer}>
            {rightIcon}
          </View>
        )}
      </View>

      {/* Helper Text or Error */}
      {(helperText || error) && (
        <Text style={getHelperTextStyles()}>
          {error || helperText}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  iconContainer: {
    paddingHorizontal: 8,
  },
});
