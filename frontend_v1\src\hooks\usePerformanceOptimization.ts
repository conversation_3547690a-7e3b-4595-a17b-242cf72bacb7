/**
 * Performance Optimization Hook
 *
 * Component Contract:
 * - Provides comprehensive performance optimization utilities for React components
 * - Implements memoization, debouncing, and throttling strategies
 * - <PERSON>les component lifecycle optimization
 * - Provides memory leak prevention
 * - Integrates with performance monitoring
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { performanceMonitor } from '../utils/performanceMonitoring';
import { globalCache } from '../utils/caching';

interface PerformanceOptimizationOptions {
  enableCaching?: boolean;
  enablePerformanceTracking?: boolean;
  debounceDelay?: number;
  throttleDelay?: number;
  memoryOptimization?: boolean;
}

interface UsePerformanceOptimizationReturn {
  // Memoization utilities
  memoizedValue: <T>(value: T, deps: React.DependencyList) => T;
  memoizedCallback: <T extends (...args: any[]) => any>(callback: T, deps: React.DependencyList) => T;
  
  // Debouncing and throttling
  debouncedCallback: <T extends (...args: any[]) => any>(callback: T, delay?: number) => T;
  throttledCallback: <T extends (...args: any[]) => any>(callback: T, delay?: number) => T;
  
  // Performance tracking
  trackRender: () => void;
  trackInteraction: (name: string) => void;
  
  // Memory optimization
  cleanupResources: () => void;
  isAppActive: boolean;
  
  // Caching utilities
  getCachedData: <T>(key: string) => Promise<T | null>;
  setCachedData: <T>(key: string, data: T, ttl?: number) => Promise<void>;
}

export const usePerformanceOptimization = (
  componentName: string,
  options: PerformanceOptimizationOptions = {}
): UsePerformanceOptimizationReturn => {
  const {
    enableCaching = true,
    enablePerformanceTracking = true,
    debounceDelay = 300,
    throttleDelay = 100,
    memoryOptimization = true,
  } = options;

  const [isAppActive, setIsAppActive] = useState(AppState.currentState === 'active');
  const renderStartTime = useRef<number>(0);
  const debounceTimers = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const throttleTimers = useRef<Map<string, { lastCall: number; timer?: NodeJS.Timeout }>>(new Map());
  const resourceCleanupCallbacks = useRef<Array<() => void>>([]);

  // Track app state changes for memory optimization
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      setIsAppActive(nextAppState === 'active');
      
      if (nextAppState === 'background' && memoryOptimization) {
        // Cleanup resources when app goes to background
        cleanupResources();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [memoryOptimization]);

  // Track component render performance
  useEffect(() => {
    if (enablePerformanceTracking) {
      renderStartTime.current = performance.now();
      
      return () => {
        const renderTime = performance.now() - renderStartTime.current;
        performanceMonitor.trackRender(componentName, renderTime);
      };
    }
  });

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear all timers
      debounceTimers.current.forEach(timer => clearTimeout(timer));
      throttleTimers.current.forEach(({ timer }) => timer && clearTimeout(timer));
      
      // Run cleanup callbacks
      resourceCleanupCallbacks.current.forEach(cleanup => cleanup());
    };
  }, []);

  // Memoized value utility
  const memoizedValue = useCallback(<T>(value: T, deps: React.DependencyList): T => {
    return useMemo(() => value, deps);
  }, []);

  // Memoized callback utility
  const memoizedCallback = useCallback(<T extends (...args: any[]) => any>(
    callback: T,
    deps: React.DependencyList
  ): T => {
    return useCallback(callback, deps);
  }, []);

  // Debounced callback utility
  const debouncedCallback = useCallback(<T extends (...args: any[]) => any>(
    callback: T,
    delay: number = debounceDelay
  ): T => {
    return ((...args: Parameters<T>) => {
      const key = callback.toString();
      
      // Clear existing timer
      const existingTimer = debounceTimers.current.get(key);
      if (existingTimer) {
        clearTimeout(existingTimer);
      }
      
      // Set new timer
      const timer = setTimeout(() => {
        callback(...args);
        debounceTimers.current.delete(key);
      }, delay);
      
      debounceTimers.current.set(key, timer);
    }) as T;
  }, [debounceDelay]);

  // Throttled callback utility
  const throttledCallback = useCallback(<T extends (...args: any[]) => any>(
    callback: T,
    delay: number = throttleDelay
  ): T => {
    return ((...args: Parameters<T>) => {
      const key = callback.toString();
      const now = Date.now();
      const throttleData = throttleTimers.current.get(key);
      
      if (!throttleData || now - throttleData.lastCall >= delay) {
        // Execute immediately
        callback(...args);
        throttleTimers.current.set(key, { lastCall: now });
      } else {
        // Schedule for later
        if (throttleData.timer) {
          clearTimeout(throttleData.timer);
        }
        
        const remainingTime = delay - (now - throttleData.lastCall);
        const timer = setTimeout(() => {
          callback(...args);
          throttleTimers.current.set(key, { lastCall: Date.now() });
        }, remainingTime);
        
        throttleTimers.current.set(key, { ...throttleData, timer });
      }
    }) as T;
  }, [throttleDelay]);

  // Track render performance
  const trackRender = useCallback(() => {
    if (enablePerformanceTracking) {
      const renderTime = performance.now() - renderStartTime.current;
      performanceMonitor.trackRender(componentName, renderTime);
    }
  }, [componentName, enablePerformanceTracking]);

  // Track user interactions
  const trackInteraction = useCallback((name: string) => {
    if (enablePerformanceTracking) {
      performanceMonitor.recordMetric(
        'user.interaction',
        Date.now(),
        { component: componentName, interaction: name }
      );
    }
  }, [componentName, enablePerformanceTracking]);

  // Cleanup resources
  const cleanupResources = useCallback(() => {
    resourceCleanupCallbacks.current.forEach(cleanup => {
      try {
        cleanup();
      } catch (error) {
        if (__DEV__) {
          console.warn('[PerformanceOptimization] Cleanup error:', error);
        }
      }
    });
    resourceCleanupCallbacks.current = [];
  }, []);

  // Cache utilities
  const getCachedData = useCallback(async <T>(key: string): Promise<T | null> => {
    if (!enableCaching) return null;
    return await globalCache.get<T>(`${componentName}:${key}`);
  }, [componentName, enableCaching]);

  const setCachedData = useCallback(async <T>(
    key: string,
    data: T,
    ttl: number = 5 * 60 * 1000 // 5 minutes default
  ): Promise<void> => {
    if (!enableCaching) return;
    await globalCache.set(`${componentName}:${key}`, data, { ttl });
  }, [componentName, enableCaching]);

  return {
    memoizedValue,
    memoizedCallback,
    debouncedCallback,
    throttledCallback,
    trackRender,
    trackInteraction,
    cleanupResources,
    isAppActive,
    getCachedData,
    setCachedData,
  };
};

/**
 * Hook for optimizing list rendering performance
 */
export const useListOptimization = <T>(
  data: T[],
  keyExtractor: (item: T, index: number) => string,
  options: {
    windowSize?: number;
    initialNumToRender?: number;
    maxToRenderPerBatch?: number;
    updateCellsBatchingPeriod?: number;
  } = {}
) => {
  const {
    windowSize = 10,
    initialNumToRender = 10,
    maxToRenderPerBatch = 10,
    updateCellsBatchingPeriod = 50,
  } = options;

  const getItemLayout = useCallback((data: T[] | null | undefined, index: number) => {
    // Estimate item height - should be customized based on actual item height
    const ITEM_HEIGHT = 80;
    return {
      length: ITEM_HEIGHT,
      offset: ITEM_HEIGHT * index,
      index,
    };
  }, []);

  const memoizedKeyExtractor = useCallback(keyExtractor, []);

  return {
    windowSize,
    initialNumToRender,
    maxToRenderPerBatch,
    updateCellsBatchingPeriod,
    getItemLayout,
    keyExtractor: memoizedKeyExtractor,
    removeClippedSubviews: true,
  };
};

/**
 * Hook for optimizing image loading performance
 */
export const useImageOptimization = () => {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const imageLoadTimers = useRef<Map<string, NodeJS.Timeout>>(new Map());

  const preloadImage = useCallback((uri: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (loadedImages.has(uri)) {
        resolve();
        return;
      }

      const image = new Image();
      image.onload = () => {
        setLoadedImages(prev => new Set(prev).add(uri));
        resolve();
      };
      image.onerror = reject;
      image.src = uri;
    });
  }, [loadedImages]);

  const isImageLoaded = useCallback((uri: string): boolean => {
    return loadedImages.has(uri);
  }, [loadedImages]);

  const clearImageCache = useCallback(() => {
    setLoadedImages(new Set());
  }, []);

  return {
    preloadImage,
    isImageLoaded,
    clearImageCache,
  };
};
