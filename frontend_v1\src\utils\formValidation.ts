/**
 * Form Validation Utilities
 *
 * Utility Functions:
 * - Comprehensive validation rules for common form fields
 * - Custom validation function support
 * - Form-level validation with error aggregation
 * - Real-time validation with debouncing
 * - Accessibility-friendly error messages
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

// Form validation configuration
export interface FieldValidation {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => ValidationResult;
  email?: boolean;
  phone?: boolean;
  password?: boolean;
  confirmPassword?: string; // Field name to match against
  name?: boolean;
  url?: boolean;
  number?: boolean;
  date?: boolean;
}

export interface FormValidationConfig {
  [fieldName: string]: FieldValidation;
}

export interface FormValidationErrors {
  [fieldName: string]: string;
}

// Built-in validation functions
export const ValidationRules = {
  /**
   * Required field validation
   */
  required: (value: any): ValidationResult => {
    const isValid =
      value !== null && value !== undefined && String(value).trim().length > 0;
    return {
      isValid,
      error: isValid ? undefined : 'Please fill in this field to continue',
    };
  },

  /**
   * Email validation
   */
  email: (value: string): ValidationResult => {
    if (!value) return { isValid: true }; // Allow empty if not required

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(value);

    return {
      isValid,
      error: isValid ? undefined : 'Please enter your email in <NAME_EMAIL>',
    };
  },

  /**
   * Phone number validation
   */
  phone: (value: string): ValidationResult => {
    if (!value) return { isValid: true }; // Allow empty if not required

    // Remove formatting characters
    const cleanPhone = value.replace(/[\s\-\(\)]/g, '');
    const phoneRegex = /^[\+]?[1-9][\d]{9,14}$/;
    const isValid = phoneRegex.test(cleanPhone);

    return {
      isValid,
      error: isValid ? undefined : 'Please enter your phone number with area code (e.g., ************)',
    };
  },

  /**
   * Password validation
   */
  password: (value: string): ValidationResult => {
    if (!value) return { isValid: true }; // Allow empty if not required

    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(value);
    const hasLowerCase = /[a-z]/.test(value);
    const hasNumbers = /\d/.test(value);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);

    if (value.length < minLength) {
      return {
        isValid: false,
        error: `Your password needs at least ${minLength} characters. Please add ${minLength - value.length} more character${minLength - value.length > 1 ? 's' : ''}.`,
      };
    }

    if (!hasUpperCase) {
      return {
        isValid: false,
        error: 'Your password needs at least one uppercase letter (A-Z). Please add one to continue.',
      };
    }

    if (!hasLowerCase) {
      return {
        isValid: false,
        error: 'Your password needs at least one lowercase letter (a-z). Please add one to continue.',
      };
    }

    if (!hasNumbers) {
      return {
        isValid: false,
        error: 'Your password needs at least one number (0-9). Please add one to continue.',
      };
    }

    return { isValid: true };
  },

  /**
   * Name validation
   */
  name: (value: string): ValidationResult => {
    if (!value) return { isValid: true }; // Allow empty if not required

    const nameRegex = /^[a-zA-Z\s\-']{2,50}$/;
    const isValid = nameRegex.test(value.trim());

    return {
      isValid,
      error: isValid
        ? undefined
        : 'Please enter your name using only letters, spaces, hyphens, and apostrophes (2-50 characters)',
    };
  },

  /**
   * URL validation
   */
  url: (value: string): ValidationResult => {
    if (!value) return { isValid: true }; // Allow empty if not required

    try {
      new URL(value);
      return { isValid: true };
    } catch {
      return {
        isValid: false,
        error: 'Please enter a valid URL',
      };
    }
  },

  /**
   * Number validation
   */
  number: (value: string): ValidationResult => {
    if (!value) return { isValid: true }; // Allow empty if not required

    const isValid = !isNaN(Number(value)) && isFinite(Number(value));

    return {
      isValid,
      error: isValid ? undefined : 'Please enter a valid number',
    };
  },

  /**
   * Date validation
   */
  date: (value: string): ValidationResult => {
    if (!value) return { isValid: true }; // Allow empty if not required

    const date = new Date(value);
    const isValid = date instanceof Date && !isNaN(date.getTime());

    return {
      isValid,
      error: isValid ? undefined : 'Please enter a valid date',
    };
  },

  /**
   * Minimum length validation
   */
  minLength: (value: string, minLength: number): ValidationResult => {
    if (!value) return { isValid: true }; // Allow empty if not required

    const isValid = value.length >= minLength;

    return {
      isValid,
      error: isValid
        ? undefined
        : `Must be at least ${minLength} characters long`,
    };
  },

  /**
   * Maximum length validation
   */
  maxLength: (value: string, maxLength: number): ValidationResult => {
    if (!value) return { isValid: true }; // Allow empty if not required

    const isValid = value.length <= maxLength;

    return {
      isValid,
      error: isValid
        ? undefined
        : `Must be no more than ${maxLength} characters long`,
    };
  },

  /**
   * Pattern validation
   */
  pattern: (
    value: string,
    pattern: RegExp,
    errorMessage?: string,
  ): ValidationResult => {
    if (!value) return { isValid: true }; // Allow empty if not required

    const isValid = pattern.test(value);

    return {
      isValid,
      error: isValid ? undefined : errorMessage || 'Invalid format',
    };
  },

  /**
   * Confirm password validation
   */
  confirmPassword: (
    value: string,
    originalPassword: string,
  ): ValidationResult => {
    const isValid = value === originalPassword;

    return {
      isValid,
      error: isValid ? undefined : 'Passwords do not match',
    };
  },
};

/**
 * Validate a single field
 */
export const validateField = (
  value: any,
  validation: FieldValidation,
  formData?: Record<string, any>,
): ValidationResult => {
  // Required validation
  if (validation.required) {
    const requiredResult = ValidationRules.required(value);
    if (!requiredResult.isValid) {
      return requiredResult;
    }
  }

  // Skip other validations if value is empty and not required
  if (!value && !validation.required) {
    return { isValid: true };
  }

  // Email validation
  if (validation.email) {
    const emailResult = ValidationRules.email(value);
    if (!emailResult.isValid) {
      return emailResult;
    }
  }

  // Phone validation
  if (validation.phone) {
    const phoneResult = ValidationRules.phone(value);
    if (!phoneResult.isValid) {
      return phoneResult;
    }
  }

  // Password validation
  if (validation.password) {
    const passwordResult = ValidationRules.password(value);
    if (!passwordResult.isValid) {
      return passwordResult;
    }
  }

  // Name validation
  if (validation.name) {
    const nameResult = ValidationRules.name(value);
    if (!nameResult.isValid) {
      return nameResult;
    }
  }

  // URL validation
  if (validation.url) {
    const urlResult = ValidationRules.url(value);
    if (!urlResult.isValid) {
      return urlResult;
    }
  }

  // Number validation
  if (validation.number) {
    const numberResult = ValidationRules.number(value);
    if (!numberResult.isValid) {
      return numberResult;
    }
  }

  // Date validation
  if (validation.date) {
    const dateResult = ValidationRules.date(value);
    if (!dateResult.isValid) {
      return dateResult;
    }
  }

  // Min length validation
  if (validation.minLength !== undefined) {
    const minLengthResult = ValidationRules.minLength(
      value,
      validation.minLength,
    );
    if (!minLengthResult.isValid) {
      return minLengthResult;
    }
  }

  // Max length validation
  if (validation.maxLength !== undefined) {
    const maxLengthResult = ValidationRules.maxLength(
      value,
      validation.maxLength,
    );
    if (!maxLengthResult.isValid) {
      return maxLengthResult;
    }
  }

  // Pattern validation
  if (validation.pattern) {
    const patternResult = ValidationRules.pattern(value, validation.pattern);
    if (!patternResult.isValid) {
      return patternResult;
    }
  }

  // Confirm password validation
  if (validation.confirmPassword && formData) {
    const originalPassword = formData[validation.confirmPassword];
    const confirmResult = ValidationRules.confirmPassword(
      value,
      originalPassword,
    );
    if (!confirmResult.isValid) {
      return confirmResult;
    }
  }

  // Custom validation
  if (validation.custom) {
    const customResult = validation.custom(value);
    if (!customResult.isValid) {
      return customResult;
    }
  }

  return { isValid: true };
};

/**
 * Validate entire form
 */
export const validateForm = (
  formData: Record<string, any>,
  validationConfig: FormValidationConfig,
): { isValid: boolean; errors: FormValidationErrors } => {
  const errors: FormValidationErrors = {};
  let isValid = true;

  Object.keys(validationConfig).forEach(fieldName => {
    const fieldValue = formData[fieldName];
    const fieldValidation = validationConfig[fieldName];

    const result = validateField(fieldValue, fieldValidation, formData);

    if (!result.isValid) {
      errors[fieldName] = result.error!;
      isValid = false;
    }
  });

  return { isValid, errors };
};

/**
 * Create debounced validation function
 */
export const createDebouncedValidator = (
  validationFn: () => void,
  delay: number = 300,
) => {
  let timeoutId: NodeJS.Timeout;

  return () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(validationFn, delay);
  };
};

/**
 * Common validation configurations
 */
export const CommonValidations = {
  email: {
    required: true,
    email: true,
  } as FieldValidation,

  password: {
    required: true,
    password: true,
    minLength: 8,
  } as FieldValidation,

  confirmPassword: (passwordFieldName: string) =>
    ({
      required: true,
      confirmPassword: passwordFieldName,
    }) as FieldValidation,

  name: {
    required: true,
    name: true,
    minLength: 2,
    maxLength: 50,
  } as FieldValidation,

  phone: {
    required: true,
    phone: true,
  } as FieldValidation,

  required: {
    required: true,
  } as FieldValidation,

  optional: {} as FieldValidation,
};

/**
 * Form validation hook-like utility
 */
export class FormValidator {
  private validationConfig: FormValidationConfig;
  private errors: FormValidationErrors = {};
  private touched: Record<string, boolean> = {};

  constructor(validationConfig: FormValidationConfig) {
    this.validationConfig = validationConfig;
  }

  validateField(fieldName: string, value: any, formData: Record<string, any>) {
    const result = validateField(
      value,
      this.validationConfig[fieldName],
      formData,
    );

    if (result.isValid) {
      delete this.errors[fieldName];
    } else {
      this.errors[fieldName] = result.error!;
    }

    return result;
  }

  validateForm(formData: Record<string, any>) {
    const result = validateForm(formData, this.validationConfig);
    this.errors = result.errors;
    return result;
  }

  setFieldTouched(fieldName: string, touched: boolean = true) {
    this.touched[fieldName] = touched;
  }

  getFieldError(fieldName: string): string | undefined {
    return this.touched[fieldName] ? this.errors[fieldName] : undefined;
  }

  getErrors(): FormValidationErrors {
    return this.errors;
  }

  isFieldTouched(fieldName: string): boolean {
    return this.touched[fieldName] || false;
  }

  reset() {
    this.errors = {};
    this.touched = {};
  }
}
