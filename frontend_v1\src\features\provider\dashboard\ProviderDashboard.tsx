/**
 * Provider Dashboard - Main Dashboard Component
 * 
 * Phase 1 MVP Implementation:
 * - Actionable alerts and basic KPIs
 * - Upcoming Jobs display
 * - Weekly Earnings summary
 * - Quick action buttons
 * - Real-time updates
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  Text,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import { useI18n } from '../../../contexts/I18nContext';
import { useProviderStore } from '../../../store/providerSlice';
import { Card } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/Button';
import { Icon } from '../../../components/ui/Icon';

// Dashboard data interfaces
interface DashboardKPI {
  id: string;
  title: string;
  value: string;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  icon: string;
  color: string;
}

interface UpcomingJob {
  id: string;
  clientName: string;
  serviceName: string;
  scheduledTime: string;
  location: string;
  status: 'confirmed' | 'pending' | 'in_progress';
  earnings: number;
}

interface ActionableAlert {
  id: string;
  type: 'info' | 'warning' | 'success' | 'error';
  title: string;
  message: string;
  actionText?: string;
  onAction?: () => void;
}

export const ProviderDashboard: React.FC = () => {
  const { colors } = useTheme();
  const { t } = useI18n();
  const { dashboard, refreshDashboard } = useProviderStore();
  
  // State
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [kpis, setKpis] = useState<DashboardKPI[]>([]);
  const [upcomingJobs, setUpcomingJobs] = useState<UpcomingJob[]>([]);
  const [alerts, setAlerts] = useState<ActionableAlert[]>([]);

  // Load dashboard data
  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // Mock data for Phase 1 MVP
      const mockKPIs: DashboardKPI[] = [
        {
          id: '1',
          title: t('provider.dashboard.weeklyEarnings'),
          value: '$1,245',
          change: '+12%',
          changeType: 'positive',
          icon: 'dollar-sign',
          color: colors.success[500],
        },
        {
          id: '2',
          title: t('provider.dashboard.upcomingJobs'),
          value: '8',
          change: '+3',
          changeType: 'positive',
          icon: 'calendar',
          color: colors.primary[500],
        },
        {
          id: '3',
          title: t('provider.dashboard.completionRate'),
          value: '96%',
          change: '+2%',
          changeType: 'positive',
          icon: 'check-circle',
          color: colors.success[500],
        },
        {
          id: '4',
          title: t('provider.dashboard.avgRating'),
          value: '4.8',
          change: '+0.1',
          changeType: 'positive',
          icon: 'star',
          color: colors.warning[500],
        },
      ];

      const mockJobs: UpcomingJob[] = [
        {
          id: '1',
          clientName: 'Sarah Johnson',
          serviceName: 'House Cleaning',
          scheduledTime: '2025-07-20 10:00 AM',
          location: '123 Main St, Toronto',
          status: 'confirmed',
          earnings: 85,
        },
        {
          id: '2',
          clientName: 'Mike Chen',
          serviceName: 'Lawn Care',
          scheduledTime: '2025-07-20 2:00 PM',
          location: '456 Oak Ave, Toronto',
          status: 'confirmed',
          earnings: 120,
        },
        {
          id: '3',
          clientName: 'Emma Wilson',
          serviceName: 'Pet Sitting',
          scheduledTime: '2025-07-21 9:00 AM',
          location: '789 Pine St, Toronto',
          status: 'pending',
          earnings: 60,
        },
      ];

      const mockAlerts: ActionableAlert[] = [
        {
          id: '1',
          type: 'info',
          title: t('provider.dashboard.newJobRequest'),
          message: 'You have 2 new job requests waiting for your response',
          actionText: t('provider.dashboard.viewRequests'),
          onAction: () => {
            // Navigate to jobs section
            console.log('Navigate to job requests');
          },
        },
        {
          id: '2',
          type: 'warning',
          title: t('provider.dashboard.profileIncomplete'),
          message: 'Complete your profile to get more job opportunities',
          actionText: t('provider.dashboard.completeProfile'),
          onAction: () => {
            // Navigate to profile section
            console.log('Navigate to profile');
          },
        },
      ];

      setKpis(mockKPIs);
      setUpcomingJobs(mockJobs);
      setAlerts(mockAlerts);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Alert.alert(
        t('common.error'),
        t('provider.dashboard.loadError')
      );
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadDashboardData();
    await refreshDashboard();
    setIsRefreshing(false);
  };

  const renderKPICard = (kpi: DashboardKPI) => (
    <Card key={kpi.id} style={styles.kpiCard}>
      <View style={styles.kpiHeader}>
        <Icon 
          name={kpi.icon} 
          size={24} 
          color={kpi.color}
          accessibilityLabel={kpi.title}
        />
        <Text style={[styles.kpiTitle, { color: colors.text.secondary }]}>
          {kpi.title}
        </Text>
      </View>
      <Text style={[styles.kpiValue, { color: colors.text.primary }]}>
        {kpi.value}
      </Text>
      {kpi.change && (
        <Text 
          style={[
            styles.kpiChange, 
            { 
              color: kpi.changeType === 'positive' 
                ? colors.success[500] 
                : kpi.changeType === 'negative'
                ? colors.error[500]
                : colors.text.secondary
            }
          ]}
        >
          {kpi.change}
        </Text>
      )}
    </Card>
  );

  const renderUpcomingJob = (job: UpcomingJob) => (
    <Card key={job.id} style={styles.jobCard}>
      <View style={styles.jobHeader}>
        <Text style={[styles.jobClient, { color: colors.text.primary }]}>
          {job.clientName}
        </Text>
        <Text style={[styles.jobEarnings, { color: colors.success[500] }]}>
          ${job.earnings}
        </Text>
      </View>
      <Text style={[styles.jobService, { color: colors.text.secondary }]}>
        {job.serviceName}
      </Text>
      <Text style={[styles.jobTime, { color: colors.text.secondary }]}>
        {job.scheduledTime}
      </Text>
      <Text style={[styles.jobLocation, { color: colors.text.secondary }]}>
        {job.location}
      </Text>
      <View style={styles.jobActions}>
        <Button
          title={t('provider.dashboard.viewDetails')}
          variant="outline"
          size="small"
          onPress={() => console.log('View job details:', job.id)}
        />
        {job.status === 'pending' && (
          <Button
            title={t('provider.dashboard.accept')}
            variant="primary"
            size="small"
            onPress={() => console.log('Accept job:', job.id)}
          />
        )}
      </View>
    </Card>
  );

  const renderAlert = (alert: ActionableAlert) => (
    <Card 
      key={alert.id} 
      style={[
        styles.alertCard,
        { 
          borderLeftColor: alert.type === 'warning' 
            ? colors.warning[500]
            : alert.type === 'error'
            ? colors.error[500]
            : alert.type === 'success'
            ? colors.success[500]
            : colors.primary[500]
        }
      ]}
    >
      <Text style={[styles.alertTitle, { color: colors.text.primary }]}>
        {alert.title}
      </Text>
      <Text style={[styles.alertMessage, { color: colors.text.secondary }]}>
        {alert.message}
      </Text>
      {alert.actionText && alert.onAction && (
        <TouchableOpacity 
          style={styles.alertAction}
          onPress={alert.onAction}
          accessibilityRole="button"
          accessibilityLabel={alert.actionText}
        >
          <Text style={[styles.alertActionText, { color: colors.primary[500] }]}>
            {alert.actionText}
          </Text>
        </TouchableOpacity>
      )}
    </Card>
  );

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: colors.background }]}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={handleRefresh}
          tintColor={colors.primary[500]}
        />
      }
      showsVerticalScrollIndicator={false}
    >
      {/* Actionable Alerts */}
      {alerts.length > 0 && (
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            {t('provider.dashboard.alerts')}
          </Text>
          {alerts.map(renderAlert)}
        </View>
      )}

      {/* KPIs */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
          {t('provider.dashboard.overview')}
        </Text>
        <View style={styles.kpiGrid}>
          {kpis.map(renderKPICard)}
        </View>
      </View>

      {/* Upcoming Jobs */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
          {t('provider.dashboard.upcomingJobs')}
        </Text>
        {upcomingJobs.map(renderUpcomingJob)}
      </View>

      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
          {t('provider.dashboard.quickActions')}
        </Text>
        <View style={styles.quickActions}>
          <Button
            title={t('provider.dashboard.manageAvailability')}
            variant="outline"
            onPress={() => console.log('Manage availability')}
            style={styles.quickActionButton}
          />
          <Button
            title={t('provider.dashboard.viewEarnings')}
            variant="outline"
            onPress={() => console.log('View earnings')}
            style={styles.quickActionButton}
          />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  kpiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  kpiCard: {
    width: '48%',
    padding: 16,
    marginBottom: 12,
  },
  kpiHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  kpiTitle: {
    fontSize: 14,
    marginLeft: 8,
  },
  kpiValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  kpiChange: {
    fontSize: 12,
    fontWeight: '500',
  },
  jobCard: {
    padding: 16,
    marginBottom: 12,
  },
  jobHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  jobClient: {
    fontSize: 16,
    fontWeight: '600',
  },
  jobEarnings: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  jobService: {
    fontSize: 14,
    marginBottom: 4,
  },
  jobTime: {
    fontSize: 14,
    marginBottom: 4,
  },
  jobLocation: {
    fontSize: 14,
    marginBottom: 12,
  },
  jobActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  alertCard: {
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  alertTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  alertMessage: {
    fontSize: 14,
    marginBottom: 8,
  },
  alertAction: {
    alignSelf: 'flex-start',
  },
  alertActionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
});
