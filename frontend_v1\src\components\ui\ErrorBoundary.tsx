/**
 * Error Boundary Component - Comprehensive Error Handling
 *
 * Component Contract:
 * - Catches JavaScript errors anywhere in the child component tree
 * - Logs error information and displays fallback UI
 * - Provides retry functionality and error reporting
 * - Supports different error types and contexts
 * - Implements accessibility and responsive design
 * - Integrates with error tracking services
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { Component, ReactNode, ErrorInfo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
} from 'react-native';

import { Colors } from '../../constants/Colors';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';

// Error types for better categorization
export type ErrorType = 'network' | 'render' | 'async' | 'permission' | 'unknown';

// Error severity levels
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

// Recovery strategies
export type RecoveryStrategy = 'retry' | 'fallback' | 'redirect' | 'reload' | 'none';

// Types
interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  enableRetry?: boolean;
  maxRetries?: number;
  context?: string;
  showErrorDetails?: boolean;
  errorType?: ErrorType;
  severity?: ErrorSeverity;
  recoveryStrategy?: RecoveryStrategy;
  autoRetryDelay?: number;
  enableErrorReporting?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
  errorId: string;
  errorType: ErrorType;
  severity: ErrorSeverity;
  isRetrying: boolean;
  lastRetryTime: number;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      errorId: this.generateErrorId(),
      errorType: props.errorType || 'unknown',
      severity: props.severity || 'medium',
      isRetrying: false,
      lastRetryTime: 0,
    };
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private categorizeError(error: Error): { type: ErrorType; severity: ErrorSeverity } {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    // Network errors
    if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
      return { type: 'network', severity: 'medium' };
    }

    // Permission errors
    if (message.includes('permission') || message.includes('unauthorized') || message.includes('forbidden')) {
      return { type: 'permission', severity: 'high' };
    }

    // Async errors
    if (message.includes('promise') || message.includes('async') || stack.includes('async')) {
      return { type: 'async', severity: 'medium' };
    }

    // Render errors
    if (stack.includes('render') || message.includes('element') || message.includes('component')) {
      return { type: 'render', severity: 'high' };
    }

    return { type: 'unknown', severity: 'medium' };
  }

  private shouldAutoRetry(): boolean {
    const { autoRetryDelay = 0, maxRetries = 3 } = this.props;
    const { retryCount, lastRetryTime, severity } = this.state;

    if (retryCount >= maxRetries || severity === 'critical') {
      return false;
    }

    if (autoRetryDelay > 0 && Date.now() - lastRetryTime > autoRetryDelay) {
      return true;
    }

    return false;
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('🚨 Error Boundary caught error:', error);
    console.error('🚨 Error Info:', errorInfo);

    this.setState({
      error,
      errorInfo,
      errorId: this.generateErrorId(),
    });

    // Call custom error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log to error tracking service (implement as needed)
    this.logError(error, errorInfo);
  }

  private logError = (error: Error, errorInfo: ErrorInfo) => {
    const errorDetails = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      context: this.props.context,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      retryCount: this.state.retryCount,
    };

    // TODO: Integrate with error tracking service (Sentry, Bugsnag, etc.)
    console.log('📊 Error logged:', errorDetails);
  };

  private handleRetry = () => {
    const { maxRetries = 3 } = this.props;

    if (this.state.retryCount >= maxRetries) {
      Alert.alert(
        'Maximum Retries Exceeded',
        'The error persists after multiple attempts. Please restart the app or contact support.',
        [
          { text: 'OK', style: 'default' },
          { text: 'Contact Support', onPress: this.handleContactSupport },
        ],
      );
      return;
    }

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: this.state.retryCount + 1,
      errorId: this.generateErrorId(),
    });
  };

  private handleContactSupport = () => {
    // TODO: Implement support contact functionality
    Alert.alert(
      'Contact Support',
      `Please contact support with error ID: ${this.state.errorId}`,
      [{ text: 'OK', style: 'default' }],
    );
  };

  private renderErrorDetails = () => {
    const { showErrorDetails = __DEV__ } = this.props;
    const { error, errorInfo } = this.state;

    if (!showErrorDetails || !error) return null;

    return (
      <View style={styles.errorDetails}>
        <Text style={styles.errorDetailsTitle}>
          Error Details (Development)
        </Text>
        <Text style={styles.errorDetailsText}>{error.message}</Text>
        {error.stack && (
          <Text style={styles.errorDetailsStack}>
            {error.stack.substring(0, 500)}...
          </Text>
        )}
        <Text style={styles.errorId}>Error ID: {this.state.errorId}</Text>
      </View>
    );
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback component
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const {
        enableRetry = true,
        maxRetries = 3,
        context = 'Application',
      } = this.props;
      const canRetry = enableRetry && this.state.retryCount < maxRetries;

      return (
        <View style={styles.container}>
          <View style={styles.content}>
            <View style={styles.iconContainer}>
              <Text style={styles.errorIcon}>⚠️</Text>
            </View>

            <Text style={styles.title}>We're Fixing This</Text>

            <Text style={styles.message}>
              We encountered an issue while loading {context.toLowerCase()}. This is usually temporary and we're working to resolve it quickly.
              {canRetry
                ? ' You can try again now, or wait a moment and try again.'
                : ' Please restart the app to continue safely, or contact our support team for immediate help.'}
            </Text>

            {canRetry && (
              <TouchableOpacity
                style={styles.retryButton}
                onPress={this.handleRetry}
                testID="error-retry-button"
                accessibilityLabel="Retry"
                accessibilityHint="Attempt to recover from the error">
                <Text style={styles.retryButtonText}>
                  Try Again ({this.state.retryCount + 1}/{maxRetries})
                </Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={styles.supportButton}
              onPress={this.handleContactSupport}
              testID="error-support-button"
              accessibilityLabel="Contact Support"
              accessibilityHint="Get help with this error">
              <Text style={styles.supportButtonText}>Contact Support</Text>
            </TouchableOpacity>

            {this.renderErrorDetails()}
          </View>
        </View>
      );
    }

    return this.props.children;
  }
}

// Specialized Error Boundaries
export const ScreenErrorBoundary: React.FC<{
  children: ReactNode;
  screenName: string;
}> = ({ children, screenName }) => (
  <ErrorBoundary
    context={`${screenName} Screen`}
    enableRetry={true}
    maxRetries={3}
    showErrorDetails={__DEV__}>
    {children}
  </ErrorBoundary>
);

export const FeatureErrorBoundary: React.FC<{
  children: ReactNode;
  featureName: string;
}> = ({ children, featureName }) => (
  <ErrorBoundary
    context={`${featureName} Feature`}
    enableRetry={true}
    maxRetries={2}
    showErrorDetails={__DEV__}>
    {children}
  </ErrorBoundary>
);

// Styles
const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
    justifyContent: 'center',
    alignItems: 'center',
    padding: getResponsiveSpacing(20),
  },
  content: {
    alignItems: 'center',
    maxWidth: width * 0.9,
  },
  iconContainer: {
    marginBottom: getResponsiveSpacing(20),
  },
  errorIcon: {
    fontSize: getResponsiveFontSize(48),
  },
  title: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: '700',
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: getResponsiveSpacing(12),
  },
  message: {
    fontSize: getResponsiveFontSize(16),
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: getResponsiveFontSize(22),
    marginBottom: getResponsiveSpacing(24),
  },
  retryButton: {
    backgroundColor: Colors.sage600,
    paddingHorizontal: getResponsiveSpacing(24),
    paddingVertical: getResponsiveSpacing(12),
    borderRadius: getResponsiveSpacing(8),
    marginBottom: getResponsiveSpacing(12),
    minWidth: 120,
  },
  retryButtonText: {
    color: Colors.text.onPrimary,
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    textAlign: 'center',
  },
  supportButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: getResponsiveSpacing(24),
    paddingVertical: getResponsiveSpacing(12),
    borderRadius: getResponsiveSpacing(8),
    borderWidth: 1,
    borderColor: Colors.border.primary,
    marginBottom: getResponsiveSpacing(20),
    minWidth: 120,
  },
  supportButtonText: {
    color: Colors.text.primary,
    fontSize: getResponsiveFontSize(16),
    fontWeight: '500',
    textAlign: 'center',
  },
  errorDetails: {
    backgroundColor: Colors.background.secondary,
    padding: getResponsiveSpacing(16),
    borderRadius: getResponsiveSpacing(8),
    borderWidth: 1,
    borderColor: Colors.border.light,
    width: '100%',
    maxHeight: 200,
  },
  errorDetailsTitle: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: getResponsiveSpacing(8),
  },
  errorDetailsText: {
    fontSize: getResponsiveFontSize(12),
    color: Colors.text.secondary,
    marginBottom: getResponsiveSpacing(8),
    fontFamily: 'monospace',
  },
  errorDetailsStack: {
    fontSize: getResponsiveFontSize(10),
    color: Colors.text.secondary,
    marginBottom: getResponsiveSpacing(8),
    fontFamily: 'monospace',
  },
  errorId: {
    fontSize: getResponsiveFontSize(10),
    color: Colors.text.secondary,
    fontFamily: 'monospace',
  },
});
