/**
 * Service Management Screen - Manage provider services
 *
 * Screen Contract:
 * - Display list of provider services with management options
 * - Add, edit, and delete services functionality
 * - Service pricing and duration management
 * - Service availability and scheduling settings
 * - Image gallery management for services
 * - Service category and tag management
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Image,
  Dimensions,
} from 'react-native';
import { SafeAreaWrapper } from '../../components/ui/SafeAreaWrapper';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import styled from 'styled-components/native';

import { useProviderStore, ProviderService } from '../../store/providerSlice';
import { useAuthStore } from '../../store/authSlice';
import { Colors } from '../../constants/Colors';
import { LoadingSpinner } from '../../components/common/LoadingSpinner';
import { ErrorMessage } from '../../components/common/ErrorMessage';

const { width } = Dimensions.get('window');

interface ServiceCardProps {
  service: ProviderService;
  onEdit: () => void;
  onDelete: () => void;
  onToggleActive: () => void;
}

const ServiceManagementScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAuthStore();
  const {
    profile,
    isLoading,
    error,
    loadProfile,
    updateService,
    deleteService,
    clearError,
  } = useProviderStore();

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (user?.id) {
      loadProfile(user.id);
    }
  }, [user?.id, loadProfile]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      if (user?.id) {
        await loadProfile(user.id);
      }
    } catch (error) {
      console.error('Failed to refresh services:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleToggleServiceActive = async (service: ProviderService) => {
    try {
      await updateService(service.id, { isActive: !service.isActive });
    } catch (error) {
      Alert.alert('Error', 'Failed to update service status');
    }
  };

  const handleDeleteService = (service: ProviderService) => {
    Alert.alert(
      'Delete Service',
      `Are you sure you want to delete "${service.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteService(service.id);
            } catch (error) {
              Alert.alert('Error', 'Failed to delete service');
            }
          },
        },
      ]
    );
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  const formatDuration = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0 && mins > 0) {
      return `${hours}h ${mins}m`;
    } else if (hours > 0) {
      return `${hours}h`;
    } else {
      return `${mins}m`;
    }
  };

  const ServiceCard: React.FC<ServiceCardProps> = ({
    service,
    onEdit,
    onDelete,
    onToggleActive,
  }) => (
    <ServiceCardContainer>
      <ServiceImageContainer>
        {service.images && service.images.length > 0 ? (
          <ServiceImage source={{ uri: service.images[0] }} />
        ) : (
          <PlaceholderImage>
            <PlaceholderIcon name="image-outline" size={32} color={Colors.light.textSecondary} />
          </PlaceholderImage>
        )}
        <ServiceStatusBadge isActive={service.isActive}>
          <ServiceStatusText isActive={service.isActive}>
            {service.isActive ? 'Active' : 'Inactive'}
          </ServiceStatusText>
        </ServiceStatusBadge>
      </ServiceImageContainer>

      <ServiceContent>
        <ServiceHeader>
          <ServiceName>{service.name}</ServiceName>
          <ServiceActions>
            <ActionButton onPress={onEdit}>
              <Ionicons name="pencil-outline" size={20} color={Colors.light.primary} />
            </ActionButton>
            <ActionButton onPress={onToggleActive}>
              <Ionicons 
                name={service.isActive ? "pause-outline" : "play-outline"} 
                size={20} 
                color={service.isActive ? Colors.light.warning : Colors.light.success} 
              />
            </ActionButton>
            <ActionButton onPress={onDelete}>
              <Ionicons name="trash-outline" size={20} color={Colors.light.error} />
            </ActionButton>
          </ServiceActions>
        </ServiceHeader>

        <ServiceDescription numberOfLines={2}>{service.description}</ServiceDescription>

        <ServiceDetails>
          <ServiceDetailItem>
            <ServiceDetailIcon name="pricetag-outline" size={16} color={Colors.light.success} />
            <ServiceDetailText>{formatCurrency(service.price)}</ServiceDetailText>
          </ServiceDetailItem>
          <ServiceDetailItem>
            <ServiceDetailIcon name="time-outline" size={16} color={Colors.light.info} />
            <ServiceDetailText>{formatDuration(service.duration)}</ServiceDetailText>
          </ServiceDetailItem>
          <ServiceDetailItem>
            <ServiceDetailIcon name="folder-outline" size={16} color={Colors.light.primary} />
            <ServiceDetailText>{service.category}</ServiceDetailText>
          </ServiceDetailItem>
        </ServiceDetails>

        {service.addOns && service.addOns.length > 0 && (
          <AddOnsContainer>
            <AddOnsLabel>Add-ons available</AddOnsLabel>
            <AddOnsCount>{service.addOns.length} options</AddOnsCount>
          </AddOnsContainer>
        )}
      </ServiceContent>
    </ServiceCardContainer>
  );

  if (isLoading && !profile) {
    return (
      <Container>
        <LoadingSpinner size="large" />
      </Container>
    );
  }

  if (error && !profile) {
    return (
      <Container>
        <ErrorMessage
          message={error}
          onRetry={() => user?.id && loadProfile(user.id)}
          onDismiss={clearError}
        />
      </Container>
    );
  }

  const services = profile?.services || [];

  return (
    <Container>
      <SafeAreaWrapper style={{ flex: 1 }}>
        <Header>
          <HeaderContent>
            <BackButton onPress={() => navigation.goBack()}>
              <Ionicons name="arrow-back" size={24} color={Colors.light.text} />
            </BackButton>
            <HeaderTitle>Service Management</HeaderTitle>
          </HeaderContent>
          <AddServiceButton onPress={() => navigation.navigate('AddService' as never)}>
            <Ionicons name="add" size={24} color="white" />
          </AddServiceButton>
        </Header>

        {services.length === 0 ? (
          <EmptyStateContainer>
            <EmptyStateIcon name="list-outline" size={64} color={Colors.light.textSecondary} />
            <EmptyStateTitle>No Services Yet</EmptyStateTitle>
            <EmptyStateText>
              Start by adding your first service to attract customers
            </EmptyStateText>
            <AddFirstServiceButton onPress={() => navigation.navigate('AddService' as never)}>
              <AddFirstServiceButtonText>Add Your First Service</AddFirstServiceButtonText>
            </AddFirstServiceButton>
          </EmptyStateContainer>
        ) : (
          <ScrollView
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[Colors.light.primary]}
              />
            }
          >
            <ServicesContainer>
              <ServicesHeader>
                <ServicesCount>{services.length} Services</ServicesCount>
                <FilterButton onPress={() => {/* TODO: Implement filter */}}>
                  <Ionicons name="filter-outline" size={20} color={Colors.light.primary} />
                  <FilterButtonText>Filter</FilterButtonText>
                </FilterButton>
              </ServicesHeader>

              {services.map((service) => (
                <ServiceCard
                  key={service.id}
                  service={service}
                  onEdit={() => navigation.navigate('EditService' as never, { serviceId: service.id })}
                  onDelete={() => handleDeleteService(service)}
                  onToggleActive={() => handleToggleServiceActive(service)}
                />
              ))}
            </ServicesContainer>

            <QuickActionsContainer>
              <QuickActionTitle>Quick Actions</QuickActionTitle>
              <QuickActionsGrid>
                <QuickActionButton onPress={() => navigation.navigate('BulkServiceEdit' as never)}>
                  <QuickActionIcon name="create-outline" size={24} color={Colors.light.primary} />
                  <QuickActionText>Bulk Edit</QuickActionText>
                </QuickActionButton>
                <QuickActionButton onPress={() => navigation.navigate('ServiceTemplates' as never)}>
                  <QuickActionIcon name="copy-outline" size={24} color={Colors.light.primary} />
                  <QuickActionText>Templates</QuickActionText>
                </QuickActionButton>
                <QuickActionButton onPress={() => navigation.navigate('ServiceAnalytics' as never)}>
                  <QuickActionIcon name="analytics-outline" size={24} color={Colors.light.primary} />
                  <QuickActionText>Analytics</QuickActionText>
                </QuickActionButton>
                <QuickActionButton onPress={() => navigation.navigate('ServiceImport' as never)}>
                  <QuickActionIcon name="download-outline" size={24} color={Colors.light.primary} />
                  <QuickActionText>Import</QuickActionText>
                </QuickActionButton>
              </QuickActionsGrid>
            </QuickActionsContainer>
          </ScrollView>
        )}
      </SafeAreaWrapper>
    </Container>
  );
};

const Container = styled.View`
  flex: 1;
  background-color: ${Colors.light.background};
`;

const Header = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: ${Colors.light.surface};
  border-bottom-width: 1px;
  border-bottom-color: ${Colors.light.border};
`;

const HeaderContent = styled.View`
  flex-direction: row;
  align-items: center;
  flex: 1;
`;

const BackButton = styled.TouchableOpacity`
  padding: 8px;
  margin-right: 12px;
`;

const HeaderTitle = styled.Text`
  font-size: 20px;
  font-weight: bold;
  color: ${Colors.light.text};
`;

const AddServiceButton = styled.TouchableOpacity`
  background-color: ${Colors.light.primary};
  border-radius: 8px;
  padding: 12px;
`;

const ServicesContainer = styled.View`
  padding: 16px;
`;

const ServicesHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const ServicesCount = styled.Text`
  font-size: 18px;
  font-weight: bold;
  color: ${Colors.light.text};
`;

const FilterButton = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  border-width: 1px;
  border-color: ${Colors.light.border};
`;

const FilterButtonText = styled.Text`
  color: ${Colors.light.primary};
  font-weight: 500;
  margin-left: 4px;
`;

const ServiceCardContainer = styled.View`
  background-color: ${Colors.light.surface};
  border-radius: 12px;
  margin-bottom: 16px;
  border-width: 1px;
  border-color: ${Colors.light.border};
  overflow: hidden;
`;

const ServiceImageContainer = styled.View`
  position: relative;
  height: 120px;
`;

const ServiceImage = styled.Image`
  width: 100%;
  height: 100%;
`;

const PlaceholderImage = styled.View`
  width: 100%;
  height: 100%;
  background-color: ${Colors.light.background};
  justify-content: center;
  align-items: center;
`;

const PlaceholderIcon = styled(Ionicons)``;

const ServiceStatusBadge = styled.View<{ isActive: boolean }>`
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: ${props => props.isActive ? Colors.light.success : Colors.light.error};
  border-radius: 12px;
  padding: 4px 8px;
`;

const ServiceStatusText = styled.Text<{ isActive: boolean }>`
  color: white;
  font-size: 12px;
  font-weight: 600;
`;

const ServiceContent = styled.View`
  padding: 16px;
`;

const ServiceHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
`;

const ServiceName = styled.Text`
  font-size: 18px;
  font-weight: bold;
  color: ${Colors.light.text};
  flex: 1;
  margin-right: 12px;
`;

const ServiceActions = styled.View`
  flex-direction: row;
  gap: 8px;
`;

const ActionButton = styled.TouchableOpacity`
  padding: 8px;
  border-radius: 6px;
  background-color: ${Colors.light.background};
`;

const ServiceDescription = styled.Text`
  font-size: 14px;
  color: ${Colors.light.textSecondary};
  line-height: 20px;
  margin-bottom: 12px;
`;

const ServiceDetails = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
`;

const ServiceDetailItem = styled.View`
  flex-direction: row;
  align-items: center;
`;

const ServiceDetailIcon = styled(Ionicons)`
  margin-right: 4px;
`;

const ServiceDetailText = styled.Text`
  font-size: 14px;
  color: ${Colors.light.text};
  font-weight: 500;
`;

const AddOnsContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background-color: ${Colors.light.background};
  border-radius: 8px;
  padding: 8px 12px;
`;

const AddOnsLabel = styled.Text`
  font-size: 12px;
  color: ${Colors.light.textSecondary};
`;

const AddOnsCount = styled.Text`
  font-size: 12px;
  color: ${Colors.light.primary};
  font-weight: 600;
`;

const QuickActionsContainer = styled.View`
  padding: 16px;
  background-color: ${Colors.light.surface};
  margin-top: 16px;
`;

const QuickActionTitle = styled.Text`
  font-size: 18px;
  font-weight: bold;
  color: ${Colors.light.text};
  margin-bottom: 16px;
`;

const QuickActionsGrid = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 12px;
`;

const QuickActionButton = styled.TouchableOpacity`
  background-color: ${Colors.light.background};
  border-radius: 12px;
  padding: 16px;
  width: ${(width - 56) / 2}px;
  align-items: center;
  border-width: 1px;
  border-color: ${Colors.light.border};
`;

const QuickActionIcon = styled(Ionicons)`
  margin-bottom: 8px;
`;

const QuickActionText = styled.Text`
  font-size: 14px;
  font-weight: 600;
  color: ${Colors.light.text};
  text-align: center;
`;

const EmptyStateContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 32px;
`;

const EmptyStateIcon = styled(Ionicons)`
  margin-bottom: 16px;
`;

const EmptyStateTitle = styled.Text`
  font-size: 20px;
  font-weight: bold;
  color: ${Colors.light.text};
  margin-bottom: 8px;
  text-align: center;
`;

const EmptyStateText = styled.Text`
  font-size: 16px;
  color: ${Colors.light.textSecondary};
  text-align: center;
  line-height: 24px;
  margin-bottom: 24px;
`;

const AddFirstServiceButton = styled.TouchableOpacity`
  background-color: ${Colors.light.primary};
  border-radius: 8px;
  padding: 16px 32px;
`;

const AddFirstServiceButtonText = styled.Text`
  color: white;
  font-weight: 600;
  font-size: 16px;
`;

export default ServiceManagementScreen;
