/**
 * System Feedback Display Component
 * 
 * Provides comprehensive visual feedback for system operations.
 * Implements Heuristic H1 (Visibility of System Status) compliance.
 * 
 * Features:
 * - Loading indicators with progress
 * - Success/error state display
 * - Network status indicator
 * - Contextual messages
 * - Accessibility support
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { HyperMinimalistTheme } from '../../design-system/HyperMinimalistTheme';
import { 
  ProgressBar, 
  SystemStatusIndicator, 
  Loading 
} from './LoadingStates';
import { SystemFeedbackState } from '../../hooks/useSystemFeedback';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';

export interface SystemFeedbackDisplayProps {
  feedback: SystemFeedbackState;
  variant?: 'inline' | 'overlay' | 'banner' | 'toast';
  showProgress?: boolean;
  showNetworkStatus?: boolean;
  style?: ViewStyle;
  testID?: string;
}

const defaultFeedback: SystemFeedbackState = {
  isLoading: false,
  progress: 0,
  status: 'idle',
  message: '',
  error: null,
  networkStatus: 'unknown',
};

export const SystemFeedbackDisplay: React.FC<SystemFeedbackDisplayProps> = ({
  feedback = defaultFeedback,
  variant = 'inline',
  showProgress = true,
  showNetworkStatus = true,
  style,
  testID = 'system-feedback-display',
}) => {
  const { isDark } = useTheme();
  const theme = HyperMinimalistTheme;
  const colors = isDark ? theme.darkColors : theme.colors;

  const getStatusIcon = () => {
    if (!feedback || !feedback.status) return null;

    switch (feedback.status) {
      case 'loading':
        return null; // Will show ActivityIndicator
      case 'success':
        return 'checkmark-circle';
      case 'error':
        return 'close-circle';
      case 'offline':
        return 'cloud-offline';
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    if (!feedback || !feedback.status) return colors.gray[500];

    switch (feedback.status) {
      case 'success':
        return colors.success[500];
      case 'error':
        return colors.error[500];
      case 'offline':
        return colors.warning[500];
      case 'loading':
        return colors.accent[400];
      default:
        return colors.gray[500];
    }
  };

  const renderContent = () => {
    if (!feedback || (feedback.status === 'idle' && !feedback.message)) {
      return null;
    }

    return (
      <View style={[styles.container, getVariantStyles(), style]} testID={testID}>
        {/* Network Status */}
        {showNetworkStatus && feedback?.networkStatus && feedback.networkStatus !== 'unknown' && (
          <SystemStatusIndicator
            status={feedback.networkStatus === 'online' ? 'online' : 'offline'}
            style={styles.networkStatus}
            testID={`${testID}-network-status`}
          />
        )}

        {/* Main Content */}
        <View style={styles.content}>
          {/* Status Icon or Loading Indicator */}
          <View style={styles.iconContainer}>
            {feedback?.status === 'loading' ? (
              <Loading size="small" message="" style={styles.loadingIndicator} />
            ) : (
              getStatusIcon() && (
                <Ionicons
                  name={getStatusIcon() as any}
                  size={20}
                  color={getStatusColor()}
                  testID={`${testID}-status-icon`}
                />
              )
            )}
          </View>

          {/* Message */}
          {feedback?.message && (
            <Text
              style={[
                styles.message,
                { color: getStatusColor() },
                variant === 'toast' && styles.toastMessage,
              ]}
              accessibilityLabel={`System status: ${feedback.message}`}
              testID={`${testID}-message`}
            >
              {feedback.message}
            </Text>
          )}

          {/* Error Details */}
          {feedback?.error && (
            <Text
              style={[styles.errorText, { color: colors.error[600] }]}
              accessibilityLabel={`Error: ${feedback.error}`}
              testID={`${testID}-error`}
            >
              {feedback.error}
            </Text>
          )}
        </View>

        {/* Progress Bar */}
        {showProgress && feedback?.status === 'loading' && feedback?.progress && feedback.progress > 0 && (
          <ProgressBar
            progress={feedback.progress}
            style={styles.progressBar}
            color={colors.accent[400]}
            backgroundColor={colors.gray[200]}
            showPercentage={variant !== 'toast'}
            testID={`${testID}-progress`}
          />
        )}
      </View>
    );
  };

  const getVariantStyles = (): ViewStyle => {
    switch (variant) {
      case 'overlay':
        return {
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000,
        };
      case 'banner':
        return {
          backgroundColor: colors.gray[50],
          borderBottomWidth: 1,
          borderBottomColor: colors.gray[200],
          paddingVertical: getResponsiveSpacing(12),
        };
      case 'toast':
        return {
          backgroundColor: colors.gray[900],
          borderRadius: getResponsiveSpacing(8),
          marginHorizontal: getResponsiveSpacing(16),
          marginVertical: getResponsiveSpacing(8),
          shadowColor: colors.shadow.light,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 4,
          elevation: 5,
        };
      default: // inline
        return {
          backgroundColor: colors.gray[50],
          borderRadius: getResponsiveSpacing(8),
          borderWidth: 1,
          borderColor: colors.gray[200],
        };
    }
  };

  return renderContent();
};

const styles = StyleSheet.create({
  container: {
    padding: getResponsiveSpacing(12),
  },
  networkStatus: {
    alignSelf: 'flex-end',
    marginBottom: getResponsiveSpacing(8),
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: getResponsiveSpacing(12),
    minWidth: 24,
    alignItems: 'center',
  },
  loadingIndicator: {
    margin: 0,
  },
  message: {
    flex: 1,
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
    lineHeight: 20,
  },
  toastMessage: {
    color: '#FFFFFF',
  },
  errorText: {
    flex: 1,
    fontSize: getResponsiveFontSize(12),
    marginTop: getResponsiveSpacing(4),
    lineHeight: 16,
  },
  progressBar: {
    marginTop: getResponsiveSpacing(12),
  },
});

export default SystemFeedbackDisplay;
