/**
 * Payment Management Screen - Comprehensive payment and billing interface
 *
 * Screen Contract:
 * - Display payment methods with add, edit, and delete functionality
 * - Show transaction history with filtering and search options
 * - Provide payment analytics and spending insights
 * - Support refund requests and dispute management
 * - Integration with <PERSON>e for secure payment processing
 * - Receipt management and download functionality
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  Alert,
  Dimensions,
} from 'react-native';
import { SafeAreaWrapper } from '../../components/ui/SafeAreaWrapper';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import styled from 'styled-components/native';

import { 
  usePaymentStore, 
  PaymentMethod, 
  PaymentTransaction, 
  PaymentMethodType 
} from '../../store/paymentSlice';
import { useAuthStore } from '../../store/authSlice';
import { Colors } from '../../constants/Colors';
import { LoadingSpinner } from '../../components/common/LoadingSpinner';
import { ErrorMessage } from '../../components/common/ErrorMessage';

const { width } = Dimensions.get('window');

interface PaymentMethodCardProps {
  paymentMethod: PaymentMethod;
  onPress: () => void;
  onSetDefault: () => void;
  onDelete: () => void;
}

interface TransactionCardProps {
  transaction: PaymentTransaction;
  onPress: () => void;
  onRefund: () => void;
}

const PaymentManagementScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAuthStore();
  const {
    paymentMethods,
    defaultPaymentMethod,
    transactions,
    recentTransactions,
    paymentSummary,
    isLoading,
    error,
    loadPaymentMethods,
    loadTransactions,
    loadRecentTransactions,
    loadPaymentSummary,
    setDefaultPaymentMethod,
    deletePaymentMethod,
    requestRefund,
    clearError,
  } = usePaymentStore();

  const [activeTab, setActiveTab] = useState<'methods' | 'transactions' | 'analytics'>('methods');
  const [refreshing, setRefreshing] = useState(false);

  useFocusEffect(
    useCallback(() => {
      if (user?.id) {
        loadData();
      }
    }, [user?.id])
  );

  const loadData = async () => {
    if (!user?.id) return;

    try {
      await Promise.all([
        loadPaymentMethods(user.id),
        loadRecentTransactions(user.id),
        loadPaymentSummary(user.id),
      ]);
    } catch (error) {
      console.error('Failed to load payment data:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadData();
    } catch (error) {
      console.error('Failed to refresh payment data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleSetDefaultPaymentMethod = async (paymentMethodId: string) => {
    try {
      await setDefaultPaymentMethod(paymentMethodId);
    } catch (error) {
      Alert.alert('Error', 'Failed to set default payment method');
    }
  };

  const handleDeletePaymentMethod = (paymentMethod: PaymentMethod) => {
    if (paymentMethod.isDefault && paymentMethods.length > 1) {
      Alert.alert(
        'Cannot Delete',
        'You cannot delete your default payment method. Please set another payment method as default first.'
      );
      return;
    }

    Alert.alert(
      'Delete Payment Method',
      `Are you sure you want to delete this ${paymentMethod.type.replace('_', ' ')} ending in ${paymentMethod.last4}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deletePaymentMethod(paymentMethod.id);
            } catch (error) {
              Alert.alert('Error', 'Failed to delete payment method');
            }
          },
        },
      ]
    );
  };

  const handleRequestRefund = (transaction: PaymentTransaction) => {
    Alert.alert(
      'Request Refund',
      `Request a refund for ${formatCurrency(transaction.amount)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Request Refund',
          onPress: () => showRefundReasons(transaction),
        },
      ]
    );
  };

  const showRefundReasons = (transaction: PaymentTransaction) => {
    const reasons = [
      'Service not provided',
      'Poor service quality',
      'Cancelled appointment',
      'Billing error',
      'Duplicate charge',
      'Other',
    ];

    Alert.alert(
      'Refund Reason',
      'Please select a reason for the refund:',
      [
        ...reasons.map(reason => ({
          text: reason,
          onPress: () => confirmRefund(transaction, reason),
        })),
        { text: 'Back', style: 'cancel' },
      ]
    );
  };

  const confirmRefund = async (transaction: PaymentTransaction, reason: string) => {
    try {
      await requestRefund(transaction.id, transaction.amount, reason);
      Alert.alert('Success', 'Your refund request has been submitted and will be processed within 3-5 business days.');
    } catch (error) {
      Alert.alert('Error', 'Failed to request refund. Please try again.');
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-CA', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getPaymentMethodIcon = (type: PaymentMethodType): string => {
    switch (type) {
      case 'credit_card':
      case 'debit_card':
        return 'card-outline';
      case 'paypal':
        return 'logo-paypal';
      case 'apple_pay':
        return 'logo-apple';
      case 'google_pay':
        return 'logo-google';
      case 'bank_transfer':
        return 'business-outline';
      case 'wallet':
        return 'wallet-outline';
      default:
        return 'card-outline';
    }
  };

  const PaymentMethodCard: React.FC<PaymentMethodCardProps> = ({
    paymentMethod,
    onPress,
    onSetDefault,
    onDelete,
  }) => (
    <PaymentMethodCardContainer onPress={onPress}>
      <PaymentMethodHeader>
        <PaymentMethodInfo>
          <PaymentMethodIcon 
            name={getPaymentMethodIcon(paymentMethod.type) as any} 
            size={24} 
            color={Colors.light.primary} 
          />
          <PaymentMethodDetails>
            <PaymentMethodType>
              {paymentMethod.brand?.toUpperCase()} •••• {paymentMethod.last4}
            </PaymentMethodType>
            <PaymentMethodExpiry>
              Expires {paymentMethod.expiryMonth}/{paymentMethod.expiryYear}
            </PaymentMethodExpiry>
          </PaymentMethodDetails>
        </PaymentMethodInfo>
        {paymentMethod.isDefault && (
          <DefaultBadge>
            <DefaultBadgeText>Default</DefaultBadgeText>
          </DefaultBadge>
        )}
      </PaymentMethodHeader>

      <PaymentMethodActions>
        {!paymentMethod.isDefault && (
          <ActionButton onPress={onSetDefault} variant="secondary">
            <ActionButtonText variant="secondary">Set as Default</ActionButtonText>
          </ActionButton>
        )}
        <ActionButton onPress={onDelete} variant="danger">
          <Ionicons name="trash-outline" size={16} color={Colors.light.error} />
        </ActionButton>
      </PaymentMethodActions>
    </PaymentMethodCardContainer>
  );

  const TransactionCard: React.FC<TransactionCardProps> = ({
    transaction,
    onPress,
    onRefund,
  }) => (
    <TransactionCardContainer onPress={onPress}>
      <TransactionHeader>
        <TransactionInfo>
          <TransactionDescription>{transaction.description}</TransactionDescription>
          <TransactionDate>{formatDate(transaction.createdAt)}</TransactionDate>
        </TransactionInfo>
        <TransactionAmount status={transaction.status}>
          {formatCurrency(transaction.amount)}
        </TransactionAmount>
      </TransactionHeader>

      <TransactionDetails>
        <TransactionStatus status={transaction.status}>
          {transaction.status.replace('_', ' ').toUpperCase()}
        </TransactionStatus>
        <TransactionMethod>
          {transaction.paymentMethod.brand?.toUpperCase()} •••• {transaction.paymentMethod.last4}
        </TransactionMethod>
      </TransactionDetails>

      {transaction.status === 'succeeded' && transaction.refunds.length === 0 && (
        <TransactionActions>
          <ActionButton onPress={onRefund} variant="secondary">
            <ActionButtonText variant="secondary">Request Refund</ActionButtonText>
          </ActionButton>
        </TransactionActions>
      )}
    </TransactionCardContainer>
  );

  if (isLoading && paymentMethods.length === 0) {
    return (
      <Container>
        <LoadingSpinner size="large" />
      </Container>
    );
  }

  return (
    <Container>
      <SafeAreaWrapper style={{ flex: 1 }}>
        <Header>
          <HeaderContent>
            <BackButton onPress={() => navigation.goBack()}>
              <Ionicons name="arrow-back" size={24} color={Colors.light.text} />
            </BackButton>
            <HeaderTitle>Payment & Billing</HeaderTitle>
          </HeaderContent>
          <HeaderActions>
            <HeaderButton onPress={() => navigation.navigate('PaymentSettings' as never)}>
              <Ionicons name="settings-outline" size={20} color={Colors.light.text} />
            </HeaderButton>
          </HeaderActions>
        </Header>

        <TabContainer>
          <TabButton
            active={activeTab === 'methods'}
            onPress={() => setActiveTab('methods')}
          >
            <TabButtonText active={activeTab === 'methods'}>
              Payment Methods
            </TabButtonText>
          </TabButton>
          <TabButton
            active={activeTab === 'transactions'}
            onPress={() => setActiveTab('transactions')}
          >
            <TabButtonText active={activeTab === 'transactions'}>
              Transactions
            </TabButtonText>
          </TabButton>
          <TabButton
            active={activeTab === 'analytics'}
            onPress={() => setActiveTab('analytics')}
          >
            <TabButtonText active={activeTab === 'analytics'}>
              Analytics
            </TabButtonText>
          </TabButton>
        </TabContainer>

        {error && (
          <ErrorMessage
            message={error}
            onRetry={loadData}
            onDismiss={clearError}
          />
        )}

        <ScrollView
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[Colors.light.primary]}
            />
          }
        >
          {activeTab === 'methods' && (
            <PaymentMethodsSection>
              <SectionHeader>
                <SectionTitle>Payment Methods</SectionTitle>
                <AddButton onPress={() => navigation.navigate('AddPaymentMethod' as never)}>
                  <Ionicons name="add" size={20} color="white" />
                  <AddButtonText>Add Method</AddButtonText>
                </AddButton>
              </SectionHeader>

              {paymentMethods.length === 0 ? (
                <EmptyStateContainer>
                  <EmptyStateIcon name="card-outline" size={64} color={Colors.light.textSecondary} />
                  <EmptyStateTitle>No Payment Methods</EmptyStateTitle>
                  <EmptyStateText>
                    Add a payment method to start booking services
                  </EmptyStateText>
                  <AddFirstMethodButton onPress={() => navigation.navigate('AddPaymentMethod' as never)}>
                    <AddFirstMethodButtonText>Add Payment Method</AddFirstMethodButtonText>
                  </AddFirstMethodButton>
                </EmptyStateContainer>
              ) : (
                paymentMethods.map((paymentMethod) => (
                  <PaymentMethodCard
                    key={paymentMethod.id}
                    paymentMethod={paymentMethod}
                    onPress={() => navigation.navigate('PaymentMethodDetails' as never, { paymentMethodId: paymentMethod.id })}
                    onSetDefault={() => handleSetDefaultPaymentMethod(paymentMethod.id)}
                    onDelete={() => handleDeletePaymentMethod(paymentMethod)}
                  />
                ))
              )}
            </PaymentMethodsSection>
          )}

          {activeTab === 'transactions' && (
            <TransactionsSection>
              <SectionHeader>
                <SectionTitle>Recent Transactions</SectionTitle>
                <ViewAllButton onPress={() => navigation.navigate('TransactionHistory' as never)}>
                  <ViewAllButtonText>View All</ViewAllButtonText>
                </ViewAllButton>
              </SectionHeader>

              {recentTransactions.length === 0 ? (
                <EmptyStateContainer>
                  <EmptyStateIcon name="receipt-outline" size={64} color={Colors.light.textSecondary} />
                  <EmptyStateTitle>No Transactions</EmptyStateTitle>
                  <EmptyStateText>
                    Your payment history will appear here
                  </EmptyStateText>
                </EmptyStateContainer>
              ) : (
                recentTransactions.map((transaction) => (
                  <TransactionCard
                    key={transaction.id}
                    transaction={transaction}
                    onPress={() => navigation.navigate('TransactionDetails' as never, { transactionId: transaction.id })}
                    onRefund={() => handleRequestRefund(transaction)}
                  />
                ))
              )}
            </TransactionsSection>
          )}

          {activeTab === 'analytics' && (
            <AnalyticsSection>
              <SectionTitle>Payment Analytics</SectionTitle>
              
              {paymentSummary ? (
                <>
                  <AnalyticsGrid>
                    <AnalyticsCard>
                      <AnalyticsValue>{formatCurrency(paymentSummary.totalAmount)}</AnalyticsValue>
                      <AnalyticsLabel>Total Spent</AnalyticsLabel>
                    </AnalyticsCard>
                    <AnalyticsCard>
                      <AnalyticsValue>{paymentSummary.totalTransactions}</AnalyticsValue>
                      <AnalyticsLabel>Transactions</AnalyticsLabel>
                    </AnalyticsCard>
                    <AnalyticsCard>
                      <AnalyticsValue>{formatCurrency(paymentSummary.averageTransactionAmount)}</AnalyticsValue>
                      <AnalyticsLabel>Average</AnalyticsLabel>
                    </AnalyticsCard>
                    <AnalyticsCard>
                      <AnalyticsValue>{formatCurrency(paymentSummary.refundedAmount)}</AnalyticsValue>
                      <AnalyticsLabel>Refunded</AnalyticsLabel>
                    </AnalyticsCard>
                  </AnalyticsGrid>

                  <MonthlyBreakdownContainer>
                    <MonthlyBreakdownTitle>Monthly Breakdown</MonthlyBreakdownTitle>
                    {paymentSummary.monthlyBreakdown.map((monthData) => (
                      <MonthlyBreakdownItem key={monthData.month}>
                        <MonthlyBreakdownMonth>
                          {new Date(monthData.month + '-01').toLocaleDateString('en-CA', { month: 'long', year: 'numeric' })}
                        </MonthlyBreakdownMonth>
                        <MonthlyBreakdownAmount>
                          {formatCurrency(monthData.totalAmount)}
                        </MonthlyBreakdownAmount>
                      </MonthlyBreakdownItem>
                    ))}
                  </MonthlyBreakdownContainer>
                </>
              ) : (
                <EmptyStateContainer>
                  <EmptyStateIcon name="analytics-outline" size={64} color={Colors.light.textSecondary} />
                  <EmptyStateTitle>No Analytics Data</EmptyStateTitle>
                  <EmptyStateText>
                    Make some payments to see your spending analytics
                  </EmptyStateText>
                </EmptyStateContainer>
              )}
            </AnalyticsSection>
          )}
        </ScrollView>
      </SafeAreaWrapper>
    </Container>
  );
};

const Container = styled.View`
  flex: 1;
  background-color: ${Colors.light.background};
`;

const Header = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: ${Colors.light.surface};
  border-bottom-width: 1px;
  border-bottom-color: ${Colors.light.border};
`;

const HeaderContent = styled.View`
  flex-direction: row;
  align-items: center;
  flex: 1;
`;

const BackButton = styled.TouchableOpacity`
  padding: 8px;
  margin-right: 12px;
`;

const HeaderTitle = styled.Text`
  font-size: 20px;
  font-weight: bold;
  color: ${Colors.light.text};
`;

const HeaderActions = styled.View`
  flex-direction: row;
  gap: 8px;
`;

const HeaderButton = styled.TouchableOpacity`
  padding: 8px;
  border-radius: 6px;
  background-color: ${Colors.light.background};
`;

const TabContainer = styled.View`
  flex-direction: row;
  background-color: ${Colors.light.surface};
  border-bottom-width: 1px;
  border-bottom-color: ${Colors.light.border};
`;

const TabButton = styled.TouchableOpacity<{ active: boolean }>`
  flex: 1;
  padding: 16px;
  align-items: center;
  border-bottom-width: 2px;
  border-bottom-color: ${props => props.active ? Colors.light.primary : 'transparent'};
`;

const TabButtonText = styled.Text<{ active: boolean }>`
  font-size: 14px;
  font-weight: ${props => props.active ? '600' : '500'};
  color: ${props => props.active ? Colors.light.primary : Colors.light.textSecondary};
`;

const PaymentMethodsSection = styled.View`
  padding: 16px;
`;

const TransactionsSection = styled.View`
  padding: 16px;
`;

const AnalyticsSection = styled.View`
  padding: 16px;
`;

const SectionHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const SectionTitle = styled.Text`
  font-size: 18px;
  font-weight: bold;
  color: ${Colors.light.text};
`;

const AddButton = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
  background-color: ${Colors.light.primary};
  border-radius: 8px;
  padding: 8px 12px;
`;

const AddButtonText = styled.Text`
  color: white;
  font-weight: 600;
  font-size: 14px;
  margin-left: 4px;
`;

const ViewAllButton = styled.TouchableOpacity``;

const ViewAllButtonText = styled.Text`
  color: ${Colors.light.primary};
  font-weight: 600;
  font-size: 14px;
`;

const PaymentMethodCardContainer = styled.TouchableOpacity`
  background-color: ${Colors.light.surface};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  border-width: 1px;
  border-color: ${Colors.light.border};
`;

const PaymentMethodHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const PaymentMethodInfo = styled.View`
  flex-direction: row;
  align-items: center;
  flex: 1;
`;

const PaymentMethodIcon = styled(Ionicons)`
  margin-right: 12px;
`;

const PaymentMethodDetails = styled.View`
  flex: 1;
`;

const PaymentMethodType = styled.Text`
  font-size: 16px;
  font-weight: 600;
  color: ${Colors.light.text};
  margin-bottom: 2px;
`;

const PaymentMethodExpiry = styled.Text`
  font-size: 14px;
  color: ${Colors.light.textSecondary};
`;

const DefaultBadge = styled.View`
  background-color: ${Colors.light.success};
  border-radius: 12px;
  padding: 4px 8px;
`;

const DefaultBadgeText = styled.Text`
  color: white;
  font-size: 12px;
  font-weight: 600;
`;

const PaymentMethodActions = styled.View`
  flex-direction: row;
  gap: 8px;
`;

const ActionButton = styled.TouchableOpacity<{ variant: 'primary' | 'secondary' | 'danger' }>`
  flex-direction: row;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  background-color: ${props => 
    props.variant === 'primary' ? Colors.light.primary :
    props.variant === 'danger' ? Colors.light.error + '20' :
    Colors.light.background
  };
  border-width: 1px;
  border-color: ${props => 
    props.variant === 'primary' ? Colors.light.primary :
    props.variant === 'danger' ? Colors.light.error :
    Colors.light.border
  };
`;

const ActionButtonText = styled.Text<{ variant: 'primary' | 'secondary' | 'danger' }>`
  font-size: 12px;
  font-weight: 500;
  color: ${props => 
    props.variant === 'primary' ? 'white' :
    props.variant === 'danger' ? Colors.light.error :
    Colors.light.text
  };
`;

const TransactionCardContainer = styled.TouchableOpacity`
  background-color: ${Colors.light.surface};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  border-width: 1px;
  border-color: ${Colors.light.border};
`;

const TransactionHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
`;

const TransactionInfo = styled.View`
  flex: 1;
  margin-right: 12px;
`;

const TransactionDescription = styled.Text`
  font-size: 16px;
  font-weight: 600;
  color: ${Colors.light.text};
  margin-bottom: 2px;
`;

const TransactionDate = styled.Text`
  font-size: 14px;
  color: ${Colors.light.textSecondary};
`;

const TransactionAmount = styled.Text<{ status: string }>`
  font-size: 18px;
  font-weight: bold;
  color: ${props => props.status === 'succeeded' ? Colors.light.success : Colors.light.text};
`;

const TransactionDetails = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const TransactionStatus = styled.Text<{ status: string }>`
  font-size: 12px;
  font-weight: 600;
  color: ${props => 
    props.status === 'succeeded' ? Colors.light.success :
    props.status === 'failed' ? Colors.light.error :
    Colors.light.warning
  };
`;

const TransactionMethod = styled.Text`
  font-size: 12px;
  color: ${Colors.light.textSecondary};
`;

const TransactionActions = styled.View`
  flex-direction: row;
  gap: 8px;
`;

const AnalyticsGrid = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 24px;
`;

const AnalyticsCard = styled.View`
  background-color: ${Colors.light.surface};
  border-radius: 12px;
  padding: 16px;
  width: ${(width - 56) / 2}px;
  border-width: 1px;
  border-color: ${Colors.light.border};
  align-items: center;
`;

const AnalyticsValue = styled.Text`
  font-size: 20px;
  font-weight: bold;
  color: ${Colors.light.text};
  margin-bottom: 4px;
`;

const AnalyticsLabel = styled.Text`
  font-size: 14px;
  color: ${Colors.light.textSecondary};
  text-align: center;
`;

const MonthlyBreakdownContainer = styled.View`
  background-color: ${Colors.light.surface};
  border-radius: 12px;
  padding: 16px;
  border-width: 1px;
  border-color: ${Colors.light.border};
`;

const MonthlyBreakdownTitle = styled.Text`
  font-size: 16px;
  font-weight: bold;
  color: ${Colors.light.text};
  margin-bottom: 12px;
`;

const MonthlyBreakdownItem = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom-width: 1px;
  border-bottom-color: ${Colors.light.border};
`;

const MonthlyBreakdownMonth = styled.Text`
  font-size: 14px;
  color: ${Colors.light.text};
`;

const MonthlyBreakdownAmount = styled.Text`
  font-size: 14px;
  font-weight: 600;
  color: ${Colors.light.text};
`;

const EmptyStateContainer = styled.View`
  align-items: center;
  padding: 32px;
`;

const EmptyStateIcon = styled(Ionicons)`
  margin-bottom: 16px;
`;

const EmptyStateTitle = styled.Text`
  font-size: 18px;
  font-weight: bold;
  color: ${Colors.light.text};
  margin-bottom: 8px;
  text-align: center;
`;

const EmptyStateText = styled.Text`
  font-size: 14px;
  color: ${Colors.light.textSecondary};
  text-align: center;
  line-height: 20px;
  margin-bottom: 24px;
`;

const AddFirstMethodButton = styled.TouchableOpacity`
  background-color: ${Colors.light.primary};
  border-radius: 8px;
  padding: 12px 24px;
`;

const AddFirstMethodButtonText = styled.Text`
  color: white;
  font-weight: 600;
  font-size: 14px;
`;

export default PaymentManagementScreen;
