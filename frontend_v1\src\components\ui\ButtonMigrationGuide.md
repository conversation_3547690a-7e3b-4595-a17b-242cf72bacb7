# Button Component Migration Guide

## Overview

This guide helps migrate from multiple button implementations to the unified `UnifiedButton` component, ensuring consistency across the application.

## Migration Mapping

### From Button (atoms/Button.tsx)

**Before:**
```tsx
import { Button } from '../atoms/Button';

<Button variant="primary" onPress={handlePress}>
  Save Changes
</Button>
```

**After:**
```tsx
import { UnifiedButton } from '../ui/UnifiedButton';

<UnifiedButton variant="primary" title="Save Changes" onPress={handlePress} />
```

### From StandardizedButton

**Before:**
```tsx
import { StandardizedButton } from '../ui/StandardizedButton';

<StandardizedButton 
  action="save" 
  onPress={handlePress}
  variant="primary"
/>
```

**After:**
```tsx
import { UnifiedButton } from '../ui/UnifiedButton';

<UnifiedButton 
  title="Save" 
  variant="primary" 
  onPress={handlePress}
  icon="save"
/>
```

### From MinimalistButton

**Before:**
```tsx
import { MinimalistButton } from '../ui/MinimalistButton';

<MinimalistButton variant="primary" size="md" onPress={handlePress}>
  Continue
</MinimalistButton>
```

**After:**
```tsx
import { UnifiedButton } from '../ui/UnifiedButton';

<UnifiedButton variant="primary" size="medium" title="Continue" onPress={handlePress} />
```

## Variant Mapping

| Old Variant | New Variant | Notes |
|-------------|-------------|-------|
| `primary` | `primary` | Direct mapping |
| `secondary` | `secondary` | Direct mapping |
| `minimal` | `minimal` | Direct mapping |
| `ghost` | `ghost` | Direct mapping |
| `danger` | `destructive` | Renamed for clarity |
| `outline` | `outline` | Direct mapping |

## Size Mapping

| Old Size | New Size | Notes |
|----------|----------|-------|
| `sm` | `small` | Renamed for clarity |
| `md` | `medium` | Renamed for clarity |
| `lg` | `large` | Renamed for clarity |

## Props Migration

### Common Props (No Change)
- `onPress`
- `disabled`
- `loading`
- `fullWidth`
- `style`
- `testID`
- `accessibilityLabel`
- `accessibilityHint`

### Changed Props

| Old Prop | New Prop | Migration |
|----------|----------|-----------|
| `children` | `title` or `children` | Both supported |
| `action` | `title` + `icon` | Use standardized title and icon |
| `iconPosition` | `iconPosition` | Same API |

## Breaking Changes

1. **Size names**: `sm/md/lg` → `small/medium/large`
2. **Variant names**: `danger` → `destructive`
3. **Action prop**: Removed in favor of explicit `title` and `icon`

## Migration Steps

1. **Replace imports**:
   ```tsx
   // Remove these imports
   import { Button } from '../atoms/Button';
   import { StandardizedButton } from '../ui/StandardizedButton';
   import { MinimalistButton } from '../ui/MinimalistButton';
   
   // Add this import
   import { UnifiedButton } from '../ui/UnifiedButton';
   ```

2. **Update component usage** according to the mapping tables above

3. **Test accessibility** - All buttons now have consistent WCAG compliance

4. **Verify haptic feedback** - Enabled by default, can be disabled with `enableHaptics={false}`

## Benefits After Migration

- ✅ Consistent API across all buttons
- ✅ Unified styling system
- ✅ Better accessibility compliance
- ✅ Standardized interaction patterns
- ✅ Reduced bundle size (single component)
- ✅ Easier maintenance and updates

## Automated Migration Script

Run this script to automatically update imports:

```bash
# Find and replace imports
find src -name "*.tsx" -exec sed -i 's/import { Button } from.*atoms\/Button/import { UnifiedButton as Button } from "..\/ui\/UnifiedButton"/g' {} \;
find src -name "*.tsx" -exec sed -i 's/import { StandardizedButton }/import { UnifiedButton as StandardizedButton }/g' {} \;
find src -name "*.tsx" -exec sed -i 's/import { MinimalistButton }/import { UnifiedButton as MinimalistButton }/g' {} \;
```

## Testing Checklist

After migration, verify:

- [ ] All buttons render correctly
- [ ] Touch targets meet 44px minimum
- [ ] Accessibility labels are present
- [ ] Haptic feedback works on supported devices
- [ ] Loading states display properly
- [ ] Disabled states have correct styling
- [ ] Icons position correctly
- [ ] Full-width buttons expand properly
- [ ] Color contrast meets WCAG standards

## Support

If you encounter issues during migration, check:

1. **Props mapping** - Ensure all props are correctly mapped
2. **Variant names** - Use new variant names
3. **Size names** - Use new size names
4. **Import paths** - Update import statements

For additional help, refer to the UnifiedButton component documentation or contact the development team.
