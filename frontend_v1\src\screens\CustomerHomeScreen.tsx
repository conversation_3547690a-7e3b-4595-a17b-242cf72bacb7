/**
 * Customer Home Screen - Main Dashboard for Customers
 *
 * Component Contract:
 * - Displays personalized home dashboard for customers
 * - Shows service categories and featured providers
 * - Provides search functionality
 * - Handles navigation to service discovery
 * - Follows TDD methodology with comprehensive test coverage
 * - Respects iOS/Android OS features and phone notches
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import {
  useNavigation,
  CompositeNavigationProp,
} from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Alert,
} from 'react-native';

import { Box } from '../components/atoms/Box';
import { IconButton } from '../components/atoms/IconButton';

import { SafeAreaScreen } from '../components/ui/SafeAreaWrapper';
// Dashboard components removed - using simplified layout per user preference
import { HyperMinimalistLayout, HyperMinimalistSection } from '../components/ui/HyperMinimalistLayout';
import { HyperMinimalistText, HeadingText, SubheadingText, BodyText } from '../components/ui/HyperMinimalistText';
import { MicroInteraction, ScaleInteraction } from '../components/ui/MicroInteractions';
// MegaMenu removed - REC-RESP-001: Using thumb-friendly bottom navigation only
import { BreadcrumbNavigation, createBreadcrumbsFromRoute } from '../components/ui/BreadcrumbNavigation';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useI18n } from '../contexts/I18nContext';
import { HyperMinimalistTheme } from '../design-system/HyperMinimalistTheme';
import { testButtonContrast, testSageGreenContrast, findOptimalSageColor } from '../utils/contrastTest';
import {
  ScreenReaderUtils,
  ColorContrastUtils,
  SemanticMarkupUtils
} from '../utils/accessibility';
import {
  TouchTargetUtils,
  // WCAG_STANDARDS - temporarily commented out to debug runtime error
} from '../utils/accessibilityUtils';
import type {
  CustomerTabParamList,
  CustomerStackParamList,
} from '../navigation/types';
import { useProvidersStore } from '../store/providersSlice';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
  getMinimumTouchTarget,
} from '../utils/responsiveUtils';

// Enhanced accessibility components
import { FocusManager } from '../components/accessibility/FocusManager';
import { AccessibleTouchable } from '../components/accessibility/AccessibleTouchable';

// Provider components
import { ProviderCard } from '../components/providers/ProviderCard';

// Booking components
import { BookingCard } from '../components/bookings/BookingCard';

// Store imports
import { useAuthStore } from '../store/authSlice';
import { useUserStore } from '../store/userSlice';
import { useBookingsStore } from '../store/bookingsSlice';

// Services
import { locationService, ProviderWithDistance } from '../services/locationService';

type CustomerHomeScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<CustomerTabParamList, 'Home'>,
  StackNavigationProp<CustomerStackParamList>
>;

export const CustomerHomeScreen: React.FC = () => {
  const { colors } = useTheme();
  const { t } = useI18n();
  const navigation = useNavigation<CustomerHomeScreenNavigationProp>();
  const [greeting, setGreeting] = useState('');
  // selectedWidget state removed - using simplified layout per user preference
  const [nearbyProviders, setNearbyProviders] = useState<ProviderWithDistance[]>([]);
  const [locationLoading, setLocationLoading] = useState(false);
  const { colors } = useTheme();

  // Defensive check for colors object
  if (!colors || !colors.background || !colors.surface || !colors.text || !colors.border) {
    console.warn('CustomerHomeScreen: Colors object is incomplete, using fallback');
    return null; // or a loading component
  }

  const styles = createStyles(colors);

  // Navigation state - MegaMenu removed for thumb-friendly navigation
  // Removed sticky navigation for unified header approach

  // Get providers from store
  const { providers, loading, fetchProviders } = useProvidersStore();
  const featuredProviders = providers.slice(0, 3); // Show first 3 as featured

  // Get auth and user data
  const { isAuthenticated } = useAuthStore();
  const { profile, isFavoriteProvider } = useUserStore();

  // Get bookings data
  const { bookings, loading: bookingsLoading, fetchBookings } = useBookingsStore();
  const recentBookings = bookings.slice(0, 5); // Show first 5 as recent

  // Get favorite providers
  const favoriteProviderIds = profile?.favoriteProviders || [];
  const favoriteProviders = providers.filter(provider =>
    favoriteProviderIds.includes(provider.id)
  );

  // Breadcrumb items for home screen
  const breadcrumbItems = createBreadcrumbsFromRoute('Home');

  useEffect(() => {
    const hour = new Date().getHours();
    if (hour < 12) {
      setGreeting(t('home.greeting.morning'));
    } else if (hour < 18) {
      setGreeting(t('home.greeting.afternoon'));
    } else {
      setGreeting(t('home.greeting.evening'));
    }
  }, [t]);

  useEffect(() => {
    // Fetch providers when component mounts
    fetchProviders();

    // Fetch bookings when component mounts
    fetchBookings();

    // Test button contrast ratios for WCAG compliance
    const buttonTest = testButtonContrast();
    const sageTests = testSageGreenContrast();
    const optimalColor = findOptimalSageColor();

    console.log('\n=== Optimal Sage Color Recommendation ===');
    console.log(`Recommended: ${optimalColor?.name} (${optimalColor?.color})`);

    // Load nearby providers
    loadNearbyProviders();
  }, [fetchProviders, fetchBookings]);

  // Load nearby providers based on location
  const loadNearbyProviders = async () => {
    try {
      setLocationLoading(true);
      const nearby = await locationService.getNearbyProviders(providers, 10); // 10 mile radius
      setNearbyProviders(nearby);
    } catch (error) {
      console.error('Error loading nearby providers:', error);
    } finally {
      setLocationLoading(false);
    }
  };

  const handleSearchPress = () => {
    navigation.navigate('Search');
  };

  const handleCategoryPress = (categoryId: string, categoryName: string) => {
    // Navigate to search screen with category filter
    console.log('Category pressed:', categoryName, 'ID:', categoryId);
    navigation.navigate('Search', {
      category: categoryId,
      categoryName: categoryName
    });
  };

  // Quick action handlers
  const handleBookNow = () => {
    // Navigate to search screen for booking
    navigation.navigate('Search');
  };

  const handleMyBookings = () => {
    // Navigate to bookings screen
    navigation.navigate('Bookings');
  };

  const handleProviderPress = (provider: any) => {
    // Navigate to provider details screen
    const providerId = typeof provider === 'string' ? provider : provider.id;
    navigation.navigate('ProviderDetails', { providerId });
  };

  const getCategoryIconName = (
    categoryName: string,
  ): keyof typeof Ionicons.glyphMap => {
    const iconMap: Record<string, keyof typeof Ionicons.glyphMap> = {
      'Hair & Beauty': 'cut-outline',
      'Spa & Wellness': 'flower-outline',
      Fitness: 'fitness-outline',
      Nails: 'hand-left-outline',
      Massage: 'hand-right-outline',
      Skincare: 'sparkles-outline',
      Makeup: 'color-palette-outline',
      Barber: 'cut-outline',
      Salon: 'brush-outline',
      Therapy: 'heart-outline',
      Nutrition: 'leaf-outline',
    };
    return iconMap[categoryName] || 'ellipse-outline';
  };

  const handleFindNearby = () => {
    // Navigate to search with nearby filter
    navigation.navigate('Search');
    // TODO: Pass filter parameter when search screen supports it
  };

  const handleViewMessages = () => {
    // Navigate to messages screen
    navigation.navigate('Messages');
  };



  const handleSeeAllCategories = () => {
    // Navigate to search screen to see all categories
    navigation.navigate('Search');
  };

  // Navigation handlers for new components
  const handleBreadcrumbNavigate = (screen: string, params?: any) => {
    navigation.navigate(screen as any, params);
  };

  // handleMenuPress removed - REC-RESP-001: Using thumb-friendly bottom navigation only

  const handleNotificationPress = () => {
    // Navigate to notifications screen
    navigation.navigate('Notifications');
  };

  const handleFavoriteToggle = (providerId: string) => {
    // The toggle is handled by the ProviderCard component via useUserStore
    console.log('Favorite toggled for provider:', providerId);
  };

  // Booking handlers
  const handleBookingPress = (booking: any) => {
    navigation.navigate('BookingDetails', { bookingId: booking.id });
  };

  const handleRescheduleBooking = (bookingId: string) => {
    navigation.navigate('RescheduleBooking', { bookingId });
  };

  const handleCancelBooking = (bookingId: string) => {
    // Show confirmation dialog and then cancel booking
    console.log('Cancel booking:', bookingId);
  };

  const categories = [
    {
      id: '1',
      name: 'Barber',
      category: 'barber' as const,
      color: colors.sage400,
      serviceCount: 12,
      icon: 'cut-outline',
      description: 'Traditional barbering and men\'s grooming',
    },
    {
      id: '2',
      name: 'Salon',
      category: 'salon' as const,
      color: colors.sage500,
      serviceCount: 18,
      icon: 'brush-outline',
      description: 'Hair styling, coloring, and treatments',
    },
    {
      id: '3',
      name: 'Nail Services',
      category: 'nails' as const,
      color: colors.sage600,
      serviceCount: 18,
      icon: 'hand-left-outline',
      description: 'Manicures, pedicures, nail art',
    },
    {
      id: '4',
      name: 'Lash Services',
      category: 'lashes' as const,
      color: colors.sage400,
      serviceCount: 12,
      icon: 'eye-outline',
      description: 'Extensions, lifts, tinting',
    },
    {
      id: '5',
      name: 'Braiding',
      category: 'braiding' as const,
      color: colors.sage500,
      serviceCount: 15,
      icon: 'git-branch-outline',
      description: 'Protective styles and braiding',
    },
    {
      id: '6',
      name: 'Skincare',
      category: 'skincare' as const,
      color: colors.sage500,
      serviceCount: 20,
      icon: 'water-outline',
      description: 'Facials, treatments, and skincare',
    },
    {
      id: '7',
      name: 'Massage',
      category: 'massage' as const,
      color: colors.sage600,
      serviceCount: 8,
      icon: 'body-outline',
      description: 'Relaxation and therapeutic massage',
    },
    {
      id: '8',
      name: 'Makeup',
      category: 'makeup' as const,
      color: colors.sage400,
      serviceCount: 16,
      icon: 'color-palette-outline',
      description: 'Special occasion and everyday makeup',
    },
    {
      id: '9',
      name: 'Locs & Twists',
      category: 'hair' as const,
      color: colors.sage500,
      serviceCount: 11,
      icon: 'reorder-three-outline',
      description: 'Loc maintenance and twist styles',
    },
  ];

  // Dashboard widgets removed - using simplified layout per user preference

  // Widget handlers removed - using simplified layout per user preference

  return (
    <FocusManager
      skipToContentId="main-content"
      preventStickyObscure={true}
    >
      <SafeAreaScreen
        backgroundColor={colors.background.primary}
        statusBarStyle="dark-content"
        respectNotch={true}
        respectGestures={true}
        testID="customer-home-screen"
        accessibilityLabel="Customer home screen"
        accessibilityRole="main">

      {/* Breadcrumb Navigation - Moved above main content */}
      <BreadcrumbNavigation
        items={breadcrumbItems}
        onNavigate={handleBreadcrumbNavigate}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        nativeID="main-content"
        accessibilityLabel="Main content area with service categories, featured providers, and nearby options"
        accessibilityRole="scrollbar"
        accessibilityHint="Scroll vertically to browse available services and providers">
        {/* Enhanced Unified Header Section */}
        <Box
          style={styles.header}
          accessibilityRole="banner"
          accessibilityLabel="Welcome header with app branding and notifications">
          <View style={styles.headerTop}>
            <Text
              style={styles.appName}
              accessibilityRole="heading"
              accessibilityLevel={1}>
              Vierla
            </Text>
            <View style={styles.headerButtons}>
              <IconButton
                name="notifications-outline"
                size="medium"
                variant="ghost"
                color="#FFFFFF"
                onPress={handleNotificationPress}
                testID="notifications-button"
                accessibilityLabel="View notifications"
                accessibilityHint="Double tap to check your notifications and updates"
                style={styles.headerActionButton}
              />
            </View>
          </View>
          <Text
            style={styles.greeting}
            accessibilityRole="text"
            accessibilityLabel={`${greeting} greeting message`}>
            {greeting}!
          </Text>
          <Text
            style={styles.subtitle}
            accessibilityRole="text"
            accessibilityLabel="Service selection prompt">
            {t('home.subtitle')}
          </Text>
        </Box>

        {/* Browse Services Section - Moved back to top per user correction */}
        <Box
          style={styles.browseServicesSection}
          accessibilityRole="region"
          accessibilityLabel="Browse Services section">
          <View style={styles.sectionHeader}>
            <Text
              style={styles.sectionTitle}
              accessibilityRole="heading"
              accessibilityLevel={2}
              accessibilityLabel="Browse Services section heading"
              testID="browse-services-heading"
            >
              {t('home.sections.browseServices')}
            </Text>
            <AccessibleTouchable
              onPress={handleSeeAllCategories}
              style={styles.viewAllButton}
              testID="see-all-categories"
              accessibilityLabel="View all service categories"
              accessibilityHint="Double tap to see complete list of service categories"
              accessibilityRole="button"
              variant="secondary"
              size="medium"
              minTouchTarget={44}>
              <Text style={styles.viewAllText}>{t('home.viewAll')}</Text>
            </AccessibleTouchable>
          </View>

          {/* Enhanced Categories Horizontal Scroll - Improved Design System Compliance */}
          <FlatList
            data={categories}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item, index) => item?.id ? `category-${item.id}` : `category-fallback-${index}`}
            contentContainerStyle={styles.categoriesContainer}
            accessibilityRole="list"
            accessibilityLabel={`Service categories list with ${categories.length} categories`}
            accessibilityHint="Swipe left or right to browse service categories"
            renderItem={({ item: category, index }) => {
              if (!category || !category.name || !category.id) return null;

              return (
                <ScaleInteraction
                  style={styles.categoryCard}
                  onPress={() => handleCategoryPress(category.id, category.name)}
                  testID={`category-${category.id}`}
                  accessibilityLabel={`${category.name} category with ${category.serviceCount || 0} services`}
                  accessibilityHint={`Browse ${(category.name || '').toLowerCase()} services`}
                intensity="medium"
                hapticFeedback={true}>

                {/* Icon Container with Enhanced Styling */}
                <View style={[styles.categoryIconContainer, { backgroundColor: category.color }]}>
                  <Ionicons
                    name={category.icon as any}
                    size={28}
                    color={colors.text.onPrimary}
                    style={styles.categoryIcon}
                  />
                </View>

                  {/* Category Information */}
                  <View style={styles.categoryInfo}>
                    <Text style={styles.categoryName}>{category.name}</Text>
                    <Text style={styles.categoryServiceCount}>
                      {category.serviceCount || 0} services
                    </Text>
                  </View>
                </ScaleInteraction>
              );
            }}
          />
        </Box>

        {/* Featured Providers Section */}
        <Box style={styles.featuredSection}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <Text
                style={styles.sectionTitle}
                accessibilityRole="header"
                accessibilityLabel="Featured Providers"
                testID="featured-providers-heading"
              >
                {t('home.sections.featuredProviders')}
              </Text>
              <Text
                style={styles.sectionSubtitle}
                accessibilityLabel="Top-rated professionals near you"
              >
                Top-rated professionals near you
              </Text>
            </View>
            <AccessibleTouchable
              onPress={() => navigation.navigate('Search')}
              style={styles.viewAllButton}
              testID="view-all-stores"
              accessibilityLabel="View all service providers"
              accessibilityHint="Double tap to browse all available service providers"
              accessibilityRole="button"
              variant="secondary"
              size="medium"
              minTouchTarget={44}>
              <Text style={styles.viewAllText}>{t('home.viewAll')}</Text>
            </AccessibleTouchable>
          </View>

          {loading ? (
            <Text style={styles.loadingText}>Loading providers...</Text>
          ) : featuredProviders.length > 0 ? (
            <FlatList
              data={featuredProviders}
              keyExtractor={(item, index) => item?.id ? `featured-${item.id}` : `featured-fallback-${index}`}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.providersContainer}
              renderItem={({ item }) => {
                if (!item || !item.id) return null;

                // Transform provider data to match ProviderCard interface
                const providerData = {
                  id: item.id,
                  name: item.business_name || item.name || 'Provider',
                  businessName: item.business_name,
                  rating: item.rating || 4.5,
                  reviewCount: item.review_count || 0,
                  profileImage: item.profile_image || item.avatar,
                  services: item.services || [],
                  distance: item.distance || '0.5 mi',
                  isVerified: item.is_verified || true,
                  category: item.categories?.[0], // Add category for StoreImage
                  categories: item.categories, // Add categories for StoreImage
                };

                return (
                  <ProviderCard
                    provider={providerData}
                    onPress={handleProviderPress}
                    onFavoriteToggle={handleFavoriteToggle}
                    testID={`featured-provider-${item.id}`}
                  />
                );
              }}
            />
          ) : (
            <Text style={styles.noProvidersText}>No providers available</Text>
          )}
        </Box>

        {/* Favorite Providers Section */}
        <Box style={styles.favoriteSection}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <Text
                style={styles.sectionTitle}
                accessibilityRole="header"
                accessibilityLabel="Favorite Providers"
                testID="favorite-providers-heading"
              >
                Favorite Providers
              </Text>
              <Text
                style={styles.sectionSubtitle}
                accessibilityLabel="Your saved providers"
              >
                Your saved providers
              </Text>
            </View>
            <AccessibleTouchable
              onPress={() => navigation.navigate('Search')}
              style={styles.viewAllButton}
              testID="view-all-favorites"
              accessibilityLabel="View all favorite providers"
              accessibilityHint="Double tap to browse all your favorite providers"
              accessibilityRole="button"
              variant="secondary"
              size="medium"
              minTouchTarget={44}>
              <Text style={styles.viewAllText}>{t('home.viewAll')}</Text>
            </AccessibleTouchable>
          </View>

          {/* Favorite Providers List */}
          <FlatList
            data={favoriteProviders}
            keyExtractor={(item, index) => item?.id ? `favorite-${item.id}` : `favorite-fallback-${index}`}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.providersContainer}
            renderItem={({ item }) => {
              if (!item || !item.id) return null;

              // Transform provider data to match ProviderCard interface
              const providerData = {
                id: item.id,
                name: item.business_name || item.name || 'Provider',
                businessName: item.business_name,
                rating: item.rating || 4.5,
                reviewCount: item.review_count || 0,
                profileImage: item.profile_image || item.avatar,
                services: item.services || [],
                distance: item.distance || '0.5 mi',
                isVerified: item.is_verified || true,
                category: item.categories?.[0], // Add category for StoreImage
                categories: item.categories, // Add categories for StoreImage
              };

              return (
                <ProviderCard
                  provider={providerData}
                  onPress={handleProviderPress}
                  onFavoriteToggle={handleFavoriteToggle}
                  compact={true}
                  testID={`favorite-provider-${item.id}`}
                />
              );
            }}
            ListEmptyComponent={
              <View style={styles.emptyFavoritesContainer}>
                <Text style={styles.emptyFavoritesText}>No favorite providers yet</Text>
                <Text style={styles.emptyFavoritesSubtext}>Heart providers to save them here</Text>
              </View>
            }
          />
        </Box>

        {/* Nearby Providers Section */}
        <Box style={styles.nearbySection}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <Text
                style={styles.sectionTitle}
                accessibilityRole="header"
                accessibilityLabel="Nearby Providers"
                testID="nearby-providers-heading"
              >
                {t('home.sections.nearbyProviders')}
              </Text>
              <Text
                style={styles.sectionSubtitle}
                accessibilityLabel="Providers close to you"
              >
                Providers close to you
              </Text>
            </View>
            <AccessibleTouchable
              onPress={() => navigation.navigate('Search')}
              style={styles.viewAllButton}
              testID="view-all-nearby"
              accessibilityLabel="View all nearby providers"
              accessibilityHint="Double tap to browse all nearby providers"
              accessibilityRole="button"
              variant="secondary"
              size="medium"
              minTouchTarget={44}>
              <Text style={styles.viewAllText}>{t('home.viewAll')}</Text>
            </AccessibleTouchable>
          </View>

          {/* Nearby Providers List */}
          {locationLoading ? (
            <Text style={styles.loadingText}>Finding nearby providers...</Text>
          ) : (
            <FlatList
              data={nearbyProviders}
              keyExtractor={(item, index) => item?.id ? `nearby-${item.id}` : `nearby-fallback-${index}`}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.providersContainer}
              renderItem={({ item }) => {
                // Transform ProviderWithDistance to match ProviderCard interface
                const providerData = {
                  id: item.id,
                  name: item.name,
                  businessName: item.businessName,
                  rating: item.rating,
                  reviewCount: item.reviewCount,
                  profileImage: item.profileImage,
                  services: item.services,
                  distance: locationService.formatDistance(item.distance),
                  isVerified: item.isVerified,
                  category: item.categories?.[0], // Add category for StoreImage
                  categories: item.categories, // Add categories for StoreImage
                };

                return (
                  <ProviderCard
                    provider={providerData}
                    onPress={handleProviderPress}
                    onFavoriteToggle={handleFavoriteToggle}
                    compact={true}
                    testID={`nearby-provider-${item.id}`}
                  />
                );
              }}
              ListEmptyComponent={
                <View style={styles.emptyNearbyContainer}>
                  <Text style={styles.emptyNearbyText}>{t('home.noProviders')}</Text>
                  <Text style={styles.emptyNearbySubtext}>Enable location to find providers near you</Text>
                </View>
              }
            />
          )}
        </Box>

        {/* Recent Bookings & Quick Booking Section */}
        <Box style={styles.recentBookingsSection}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <Text
                style={styles.sectionTitle}
                accessibilityRole="header"
                accessibilityLabel="Recent & Quick Bookings"
                testID="recent-bookings-heading"
              >
                {t('home.sections.recentBookings')}
              </Text>
              <Text
                style={styles.sectionSubtitle}
                accessibilityLabel="Your booking history and quick actions"
              >
                Your booking history and quick actions
              </Text>
            </View>
            <AccessibleTouchable
              onPress={() => navigation.navigate('Bookings')}
              style={styles.viewAllButton}
              testID="view-all-bookings"
              accessibilityLabel="View all bookings"
              accessibilityHint="Double tap to view your complete booking history"
              accessibilityRole="button"
              variant="secondary"
              size="medium"
              minTouchTarget={44}>
              <Text style={styles.viewAllText}>{t('home.viewAll')}</Text>
            </AccessibleTouchable>
          </View>

          {/* Recent Bookings List */}
          {bookingsLoading ? (
            <Text style={styles.loadingText}>Loading bookings...</Text>
          ) : (
            <FlatList
              data={recentBookings}
              keyExtractor={(item, index) => item?.id ? `booking-${item.id}` : `booking-fallback-${index}`}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.bookingsContainer}
              renderItem={({ item }) => (
                <BookingCard
                  booking={item}
                  onPress={handleBookingPress}
                  onReschedule={handleRescheduleBooking}
                  onCancel={handleCancelBooking}
                  compact={true}
                  testID={`recent-booking-${item.id}`}
                />
              )}
              ListEmptyComponent={
                <View style={styles.emptyBookingsContainer}>
                  <Text style={styles.emptyBookingsText}>No recent bookings</Text>
                  <Text style={styles.emptyBookingsSubtext}>Book your first service to get started</Text>
                  <AccessibleTouchable
                    style={styles.quickBookButton}
                    onPress={() => navigation.navigate('Search')}
                    accessibilityLabel="Quick book a service"
                    accessibilityRole="button"
                    variant="primary"
                    size="medium"
                    minTouchTarget={44}>
                    <Text style={styles.quickBookText}>{t('home.quickActions.bookNow')}</Text>
                  </AccessibleTouchable>
                </View>
              }
            />
          )}
        </Box>
      </ScrollView>

      {/* MegaMenu removed - REC-RESP-001: Using thumb-friendly bottom navigation only */}
    </SafeAreaScreen>
    </FocusManager>
  );
};

const createStyles = (colors: typeof Colors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: getResponsiveSpacing(20),
  },
  header: {
    paddingHorizontal: getResponsiveSpacing(20),
    paddingTop: getResponsiveSpacing(16),
    paddingBottom: getResponsiveSpacing(24), // Increased padding for better visual separation
    backgroundColor: colors.sage400, // Light sage green for light mode, dark sage green for dark mode
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(12),
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: getResponsiveSpacing(16), // Increased gap to prevent overlap
    paddingLeft: getResponsiveSpacing(8), // Add padding to prevent edge overlap
  },

  headerActionButton: {
    marginRight: getResponsiveSpacing(2), // Reduced from 8 to 2 for smaller right margin
  },
  helpButton: {
    marginRight: getResponsiveSpacing(8), // Consistent spacing between header buttons
  },
  appName: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: '700',
    color: '#FFFFFF', // White text for better contrast on sage background
    letterSpacing: 1,
  },
  profileButton: {
    padding: getResponsiveSpacing(4),
    minWidth: getMinimumTouchTarget(),
    minHeight: getMinimumTouchTarget(),
    alignItems: 'center',
    justifyContent: 'center',
  },
  greeting: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: 'bold',
    color: '#FFFFFF', // White text for better contrast on sage background
    marginBottom: getResponsiveSpacing(4),
  },
  subtitle: {
    fontSize: getResponsiveFontSize(14),
    color: '#FFFFFF', // White text for better contrast on sage background
    lineHeight: getResponsiveFontSize(20),
  },


  browseServicesSection: {
    paddingHorizontal: getResponsiveSpacing(20),
    paddingTop: getResponsiveSpacing(16), // Added padding space above the browse services section
    marginBottom: getResponsiveSpacing(24),
  },
  // dashboardSection removed - using simplified layout per user preference
  categoriesContainer: {
    paddingLeft: getResponsiveSpacing(2), // Reduced from 4 to 2 for smaller left margin
    paddingRight: getResponsiveSpacing(2), // Reduced from 4 to 2 for smaller right margin
  },
  sectionTitle: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(12), // Reduced padding below the browse services text
    paddingHorizontal: getResponsiveSpacing(4),
    letterSpacing: 0.3,
  },

  categoryCard: {
    width: getResponsiveSpacing(140),
    backgroundColor: colors.surface.secondary,
    borderRadius: getResponsiveSpacing(12), // Reduced from 16 for minimalism
    padding: getResponsiveSpacing(16),
    alignItems: 'center',
    marginRight: getResponsiveSpacing(8), // Reduced from 16 to 8 for smaller margins
    // Hyper-minimalist shadow - subtle depth only
    ...(HyperMinimalistTheme.shadows?.subtle || {}),
    borderWidth: 1,
    borderColor: colors.border.light,
  },
  categoryIconContainer: {
    width: getResponsiveSpacing(56),
    height: getResponsiveSpacing(56),
    borderRadius: getResponsiveSpacing(28),
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: getResponsiveSpacing(12),
    // Hyper-minimalist shadow - very subtle
    ...(HyperMinimalistTheme.shadows?.subtle || {}),
  },
  categoryName: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
    lineHeight: getResponsiveFontSize(18),
    marginBottom: getResponsiveSpacing(4),
  },


  categoryIcon: {
    textAlign: 'center',
  },
  categoryInfo: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  categoryServiceCount: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '500',
    color: colors.text.secondary,
    textAlign: 'center',
    marginTop: getResponsiveSpacing(4),
  },

  categoryServiceIcon: {
    opacity: 0.9,
  },
  quickActionsCompact: {
    paddingHorizontal: getResponsiveSpacing(20),
    marginBottom: getResponsiveSpacing(16),
  },
  quickActionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(12),
  },
  quickActionIconButton: {
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: getMinimumTouchTarget(),
    minHeight: getMinimumTouchTarget(),
  },
  quickActionIconCircle: {
    width: getResponsiveSpacing(40),
    height: getResponsiveSpacing(40),
    borderRadius: getResponsiveSpacing(20),
    backgroundColor: colors.background.secondary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: getResponsiveSpacing(6),
    // Hyper-minimalist shadow - barely visible
    ...(HyperMinimalistTheme.shadows?.subtle || {}),
  },
  quickActionLabel: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '500',
    color: colors.text.secondary,
    textAlign: 'center',
    letterSpacing: 0.2,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    gap: 10,
  },
  quickActionButton: {
    flex: 1,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: getResponsiveSpacing(12),
    justifyContent: 'space-between',
  },
  quickActionCard: {
    width: '48%',
    backgroundColor: colors.surface.secondary,
    borderRadius: getResponsiveSpacing(12), // Reduced for minimalism
    padding: getResponsiveSpacing(16),
    alignItems: 'center',
    // Hyper-minimalist shadow
    ...(HyperMinimalistTheme.shadows?.subtle || {}),
    borderWidth: 1,
    borderColor: colors.border.light,
  },
  quickActionIcon: {
    width: getResponsiveSpacing(48),
    height: getResponsiveSpacing(48),
    borderRadius: getResponsiveSpacing(24),
    backgroundColor: colors.sage100,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(8),
  },
  quickActionTitle: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: getResponsiveSpacing(4),
  },
  quickActionSubtitle: {
    fontSize: getResponsiveFontSize(12),
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: getResponsiveFontSize(16),
  },
  featuredSection: {
    paddingHorizontal: getResponsiveSpacing(20),
    marginBottom: getResponsiveSpacing(24),
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: getResponsiveSpacing(16),
  },
  sectionTitleContainer: {
    flex: 1,
  },
  sectionSubtitle: {
    fontSize: getResponsiveFontSize(12),
    color: colors.text.secondary,
    marginTop: getResponsiveSpacing(2),
    lineHeight: getResponsiveFontSize(16),
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(8),
    paddingVertical: getResponsiveSpacing(4),
    borderRadius: getResponsiveSpacing(8),
    backgroundColor: 'transparent',
  },
  seeAllText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
    color: colors.sage400,
    marginRight: getResponsiveSpacing(4),
  },
  viewAllButton: {
    paddingHorizontal: getResponsiveSpacing(12),
    paddingVertical: getResponsiveSpacing(6),
    borderRadius: getResponsiveSpacing(16),
    backgroundColor: colors.sage400,
  },
  viewAllText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
    color: colors.text.onPrimary,
  },
  comingSoon: {
    fontSize: 16,
    color: '#9CA3AF',
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: getResponsiveSpacing(40),
  },
  noProvidersText: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: getResponsiveSpacing(40),
  },
  providersContainer: {
    paddingLeft: getResponsiveSpacing(4),
    gap: getResponsiveSpacing(12),
  },
  providerCard: {
    width: getResponsiveSpacing(200),
    backgroundColor: colors.surface.secondary,
    borderRadius: getResponsiveSpacing(20),
    padding: getResponsiveSpacing(20),
    marginRight: getResponsiveSpacing(16),
    borderWidth: 1,
    borderColor: colors.border.light,
    position: 'relative',
  },
  featuredBadge: {
    position: 'absolute',
    top: getResponsiveSpacing(8),
    right: getResponsiveSpacing(8),
    backgroundColor: colors.warning + '20',
    borderRadius: getResponsiveSpacing(10),
    paddingHorizontal: getResponsiveSpacing(6),
    paddingVertical: getResponsiveSpacing(3),
    flexDirection: 'row',
    alignItems: 'center',
    gap: getResponsiveSpacing(3),
    zIndex: 10, // Ensure it's above other content
  },
  featuredBadgeText: {
    fontSize: getResponsiveFontSize(10),
    fontWeight: '600',
    color: colors.warning,
  },
  providerImageContainer: {
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(8),
    position: 'relative',
  },
  providerAvatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.sage400,
    alignItems: 'center',
    justifyContent: 'center',
    // Hyper-minimalist shadow - subtle depth
    ...(HyperMinimalistTheme.shadows?.soft || {}),
    borderWidth: 2, // Reduced border width for minimalism
    borderColor: colors.background.primary,
  },
  providerAvatarText: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: '700',
    color: colors.text.onPrimary,
  },

  providerInfo: {
    flex: 1,
    marginTop: getResponsiveSpacing(12),
    paddingTop: getResponsiveSpacing(4), // Add padding to prevent overlap with featured badge
  },
  providerName: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(6),
    lineHeight: getResponsiveFontSize(20),
    minHeight: getResponsiveFontSize(40), // Reserve space for 2 lines (20 * 2)
  },
  providerCategory: {
    fontSize: getResponsiveFontSize(13),
    color: colors.text.secondary,
    marginBottom: getResponsiveSpacing(12),
    fontWeight: '500',
  },
  providerMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(4),
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: getResponsiveSpacing(4),
  },
  providerRating: {
    fontSize: getResponsiveFontSize(12),
    color: colors.text.secondary,
    fontWeight: '500',
  },
  reviewCount: {
    fontSize: getResponsiveFontSize(11),
    color: colors.text.tertiary,
    fontWeight: '400',
  },
  providerDistance: {
    fontSize: getResponsiveFontSize(12),
    color: colors.text.tertiary,
    fontWeight: '500',
  },
  providerActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: getResponsiveSpacing(12), // Increased margin for consistent spacing
    minHeight: getResponsiveSpacing(32), // Ensure consistent height for button alignment
  },
  quickBookButton: {
    backgroundColor: colors.primary.default,
    borderRadius: getResponsiveSpacing(8),
    paddingHorizontal: getResponsiveSpacing(12),
    paddingVertical: getResponsiveSpacing(8), // Increased padding for better touch target
    minWidth: getResponsiveSpacing(60), // Ensure consistent button width
    alignItems: 'center',
    justifyContent: 'center',
  },
  quickBookText: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '600',
    color: colors.text.onPrimary,
  },
  // New section styles
  favoriteSection: {
    paddingHorizontal: getResponsiveSpacing(20),
    marginBottom: getResponsiveSpacing(24),
  },
  nearbySection: {
    paddingHorizontal: getResponsiveSpacing(20),
    marginBottom: getResponsiveSpacing(24),
  },
  recentBookingsSection: {
    paddingHorizontal: getResponsiveSpacing(20),
    marginBottom: getResponsiveSpacing(24),
  },
  emptyFavoritesContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getResponsiveSpacing(32),
    paddingHorizontal: getResponsiveSpacing(20),
  },
  emptyFavoritesText: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: getResponsiveSpacing(8),
  },
  emptyFavoritesSubtext: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.tertiary,
    textAlign: 'center',
    lineHeight: getResponsiveFontSize(20),
  },
  emptyNearbyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getResponsiveSpacing(32),
    paddingHorizontal: getResponsiveSpacing(20),
  },
  emptyNearbyText: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: getResponsiveSpacing(8),
  },
  emptyNearbySubtext: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.tertiary,
    textAlign: 'center',
    lineHeight: getResponsiveFontSize(20),
  },
  emptyBookingsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getResponsiveSpacing(32),
    paddingHorizontal: getResponsiveSpacing(20),
  },
  emptyBookingsText: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: getResponsiveSpacing(8),
  },
  emptyBookingsSubtext: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.tertiary,
    textAlign: 'center',
    lineHeight: getResponsiveFontSize(20),
    marginBottom: getResponsiveSpacing(16),
  },
  bookingsContainer: {
    paddingHorizontal: getResponsiveSpacing(4),
  },
});
