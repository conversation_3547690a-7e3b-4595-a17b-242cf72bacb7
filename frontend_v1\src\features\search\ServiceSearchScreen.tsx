/**
 * Service Search Screen - Advanced service discovery and search
 *
 * Screen Contract:
 * - Provides comprehensive service search functionality
 * - Implements intelligent filtering and sorting options
 * - Displays search results with detailed service information
 * - Supports geolocation-based search and distance filtering
 * - Maintains search history and user preferences
 * - Real-time availability checking and booking integration
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  Alert,
  Dimensions,
  Image,
} from 'react-native';
import { SafeAreaWrapper } from '../../components/ui/SafeAreaWrapper';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import styled from 'styled-components/native';

import { useServiceCatalogStore, Service, SearchFilters } from '../../store/serviceCatalogSlice';
import { Colors } from '../../constants/Colors';
import { LoadingSpinner } from '../../components/common/LoadingSpinner';
import { ErrorMessage } from '../../components/common/ErrorMessage';

const { width } = Dimensions.get('window');

interface ServiceCardProps {
  service: Service;
  onPress: () => void;
  onFavorite: () => void;
  isFavorite: boolean;
}

const ServiceSearchScreen: React.FC = () => {
  const navigation = useNavigation();
  const {
    searchQuery,
    searchFilters,
    searchResults,
    isSearching,
    searchError,
    favoriteServices,
    searchHistory,
    userLocation,
    searchServices,
    loadMoreResults,
    updateFilters,
    clearSearch,
    addToFavorites,
    removeFromFavorites,
    addToRecentlyViewed,
    setUserLocation,
    clearError,
  } = useServiceCatalogStore();

  const [localQuery, setLocalQuery] = useState(searchQuery);
  const [showFilters, setShowFilters] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useFocusEffect(
    useCallback(() => {
      // Request location permission and get user location
      requestLocationPermission();
    }, [])
  );

  const requestLocationPermission = async () => {
    try {
      // Mock location - replace with actual location service
      const mockLocation = {
        latitude: 43.6532,
        longitude: -79.3832,
        address: 'Toronto, ON, Canada',
      };
      setUserLocation(mockLocation);
    } catch (error) {
      console.error('Failed to get location:', error);
    }
  };

  const handleSearch = async (query: string = localQuery) => {
    if (!query.trim()) return;

    const searchQuery = {
      query: query.trim(),
      location: userLocation || undefined,
      filters: searchFilters,
      page: 1,
      limit: 20,
    };

    await searchServices(searchQuery);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      if (searchQuery) {
        await handleSearch(searchQuery);
      }
    } catch (error) {
      console.error('Failed to refresh search:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleLoadMore = async () => {
    if (searchResults?.hasMore && !isSearching) {
      await loadMoreResults();
    }
  };

  const handleServicePress = (service: Service) => {
    addToRecentlyViewed(service.id);
    navigation.navigate('ServiceDetails' as never, { serviceId: service.id });
  };

  const handleFavoriteToggle = (serviceId: string) => {
    if (favoriteServices.includes(serviceId)) {
      removeFromFavorites(serviceId);
    } else {
      addToFavorites(serviceId);
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  const formatDuration = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0 && mins > 0) {
      return `${hours}h ${mins}m`;
    } else if (hours > 0) {
      return `${hours}h`;
    } else {
      return `${mins}m`;
    }
  };

  const ServiceCard: React.FC<ServiceCardProps> = ({
    service,
    onPress,
    onFavorite,
    isFavorite,
  }) => (
    <ServiceCardContainer onPress={onPress}>
      <ServiceImageContainer>
        {service.images && service.images.length > 0 ? (
          <ServiceImage source={{ uri: service.images[0] }} />
        ) : (
          <PlaceholderImage>
            <PlaceholderIcon name="image-outline" size={32} color={Colors.light.textSecondary} />
          </PlaceholderImage>
        )}
        <FavoriteButton onPress={onFavorite}>
          <Ionicons 
            name={isFavorite ? "heart" : "heart-outline"} 
            size={20} 
            color={isFavorite ? Colors.light.error : Colors.light.textSecondary} 
          />
        </FavoriteButton>
        {service.isFeatured && (
          <FeaturedBadge>
            <FeaturedText>Featured</FeaturedText>
          </FeaturedBadge>
        )}
      </ServiceImageContainer>

      <ServiceContent>
        <ServiceHeader>
          <ServiceName numberOfLines={2}>{service.name}</ServiceName>
          <ServiceRating>
            <Ionicons name="star" size={14} color={Colors.light.warning} />
            <RatingText>{service.rating.toFixed(1)}</RatingText>
            <ReviewCount>({service.reviewCount})</ReviewCount>
          </ServiceRating>
        </ServiceHeader>

        <ProviderInfo>
          <ProviderName>{service.provider.businessName}</ProviderName>
          {service.provider.isVerified && (
            <VerifiedBadge>
              <Ionicons name="checkmark-circle" size={14} color={Colors.light.success} />
            </VerifiedBadge>
          )}
        </ProviderInfo>

        <ServiceDetails>
          <ServiceDetailItem>
            <ServiceDetailIcon name="pricetag-outline" size={14} color={Colors.light.success} />
            <ServiceDetailText>{formatCurrency(service.price)}</ServiceDetailText>
          </ServiceDetailItem>
          <ServiceDetailItem>
            <ServiceDetailIcon name="time-outline" size={14} color={Colors.light.info} />
            <ServiceDetailText>{formatDuration(service.duration)}</ServiceDetailText>
          </ServiceDetailItem>
          {service.distance && (
            <ServiceDetailItem>
              <ServiceDetailIcon name="location-outline" size={14} color={Colors.light.primary} />
              <ServiceDetailText>{service.distance.toFixed(1)} km</ServiceDetailText>
            </ServiceDetailItem>
          )}
        </ServiceDetails>

        <AvailabilityInfo>
          {service.availability.isBookable ? (
            <AvailabilityText available>
              Next available: {service.availability.nextAvailable ? 
                new Date(service.availability.nextAvailable).toLocaleDateString() : 'Today'}
            </AvailabilityText>
          ) : (
            <AvailabilityText available={false}>Currently unavailable</AvailabilityText>
          )}
        </AvailabilityInfo>
      </ServiceContent>
    </ServiceCardContainer>
  );

  return (
    <Container>
      <SafeAreaWrapper style={{ flex: 1 }}>
        <Header>
          <SearchContainer>
            <SearchInput
              placeholder="Search for services..."
              value={localQuery}
              onChangeText={setLocalQuery}
              onSubmitEditing={() => handleSearch()}
              returnKeyType="search"
            />
            <SearchButton onPress={() => handleSearch()}>
              <Ionicons name="search" size={20} color="white" />
            </SearchButton>
          </SearchContainer>
          <FilterButton onPress={() => setShowFilters(!showFilters)}>
            <Ionicons name="options-outline" size={20} color={Colors.light.primary} />
          </FilterButton>
        </Header>

        {showFilters && (
          <FiltersContainer>
            <FiltersHeader>
              <FiltersTitle>Filters</FiltersTitle>
              <ClearFiltersButton onPress={() => updateFilters({})}>
                <ClearFiltersText>Clear All</ClearFiltersText>
              </ClearFiltersButton>
            </FiltersHeader>
            <FiltersContent>
              <FilterRow>
                <FilterLabel>Sort by:</FilterLabel>
                <FilterOptions>
                  {['relevance', 'price_low', 'price_high', 'rating', 'distance'].map((option) => (
                    <FilterOption
                      key={option}
                      selected={searchFilters.sortBy === option}
                      onPress={() => updateFilters({ sortBy: option as any })}
                    >
                      <FilterOptionText selected={searchFilters.sortBy === option}>
                        {option.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </FilterOptionText>
                    </FilterOption>
                  ))}
                </FilterOptions>
              </FilterRow>
            </FiltersContent>
          </FiltersContainer>
        )}

        {searchError && (
          <ErrorMessage
            message={searchError}
            onRetry={() => handleSearch()}
            onDismiss={clearError}
          />
        )}

        {!searchResults && !isSearching && (
          <EmptyStateContainer>
            <EmptyStateIcon name="search-outline" size={64} color={Colors.light.textSecondary} />
            <EmptyStateTitle>Discover Amazing Services</EmptyStateTitle>
            <EmptyStateText>
              Search for beauty, wellness, and lifestyle services near you
            </EmptyStateText>
            
            {searchHistory.length > 0 && (
              <SearchHistoryContainer>
                <SearchHistoryTitle>Recent Searches</SearchHistoryTitle>
                {searchHistory.slice(0, 5).map((item) => (
                  <SearchHistoryItem
                    key={item.id}
                    onPress={() => {
                      setLocalQuery(item.query);
                      updateFilters(item.filters);
                      handleSearch(item.query);
                    }}
                  >
                    <Ionicons name="time-outline" size={16} color={Colors.light.textSecondary} />
                    <SearchHistoryText>{item.query}</SearchHistoryText>
                  </SearchHistoryItem>
                ))}
              </SearchHistoryContainer>
            )}
          </EmptyStateContainer>
        )}

        {searchResults && (
          <ResultsContainer>
            <ResultsHeader>
              <ResultsCount>
                {searchResults.totalCount} services found
              </ResultsCount>
              {searchResults.suggestions.length > 0 && (
                <SuggestionsContainer>
                  <SuggestionsLabel>Try:</SuggestionsLabel>
                  {searchResults.suggestions.slice(0, 3).map((suggestion) => (
                    <SuggestionChip
                      key={suggestion}
                      onPress={() => {
                        setLocalQuery(suggestion);
                        handleSearch(suggestion);
                      }}
                    >
                      <SuggestionText>{suggestion}</SuggestionText>
                    </SuggestionChip>
                  ))}
                </SuggestionsContainer>
              )}
            </ResultsHeader>

            <FlatList
              data={searchResults.services}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <ServiceCard
                  service={item}
                  onPress={() => handleServicePress(item)}
                  onFavorite={() => handleFavoriteToggle(item.id)}
                  isFavorite={favoriteServices.includes(item.id)}
                />
              )}
              showsVerticalScrollIndicator={false}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={handleRefresh}
                  colors={[Colors.light.primary]}
                />
              }
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.1}
              ListFooterComponent={() => 
                isSearching ? <LoadingSpinner size="small" /> : null
              }
            />
          </ResultsContainer>
        )}

        {isSearching && !searchResults && (
          <LoadingContainer>
            <LoadingSpinner size="large" />
            <LoadingText>Searching for services...</LoadingText>
          </LoadingContainer>
        )}
      </SafeAreaWrapper>
    </Container>
  );
};

const Container = styled.View`
  flex: 1;
  background-color: ${Colors.light.background};
`;

const Header = styled.View`
  flex-direction: row;
  align-items: center;
  padding: 16px;
  background-color: ${Colors.light.surface};
  border-bottom-width: 1px;
  border-bottom-color: ${Colors.light.border};
`;

const SearchContainer = styled.View`
  flex: 1;
  flex-direction: row;
  align-items: center;
  background-color: ${Colors.light.background};
  border-radius: 8px;
  border-width: 1px;
  border-color: ${Colors.light.border};
  margin-right: 12px;
`;

const SearchInput = styled.TextInput`
  flex: 1;
  padding: 12px 16px;
  font-size: 16px;
  color: ${Colors.light.text};
`;

const SearchButton = styled.TouchableOpacity`
  background-color: ${Colors.light.primary};
  border-radius: 6px;
  padding: 8px;
  margin: 4px;
`;

const FilterButton = styled.TouchableOpacity`
  padding: 12px;
  border-radius: 8px;
  border-width: 1px;
  border-color: ${Colors.light.border};
`;

const FiltersContainer = styled.View`
  background-color: ${Colors.light.surface};
  border-bottom-width: 1px;
  border-bottom-color: ${Colors.light.border};
  padding: 16px;
`;

const FiltersHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const FiltersTitle = styled.Text`
  font-size: 16px;
  font-weight: bold;
  color: ${Colors.light.text};
`;

const ClearFiltersButton = styled.TouchableOpacity``;

const ClearFiltersText = styled.Text`
  color: ${Colors.light.primary};
  font-weight: 500;
`;

const FiltersContent = styled.View``;

const FilterRow = styled.View`
  margin-bottom: 12px;
`;

const FilterLabel = styled.Text`
  font-size: 14px;
  font-weight: 500;
  color: ${Colors.light.text};
  margin-bottom: 8px;
`;

const FilterOptions = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
`;

const FilterOption = styled.TouchableOpacity<{ selected: boolean }>`
  background-color: ${props => props.selected ? Colors.light.primary : Colors.light.background};
  border-radius: 16px;
  padding: 6px 12px;
  border-width: 1px;
  border-color: ${props => props.selected ? Colors.light.primary : Colors.light.border};
`;

const FilterOptionText = styled.Text<{ selected: boolean }>`
  color: ${props => props.selected ? 'white' : Colors.light.text};
  font-size: 12px;
  font-weight: 500;
`;

const EmptyStateContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 32px;
`;

const EmptyStateIcon = styled(Ionicons)`
  margin-bottom: 16px;
`;

const EmptyStateTitle = styled.Text`
  font-size: 20px;
  font-weight: bold;
  color: ${Colors.light.text};
  margin-bottom: 8px;
  text-align: center;
`;

const EmptyStateText = styled.Text`
  font-size: 16px;
  color: ${Colors.light.textSecondary};
  text-align: center;
  line-height: 24px;
  margin-bottom: 24px;
`;

const SearchHistoryContainer = styled.View`
  width: 100%;
  max-width: 300px;
`;

const SearchHistoryTitle = styled.Text`
  font-size: 16px;
  font-weight: bold;
  color: ${Colors.light.text};
  margin-bottom: 12px;
`;

const SearchHistoryItem = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
  padding: 8px 0;
`;

const SearchHistoryText = styled.Text`
  color: ${Colors.light.textSecondary};
  margin-left: 8px;
`;

const ResultsContainer = styled.View`
  flex: 1;
`;

const ResultsHeader = styled.View`
  padding: 16px;
  background-color: ${Colors.light.surface};
  border-bottom-width: 1px;
  border-bottom-color: ${Colors.light.border};
`;

const ResultsCount = styled.Text`
  font-size: 16px;
  font-weight: bold;
  color: ${Colors.light.text};
  margin-bottom: 8px;
`;

const SuggestionsContainer = styled.View`
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
`;

const SuggestionsLabel = styled.Text`
  font-size: 14px;
  color: ${Colors.light.textSecondary};
`;

const SuggestionChip = styled.TouchableOpacity`
  background-color: ${Colors.light.background};
  border-radius: 12px;
  padding: 4px 8px;
  border-width: 1px;
  border-color: ${Colors.light.border};
`;

const SuggestionText = styled.Text`
  font-size: 12px;
  color: ${Colors.light.primary};
`;

const ServiceCardContainer = styled.TouchableOpacity`
  background-color: ${Colors.light.surface};
  border-radius: 12px;
  margin: 8px 16px;
  border-width: 1px;
  border-color: ${Colors.light.border};
  overflow: hidden;
`;

const ServiceImageContainer = styled.View`
  position: relative;
  height: 120px;
`;

const ServiceImage = styled.Image`
  width: 100%;
  height: 100%;
`;

const PlaceholderImage = styled.View`
  width: 100%;
  height: 100%;
  background-color: ${Colors.light.background};
  justify-content: center;
  align-items: center;
`;

const PlaceholderIcon = styled(Ionicons)``;

const FavoriteButton = styled.TouchableOpacity`
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  padding: 6px;
`;

const FeaturedBadge = styled.View`
  position: absolute;
  top: 8px;
  left: 8px;
  background-color: ${Colors.light.warning};
  border-radius: 12px;
  padding: 4px 8px;
`;

const FeaturedText = styled.Text`
  color: white;
  font-size: 10px;
  font-weight: 600;
`;

const ServiceContent = styled.View`
  padding: 16px;
`;

const ServiceHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
`;

const ServiceName = styled.Text`
  font-size: 16px;
  font-weight: bold;
  color: ${Colors.light.text};
  flex: 1;
  margin-right: 12px;
`;

const ServiceRating = styled.View`
  flex-direction: row;
  align-items: center;
`;

const RatingText = styled.Text`
  font-size: 14px;
  font-weight: 600;
  color: ${Colors.light.text};
  margin-left: 2px;
`;

const ReviewCount = styled.Text`
  font-size: 12px;
  color: ${Colors.light.textSecondary};
  margin-left: 4px;
`;

const ProviderInfo = styled.View`
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
`;

const ProviderName = styled.Text`
  font-size: 14px;
  color: ${Colors.light.textSecondary};
  margin-right: 6px;
`;

const VerifiedBadge = styled.View``;

const ServiceDetails = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 8px;
`;

const ServiceDetailItem = styled.View`
  flex-direction: row;
  align-items: center;
`;

const ServiceDetailIcon = styled(Ionicons)`
  margin-right: 4px;
`;

const ServiceDetailText = styled.Text`
  font-size: 12px;
  color: ${Colors.light.text};
  font-weight: 500;
`;

const AvailabilityInfo = styled.View``;

const AvailabilityText = styled.Text<{ available: boolean }>`
  font-size: 12px;
  color: ${props => props.available ? Colors.light.success : Colors.light.error};
  font-weight: 500;
`;

const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 32px;
`;

const LoadingText = styled.Text`
  font-size: 16px;
  color: ${Colors.light.textSecondary};
  margin-top: 16px;
`;

export default ServiceSearchScreen;
