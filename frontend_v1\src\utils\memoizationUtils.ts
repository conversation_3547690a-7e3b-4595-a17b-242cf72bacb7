/**
 * Memoization Utilities for Performance Optimization
 * 
 * This module provides utilities for optimizing React component performance
 * through intelligent memoization strategies.
 * 
 * Features:
 * - Smart component memoization
 * - Custom comparison functions
 * - Performance monitoring
 * - Memory usage optimization
 * - Debugging utilities
 * 
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import React, { useMemo, useCallback, useRef, useEffect } from 'react';

// Performance monitoring for memoization
interface MemoStats {
  componentName: string;
  renderCount: number;
  memoHits: number;
  memoMisses: number;
  lastRenderTime: number;
  averageRenderTime: number;
}

const memoStats = new Map<string, MemoStats>();

/**
 * Enhanced React.memo with performance monitoring
 */
export const createMemoizedComponent = <P extends object>(
  Component: React.ComponentType<P>,
  compareProps?: (prevProps: P, nextProps: P) => boolean,
  componentName?: string
): React.MemoExoticComponent<React.ComponentType<P>> => {
  const name = componentName || Component.displayName || Component.name || 'Anonymous';
  
  // Initialize stats
  if (!memoStats.has(name)) {
    memoStats.set(name, {
      componentName: name,
      renderCount: 0,
      memoHits: 0,
      memoMisses: 0,
      lastRenderTime: 0,
      averageRenderTime: 0,
    });
  }

  const customCompare = (prevProps: P, nextProps: P): boolean => {
    const startTime = performance.now();
    const stats = memoStats.get(name)!;
    
    let shouldSkipRender = false;
    
    if (compareProps) {
      shouldSkipRender = compareProps(prevProps, nextProps);
    } else {
      shouldSkipRender = shallowEqual(prevProps, nextProps);
    }
    
    const compareTime = performance.now() - startTime;
    
    if (shouldSkipRender) {
      stats.memoHits++;
    } else {
      stats.memoMisses++;
      stats.renderCount++;
      stats.lastRenderTime = compareTime;
      stats.averageRenderTime = (stats.averageRenderTime + compareTime) / 2;
    }
    
    memoStats.set(name, stats);
    
    return shouldSkipRender;
  };

  const MemoizedComponent = React.memo(Component, customCompare);
  MemoizedComponent.displayName = `Memoized(${name})`;
  
  return MemoizedComponent;
};

/**
 * Shallow equality comparison for props
 */
export const shallowEqual = <T extends object>(obj1: T, obj2: T): boolean => {
  const keys1 = Object.keys(obj1) as (keyof T)[];
  const keys2 = Object.keys(obj2) as (keyof T)[];

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    if (obj1[key] !== obj2[key]) {
      return false;
    }
  }

  return true;
};

/**
 * Deep equality comparison for complex props
 */
export const deepEqual = (obj1: any, obj2: any): boolean => {
  if (obj1 === obj2) {
    return true;
  }

  if (obj1 == null || obj2 == null) {
    return obj1 === obj2;
  }

  if (typeof obj1 !== typeof obj2) {
    return false;
  }

  if (typeof obj1 !== 'object') {
    return obj1 === obj2;
  }

  if (Array.isArray(obj1) !== Array.isArray(obj2)) {
    return false;
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    if (!keys2.includes(key) || !deepEqual(obj1[key], obj2[key])) {
      return false;
    }
  }

  return true;
};

/**
 * Custom comparison functions for common prop patterns
 */
export const ComparisonFunctions = {
  /**
   * Compare props ignoring specific keys
   */
  ignoreKeys: <T extends object>(keysToIgnore: (keyof T)[]) => 
    (prevProps: T, nextProps: T): boolean => {
      const filteredPrev = { ...prevProps };
      const filteredNext = { ...nextProps };
      
      keysToIgnore.forEach(key => {
        delete filteredPrev[key];
        delete filteredNext[key];
      });
      
      return shallowEqual(filteredPrev, filteredNext);
    },

  /**
   * Compare only specific keys
   */
  onlyKeys: <T extends object>(keysToCompare: (keyof T)[]) => 
    (prevProps: T, nextProps: T): boolean => {
      for (const key of keysToCompare) {
        if (prevProps[key] !== nextProps[key]) {
          return false;
        }
      }
      return true;
    },

  /**
   * Compare arrays by length and reference
   */
  arrayByReference: <T extends { [K in keyof T]: any[] }>(arrayKeys: (keyof T)[]) => 
    (prevProps: T, nextProps: T): boolean => {
      for (const key of arrayKeys) {
        const prevArray = prevProps[key];
        const nextArray = nextProps[key];
        
        if (prevArray.length !== nextArray.length || prevArray !== nextArray) {
          return false;
        }
      }
      
      // Compare other props normally
      const otherKeys = Object.keys(prevProps).filter(k => !arrayKeys.includes(k as keyof T));
      for (const key of otherKeys) {
        if (prevProps[key as keyof T] !== nextProps[key as keyof T]) {
          return false;
        }
      }
      
      return true;
    },

  /**
   * Compare objects by deep equality
   */
  deepCompare: <T extends object>() => 
    (prevProps: T, nextProps: T): boolean => deepEqual(prevProps, nextProps),
};

/**
 * Hook for memoizing expensive calculations
 */
export const useExpensiveMemo = <T>(
  factory: () => T,
  deps: React.DependencyList,
  debugName?: string
): T => {
  const startTimeRef = useRef<number>();
  
  return useMemo(() => {
    startTimeRef.current = performance.now();
    const result = factory();
    const duration = performance.now() - startTimeRef.current;
    
    if (debugName && duration > 10) {
      console.warn(`[Performance] Expensive memo "${debugName}" took ${duration.toFixed(2)}ms`);
    }
    
    return result;
  }, deps);
};

/**
 * Hook for memoizing callback functions with performance monitoring
 */
export const useOptimizedCallback = <T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList,
  debugName?: string
): T => {
  const callCountRef = useRef(0);
  
  return useCallback((...args: Parameters<T>) => {
    callCountRef.current++;
    const startTime = performance.now();
    
    const result = callback(...args);
    
    const duration = performance.now() - startTime;
    if (debugName && duration > 5) {
      console.warn(`[Performance] Callback "${debugName}" took ${duration.toFixed(2)}ms (call #${callCountRef.current})`);
    }
    
    return result;
  }, deps) as T;
};

/**
 * Hook for tracking component render performance
 */
export const useRenderPerformance = (componentName: string) => {
  const renderStartTime = useRef<number>();
  const renderCount = useRef(0);
  
  // Track render start
  renderStartTime.current = performance.now();
  renderCount.current++;
  
  useEffect(() => {
    // Track render end
    const renderTime = performance.now() - renderStartTime.current!;
    
    if (renderTime > 16) { // Longer than one frame (60fps)
      console.warn(`[Performance] ${componentName} render took ${renderTime.toFixed(2)}ms (render #${renderCount.current})`);
    }
    
    // Track in analytics if available
    if (typeof global.analytics !== 'undefined') {
      global.analytics.track('Component Render Performance', {
        componentName,
        renderTime,
        renderCount: renderCount.current,
        timestamp: new Date().toISOString(),
      });
    }
  });
};

/**
 * Get memoization statistics for debugging
 */
export const getMemoizationStats = (): MemoStats[] => {
  return Array.from(memoStats.values()).sort((a, b) => b.renderCount - a.renderCount);
};

/**
 * Clear memoization statistics
 */
export const clearMemoizationStats = (): void => {
  memoStats.clear();
};

/**
 * Log memoization performance report
 */
export const logMemoizationReport = (): void => {
  const stats = getMemoizationStats();
  
  if (stats.length === 0) {
    console.log('[Memoization] No memoization data available');
    return;
  }
  
  console.log('\n=== Memoization Performance Report ===');
  console.log(`Total components tracked: ${stats.length}`);
  
  stats.forEach(stat => {
    const hitRate = stat.memoHits / (stat.memoHits + stat.memoMisses) * 100;
    console.log(`\n${stat.componentName}:`);
    console.log(`  Renders: ${stat.renderCount}`);
    console.log(`  Memo hits: ${stat.memoHits}`);
    console.log(`  Memo misses: ${stat.memoMisses}`);
    console.log(`  Hit rate: ${hitRate.toFixed(1)}%`);
    console.log(`  Avg render time: ${stat.averageRenderTime.toFixed(2)}ms`);
  });
};

/**
 * Predefined memoized components for common use cases
 */
export const MemoizedComponents = {
  /**
   * Memoized list item component
   */
  ListItem: <T extends { id: string | number }>(Component: React.ComponentType<T>) =>
    createMemoizedComponent(
      Component,
      ComparisonFunctions.onlyKeys(['id', 'data']),
      'ListItem'
    ),

  /**
   * Memoized form field component
   */
  FormField: <T extends { value: any; error?: string }>(Component: React.ComponentType<T>) =>
    createMemoizedComponent(
      Component,
      ComparisonFunctions.onlyKeys(['value', 'error', 'disabled']),
      'FormField'
    ),

  /**
   * Memoized card component
   */
  Card: <T extends object>(Component: React.ComponentType<T>) =>
    createMemoizedComponent(
      Component,
      ComparisonFunctions.ignoreKeys(['onPress', 'style']),
      'Card'
    ),
};

export default {
  createMemoizedComponent,
  shallowEqual,
  deepEqual,
  ComparisonFunctions,
  MemoizedComponents,
  useExpensiveMemo,
  useOptimizedCallback,
  useRenderPerformance,
  getMemoizationStats,
  clearMemoizationStats,
  logMemoizationReport,
};
