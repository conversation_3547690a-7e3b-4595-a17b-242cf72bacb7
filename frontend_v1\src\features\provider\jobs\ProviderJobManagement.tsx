/**
 * Provider Job Management - Job Management Component
 * 
 * Phase 1 MVP Implementation:
 * - View, accept, and decline job invitations
 * - View active and completed job details
 * - Basic job status management
 * - Job filtering and search
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import { useI18n } from '../../../contexts/I18nContext';
import { Card } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/Button';
import { Icon } from '../../../components/ui/Icon';
import { SearchInput } from '../../../components/ui/SearchInput';

// Job interfaces
interface Job {
  id: string;
  clientName: string;
  clientAvatar?: string;
  serviceName: string;
  description: string;
  scheduledTime: string;
  duration: number; // in minutes
  location: string;
  earnings: number;
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  requirements?: string[];
  createdAt: string;
}

type JobFilter = 'all' | 'pending' | 'active' | 'completed';

export const ProviderJobManagement: React.FC = () => {
  const { colors } = useTheme();
  const { t } = useI18n();
  
  // State
  const [jobs, setJobs] = useState<Job[]>([]);
  const [filteredJobs, setFilteredJobs] = useState<Job[]>([]);
  const [activeFilter, setActiveFilter] = useState<JobFilter>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Load jobs data
  useEffect(() => {
    loadJobs();
  }, []);

  // Filter jobs when filter or search changes
  useEffect(() => {
    filterJobs();
  }, [jobs, activeFilter, searchQuery]);

  const loadJobs = async () => {
    try {
      setIsLoading(true);
      
      // Mock data for Phase 1 MVP
      const mockJobs: Job[] = [
        {
          id: '1',
          clientName: 'Sarah Johnson',
          serviceName: 'House Cleaning',
          description: 'Deep cleaning for 3-bedroom house, including kitchen and bathrooms',
          scheduledTime: '2025-07-20 10:00 AM',
          duration: 180,
          location: '123 Main St, Toronto, ON',
          earnings: 85,
          status: 'pending',
          priority: 'high',
          requirements: ['Bring cleaning supplies', 'Pet-friendly products'],
          createdAt: '2025-07-19 09:30 AM',
        },
        {
          id: '2',
          clientName: 'Mike Chen',
          serviceName: 'Lawn Care',
          description: 'Weekly lawn maintenance including mowing and edging',
          scheduledTime: '2025-07-20 2:00 PM',
          duration: 120,
          location: '456 Oak Ave, Toronto, ON',
          earnings: 120,
          status: 'accepted',
          priority: 'medium',
          requirements: ['Bring own equipment'],
          createdAt: '2025-07-18 3:15 PM',
        },
        {
          id: '3',
          clientName: 'Emma Wilson',
          serviceName: 'Pet Sitting',
          description: 'Care for 2 cats while owner is away for the weekend',
          scheduledTime: '2025-07-21 9:00 AM',
          duration: 1440, // 24 hours
          location: '789 Pine St, Toronto, ON',
          earnings: 160,
          status: 'in_progress',
          priority: 'high',
          requirements: ['Experience with cats', 'Available for overnight'],
          createdAt: '2025-07-17 11:20 AM',
        },
        {
          id: '4',
          clientName: 'David Brown',
          serviceName: 'Handyman Services',
          description: 'Fix leaky faucet and install new light fixture',
          scheduledTime: '2025-07-18 1:00 PM',
          duration: 90,
          location: '321 Elm St, Toronto, ON',
          earnings: 95,
          status: 'completed',
          priority: 'low',
          requirements: ['Bring tools'],
          createdAt: '2025-07-16 2:45 PM',
        },
      ];

      setJobs(mockJobs);
    } catch (error) {
      console.error('Error loading jobs:', error);
      Alert.alert(
        t('common.error'),
        t('provider.jobs.loadError')
      );
    } finally {
      setIsLoading(false);
    }
  };

  const filterJobs = () => {
    let filtered = jobs;

    // Apply status filter
    if (activeFilter !== 'all') {
      if (activeFilter === 'active') {
        filtered = filtered.filter(job => 
          job.status === 'accepted' || job.status === 'in_progress'
        );
      } else {
        filtered = filtered.filter(job => job.status === activeFilter);
      }
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(job =>
        job.clientName.toLowerCase().includes(query) ||
        job.serviceName.toLowerCase().includes(query) ||
        job.description.toLowerCase().includes(query) ||
        job.location.toLowerCase().includes(query)
      );
    }

    setFilteredJobs(filtered);
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadJobs();
    setIsRefreshing(false);
  };

  const handleJobAction = async (jobId: string, action: 'accept' | 'decline' | 'complete' | 'cancel') => {
    try {
      // Update job status
      setJobs(prevJobs =>
        prevJobs.map(job => {
          if (job.id === jobId) {
            let newStatus: Job['status'];
            switch (action) {
              case 'accept':
                newStatus = 'accepted';
                break;
              case 'decline':
                newStatus = 'cancelled';
                break;
              case 'complete':
                newStatus = 'completed';
                break;
              case 'cancel':
                newStatus = 'cancelled';
                break;
              default:
                return job;
            }
            return { ...job, status: newStatus };
          }
          return job;
        })
      );

      // Show success message
      Alert.alert(
        t('common.success'),
        t(`provider.jobs.${action}Success`)
      );
    } catch (error) {
      console.error(`Error ${action}ing job:`, error);
      Alert.alert(
        t('common.error'),
        t(`provider.jobs.${action}Error`)
      );
    }
  };

  const getStatusColor = (status: Job['status']) => {
    switch (status) {
      case 'pending':
        return colors.warning[500];
      case 'accepted':
        return colors.primary[500];
      case 'in_progress':
        return colors.info[500];
      case 'completed':
        return colors.success[500];
      case 'cancelled':
        return colors.error[500];
      default:
        return colors.text.secondary;
    }
  };

  const getPriorityColor = (priority: Job['priority']) => {
    switch (priority) {
      case 'high':
        return colors.error[500];
      case 'medium':
        return colors.warning[500];
      case 'low':
        return colors.success[500];
      default:
        return colors.text.secondary;
    }
  };

  const renderFilterButton = (filter: JobFilter, label: string) => (
    <TouchableOpacity
      key={filter}
      style={[
        styles.filterButton,
        {
          backgroundColor: activeFilter === filter 
            ? colors.primary[500] 
            : colors.background,
          borderColor: colors.primary[500],
        }
      ]}
      onPress={() => setActiveFilter(filter)}
      accessibilityRole="button"
      accessibilityLabel={label}
      accessibilityState={{ selected: activeFilter === filter }}
    >
      <Text
        style={[
          styles.filterButtonText,
          {
            color: activeFilter === filter 
              ? colors.white 
              : colors.primary[500],
          }
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderJob = (job: Job) => (
    <Card key={job.id} style={styles.jobCard}>
      {/* Job Header */}
      <View style={styles.jobHeader}>
        <View style={styles.jobTitleSection}>
          <Text style={[styles.jobClient, { color: colors.text.primary }]}>
            {job.clientName}
          </Text>
          <View style={styles.jobMeta}>
            <View 
              style={[
                styles.statusBadge,
                { backgroundColor: getStatusColor(job.status) }
              ]}
            >
              <Text style={[styles.statusText, { color: colors.white }]}>
                {t(`provider.jobs.status.${job.status}`)}
              </Text>
            </View>
            <View 
              style={[
                styles.priorityBadge,
                { backgroundColor: getPriorityColor(job.priority) }
              ]}
            >
              <Text style={[styles.priorityText, { color: colors.white }]}>
                {t(`provider.jobs.priority.${job.priority}`)}
              </Text>
            </View>
          </View>
        </View>
        <Text style={[styles.jobEarnings, { color: colors.success[500] }]}>
          ${job.earnings}
        </Text>
      </View>

      {/* Job Details */}
      <Text style={[styles.jobService, { color: colors.text.primary }]}>
        {job.serviceName}
      </Text>
      <Text style={[styles.jobDescription, { color: colors.text.secondary }]}>
        {job.description}
      </Text>
      
      {/* Job Info */}
      <View style={styles.jobInfo}>
        <View style={styles.jobInfoRow}>
          <Icon name="clock" size={16} color={colors.text.secondary} />
          <Text style={[styles.jobInfoText, { color: colors.text.secondary }]}>
            {job.scheduledTime} ({job.duration} min)
          </Text>
        </View>
        <View style={styles.jobInfoRow}>
          <Icon name="map-pin" size={16} color={colors.text.secondary} />
          <Text style={[styles.jobInfoText, { color: colors.text.secondary }]}>
            {job.location}
          </Text>
        </View>
      </View>

      {/* Requirements */}
      {job.requirements && job.requirements.length > 0 && (
        <View style={styles.requirements}>
          <Text style={[styles.requirementsTitle, { color: colors.text.primary }]}>
            {t('provider.jobs.requirements')}:
          </Text>
          {job.requirements.map((req, index) => (
            <Text key={index} style={[styles.requirement, { color: colors.text.secondary }]}>
              • {req}
            </Text>
          ))}
        </View>
      )}

      {/* Job Actions */}
      <View style={styles.jobActions}>
        {job.status === 'pending' && (
          <>
            <Button
              title={t('provider.jobs.decline')}
              variant="outline"
              size="small"
              onPress={() => handleJobAction(job.id, 'decline')}
              style={styles.actionButton}
            />
            <Button
              title={t('provider.jobs.accept')}
              variant="primary"
              size="small"
              onPress={() => handleJobAction(job.id, 'accept')}
              style={styles.actionButton}
            />
          </>
        )}
        {job.status === 'accepted' && (
          <Button
            title={t('provider.jobs.markComplete')}
            variant="primary"
            size="small"
            onPress={() => handleJobAction(job.id, 'complete')}
            style={styles.actionButton}
          />
        )}
        {(job.status === 'accepted' || job.status === 'in_progress') && (
          <Button
            title={t('provider.jobs.viewDetails')}
            variant="outline"
            size="small"
            onPress={() => console.log('View job details:', job.id)}
            style={styles.actionButton}
          />
        )}
      </View>
    </Card>
  );

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
          {t('provider.jobs.loading')}
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Search and Filters */}
      <View style={styles.header}>
        <SearchInput
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder={t('provider.jobs.searchPlaceholder')}
          style={styles.searchInput}
        />
        
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.filtersContainer}
        >
          {renderFilterButton('all', t('provider.jobs.filter.all'))}
          {renderFilterButton('pending', t('provider.jobs.filter.pending'))}
          {renderFilterButton('active', t('provider.jobs.filter.active'))}
          {renderFilterButton('completed', t('provider.jobs.filter.completed'))}
        </ScrollView>
      </View>

      {/* Jobs List */}
      <ScrollView
        style={styles.jobsList}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            tintColor={colors.primary[500]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {filteredJobs.length > 0 ? (
          filteredJobs.map(renderJob)
        ) : (
          <View style={styles.emptyState}>
            <Icon 
              name="briefcase" 
              size={48} 
              color={colors.text.secondary}
              style={styles.emptyIcon}
            />
            <Text style={[styles.emptyTitle, { color: colors.text.primary }]}>
              {t('provider.jobs.noJobs')}
            </Text>
            <Text style={[styles.emptyMessage, { color: colors.text.secondary }]}>
              {t('provider.jobs.noJobsMessage')}
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  searchInput: {
    marginBottom: 16,
  },
  filtersContainer: {
    flexDirection: 'row',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  jobsList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  jobCard: {
    padding: 16,
    marginBottom: 12,
  },
  jobHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  jobTitleSection: {
    flex: 1,
  },
  jobClient: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  jobMeta: {
    flexDirection: 'row',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '500',
  },
  jobEarnings: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  jobService: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  jobDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  jobInfo: {
    marginBottom: 12,
  },
  jobInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  jobInfoText: {
    fontSize: 14,
    marginLeft: 8,
  },
  requirements: {
    marginBottom: 16,
  },
  requirementsTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  requirement: {
    fontSize: 14,
    marginLeft: 8,
  },
  jobActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionButton: {
    marginLeft: 8,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptyMessage: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});
