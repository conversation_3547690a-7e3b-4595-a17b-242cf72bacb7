/**
 * SearchInput Component - Search Input UI Component
 * 
 * A specialized text input component for search functionality
 * with search icon and clear button.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import {
  View,
  TextInput as RNTextInput,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextInputProps as RNTextInputProps,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Icon from './IconLibrary';

interface SearchInputProps extends Omit<RNTextInputProps, 'style'> {
  onClear?: () => void;
  showClearButton?: boolean;
  containerStyle?: ViewStyle;
  inputStyle?: ViewStyle;
}

export const SearchInput: React.FC<SearchInputProps> = ({
  value,
  onChangeText,
  onClear,
  showClearButton = true,
  placeholder = 'Search...',
  containerStyle,
  inputStyle,
  ...textInputProps
}) => {
  const { colors } = useTheme();

  const handleClear = () => {
    onChangeText?.('');
    onClear?.();
  };

  const getContainerStyles = (): ViewStyle => ({
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: colors.border,
  });

  const getInputStyles = (): ViewStyle => ({
    flex: 1,
    fontSize: 16,
    color: colors.text.primary,
    marginLeft: 8,
  });

  return (
    <View style={[getContainerStyles(), containerStyle]}>
      {/* Search Icon */}
      <Icon 
        name="search" 
        size={20} 
        color={colors.text.secondary}
      />

      {/* Text Input */}
      <RNTextInput
        {...textInputProps}
        style={[getInputStyles(), inputStyle]}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={colors.text.secondary}
        accessibilityLabel={placeholder}
        accessibilityRole="search"
      />

      {/* Clear Button */}
      {showClearButton && value && value.length > 0 && (
        <TouchableOpacity
          onPress={handleClear}
          style={styles.clearButton}
          accessibilityRole="button"
          accessibilityLabel="Clear search"
        >
          <Icon 
            name="close" 
            size={18} 
            color={colors.text.secondary}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  clearButton: {
    padding: 4,
    marginLeft: 8,
  },
});
