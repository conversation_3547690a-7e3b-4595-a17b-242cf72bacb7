/**
 * Collapsible Section Component
 * 
 * Provides clean, accessible collapsible sections for organizing
 * information hierarchically and reducing visual clutter.
 * 
 * Features:
 * - Smooth expand/collapse animations
 * - WCAG 2.2 AA compliant
 * - Keyboard navigation support
 * - Screen reader announcements
 * - Customizable styling
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  LayoutAnimation,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { HyperMinimalistTheme } from '../../design-system/HyperMinimalistTheme';
import { AccessibilityUtils } from '../../utils/accessibilityUtils';

export interface CollapsibleSectionProps {
  // Content
  title: string;
  children: React.ReactNode;
  
  // Behavior
  initiallyExpanded?: boolean;
  disabled?: boolean;
  
  // Styling
  headerStyle?: any;
  contentStyle?: any;
  titleStyle?: any;
  
  // Icons
  expandIcon?: keyof typeof Ionicons.glyphMap;
  collapseIcon?: keyof typeof Ionicons.glyphMap;
  
  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  
  // Callbacks
  onToggle?: (isExpanded: boolean) => void;
  onExpand?: () => void;
  onCollapse?: () => void;
}

export const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  children,
  initiallyExpanded = false,
  disabled = false,
  headerStyle,
  contentStyle,
  titleStyle,
  expandIcon = 'chevron-down',
  collapseIcon = 'chevron-up',
  accessibilityLabel,
  accessibilityHint,
  onToggle,
  onExpand,
  onCollapse,
}) => {
  const { isDark } = useTheme();
  const theme = HyperMinimalistTheme;
  const colors = isDark ? theme.darkColors : theme.colors;
  
  const [isExpanded, setIsExpanded] = useState(initiallyExpanded);
  const [contentHeight, setContentHeight] = useState(0);
  const animatedHeight = useRef(new Animated.Value(initiallyExpanded ? 1 : 0)).current;
  const rotateAnim = useRef(new Animated.Value(initiallyExpanded ? 1 : 0)).current;

  // Configure layout animation for smooth transitions
  useEffect(() => {
    if (Platform.OS === 'ios') {
      LayoutAnimation.configureNext({
        duration: 300,
        create: {
          type: LayoutAnimation.Types.easeInEaseOut,
          property: LayoutAnimation.Properties.opacity,
        },
        update: {
          type: LayoutAnimation.Types.easeInEaseOut,
        },
      });
    }
  }, [isExpanded]);

  const toggleSection = () => {
    if (disabled) return;
    
    const newExpandedState = !isExpanded;
    setIsExpanded(newExpandedState);
    
    // Animate height
    Animated.timing(animatedHeight, {
      toValue: newExpandedState ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
    
    // Animate icon rotation
    Animated.timing(rotateAnim, {
      toValue: newExpandedState ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
    
    // Callbacks
    onToggle?.(newExpandedState);
    if (newExpandedState) {
      onExpand?.();
    } else {
      onCollapse?.();
    }
    
    // Screen reader announcement
    const announcement = newExpandedState 
      ? `${title} section expanded` 
      : `${title} section collapsed`;
    AccessibilityUtils.ScreenReaderUtils.announceForAccessibility(announcement);
  };

  const handleContentLayout = (event: any) => {
    const { height } = event.nativeEvent.layout;
    setContentHeight(height);
  };

  const animatedHeightStyle = {
    height: animatedHeight.interpolate({
      inputRange: [0, 1],
      outputRange: [0, contentHeight],
    }),
  };

  const iconRotation = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  return (
    <View style={styles.container}>
      {/* Header */}
      <TouchableOpacity
        style={[styles.header, headerStyle]}
        onPress={toggleSection}
        disabled={disabled}
        accessibilityRole="button"
        accessibilityLabel={accessibilityLabel || `Toggle ${title} section`}
        accessibilityHint={accessibilityHint || `Tap to ${isExpanded ? 'collapse' : 'expand'} this section`}
        accessibilityState={{
          expanded: isExpanded,
          disabled,
        }}
      >
        <Text style={[styles.title, { color: colors.text.primary }, titleStyle]}>
          {title}
        </Text>
        
        <Animated.View style={{ transform: [{ rotate: iconRotation }] }}>
          <Ionicons
            name={isExpanded ? collapseIcon : expandIcon}
            size={20}
            color={disabled ? colors.gray[400] : colors.gray[600]}
          />
        </Animated.View>
      </TouchableOpacity>
      
      {/* Content */}
      <Animated.View style={[styles.contentContainer, animatedHeightStyle]}>
        <View
          style={[styles.content, contentStyle]}
          onLayout={handleContentLayout}
        >
          {children}
        </View>
      </Animated.View>
    </View>
  );
};

// Helper component for FAQ-style sections
export const FAQSection: React.FC<{
  question: string;
  answer: string;
  initiallyExpanded?: boolean;
}> = ({ question, answer, initiallyExpanded = false }) => {
  const { isDark } = useTheme();
  const theme = HyperMinimalistTheme;
  const colors = isDark ? theme.darkColors : theme.colors;
  
  return (
    <CollapsibleSection
      title={question}
      initiallyExpanded={initiallyExpanded}
      titleStyle={styles.faqQuestion}
    >
      <Text style={[styles.faqAnswer, { color: colors.text.secondary }]}>
        {answer}
      </Text>
    </CollapsibleSection>
  );
};

// Helper component for details sections
export const DetailsSection: React.FC<{
  title: string;
  details: Array<{ label: string; value: string }>;
  initiallyExpanded?: boolean;
}> = ({ title, details, initiallyExpanded = false }) => {
  const { isDark } = useTheme();
  const theme = HyperMinimalistTheme;
  const colors = isDark ? theme.darkColors : theme.colors;
  
  return (
    <CollapsibleSection
      title={title}
      initiallyExpanded={initiallyExpanded}
    >
      <View style={styles.detailsList}>
        {details.map((detail, index) => (
          <View key={index} style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.text.secondary }]}>
              {detail.label}:
            </Text>
            <Text style={[styles.detailValue, { color: colors.text.primary }]}>
              {detail.value}
            </Text>
          </View>
        ))}
      </View>
    </CollapsibleSection>
  );
};

const styles = StyleSheet.create({
  container: {
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    minHeight: 56, // WCAG minimum touch target
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 12,
  },
  contentContainer: {
    overflow: 'hidden',
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  faqQuestion: {
    fontSize: 15,
    fontWeight: '500',
  },
  faqAnswer: {
    fontSize: 14,
    lineHeight: 20,
  },
  detailsList: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '400',
    textAlign: 'right',
    flex: 1,
  },
});
