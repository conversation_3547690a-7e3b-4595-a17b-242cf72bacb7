/**
 * Canadian Tax System
 * 
 * Comprehensive tax calculation system for Canadian provinces and territories
 * with support for GST, PST, HST, and QST calculations.
 * 
 * Features:
 * - Accurate provincial tax rates
 * - GST/PST/HST/QST calculations
 * - Tax-inclusive and tax-exclusive pricing
 * - Tax exemption handling
 * - Service-specific tax rules
 * - Real-time tax rate updates
 * - Detailed tax breakdowns
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { CanadianProvince, CANADIAN_PROVINCES } from './canadianFormats';
import { formatCanadianCurrency } from './canadianFormats';
import { SupportedLocale } from './i18n';

// Tax types
export type TaxType = 'GST' | 'PST' | 'HST' | 'QST';

// Service categories for tax purposes
export type ServiceCategory = 
  | 'general' 
  | 'essential' 
  | 'medical' 
  | 'educational' 
  | 'financial' 
  | 'digital';

// Tax exemption reasons
export type TaxExemptionReason = 
  | 'essential_service' 
  | 'medical_service' 
  | 'educational_service' 
  | 'non_profit' 
  | 'government' 
  | 'export';

// Tax calculation result
export interface TaxCalculationResult {
  subtotal: number;
  taxes: {
    gst: {
      rate: number;
      amount: number;
      applicable: boolean;
    };
    pst: {
      rate: number;
      amount: number;
      applicable: boolean;
      name: string; // PST or QST
    };
    hst: {
      rate: number;
      amount: number;
      applicable: boolean;
    };
  };
  totalTax: number;
  total: number;
  breakdown: TaxBreakdownItem[];
  exemptions: TaxExemption[];
}

// Tax breakdown item
export interface TaxBreakdownItem {
  type: TaxType;
  name: string;
  rate: number;
  amount: number;
  description: string;
}

// Tax exemption
export interface TaxExemption {
  type: TaxType;
  reason: TaxExemptionReason;
  description: string;
  savedAmount: number;
}

// Tax calculation options
export interface TaxCalculationOptions {
  province: CanadianProvince;
  serviceCategory?: ServiceCategory;
  exemptions?: TaxExemptionReason[];
  taxInclusive?: boolean;
  locale?: SupportedLocale;
}

// Service category tax rules
const SERVICE_TAX_RULES: Record<ServiceCategory, {
  gstExempt: boolean;
  pstExempt: boolean;
  hstExempt: boolean;
  description: string;
}> = {
  general: {
    gstExempt: false,
    pstExempt: false,
    hstExempt: false,
    description: 'Standard taxable services',
  },
  essential: {
    gstExempt: true,
    pstExempt: true,
    hstExempt: true,
    description: 'Essential services (basic groceries, medical)',
  },
  medical: {
    gstExempt: true,
    pstExempt: true,
    hstExempt: true,
    description: 'Medical and health services',
  },
  educational: {
    gstExempt: true,
    pstExempt: true,
    hstExempt: true,
    description: 'Educational services',
  },
  financial: {
    gstExempt: true,
    pstExempt: false,
    hstExempt: true,
    description: 'Financial services',
  },
  digital: {
    gstExempt: false,
    pstExempt: false,
    hstExempt: false,
    description: 'Digital services and products',
  },
};

/**
 * Calculate Canadian taxes for a given amount
 */
export function calculateCanadianTaxes(
  amount: number,
  options: TaxCalculationOptions
): TaxCalculationResult {
  const {
    province,
    serviceCategory = 'general',
    exemptions = [],
    taxInclusive = false,
    locale = 'en-CA',
  } = options;

  const provinceData = CANADIAN_PROVINCES[province];
  const serviceRules = SERVICE_TAX_RULES[serviceCategory];
  
  // Determine base amount (before tax)
  let subtotal = amount;
  if (taxInclusive) {
    subtotal = calculateSubtotalFromTaxInclusive(amount, province, serviceCategory, exemptions);
  }

  // Calculate individual taxes
  const gstApplicable = !serviceRules.gstExempt && !exemptions.includes('essential_service');
  const pstApplicable = !serviceRules.pstExempt && !exemptions.includes('essential_service');
  const hstApplicable = !serviceRules.hstExempt && !exemptions.includes('essential_service');

  const gstAmount = gstApplicable ? subtotal * provinceData.gst : 0;
  const pstAmount = pstApplicable ? subtotal * provinceData.pst : 0;
  const hstAmount = hstApplicable ? subtotal * provinceData.hst : 0;

  const totalTax = gstAmount + pstAmount + hstAmount;
  const total = subtotal + totalTax;

  // Create breakdown
  const breakdown: TaxBreakdownItem[] = [];
  
  if (gstAmount > 0) {
    breakdown.push({
      type: 'GST',
      name: 'GST',
      rate: provinceData.gst,
      amount: gstAmount,
      description: 'Goods and Services Tax',
    });
  }

  if (pstAmount > 0) {
    const pstName = province === 'QC' ? 'QST' : 'PST';
    const pstDescription = province === 'QC' 
      ? 'Quebec Sales Tax' 
      : 'Provincial Sales Tax';
    
    breakdown.push({
      type: province === 'QC' ? 'QST' : 'PST',
      name: pstName,
      rate: provinceData.pst,
      amount: pstAmount,
      description: pstDescription,
    });
  }

  if (hstAmount > 0) {
    breakdown.push({
      type: 'HST',
      name: 'HST',
      rate: provinceData.hst,
      amount: hstAmount,
      description: 'Harmonized Sales Tax',
    });
  }

  // Calculate exemptions
  const exemptionDetails: TaxExemption[] = [];
  
  if (serviceRules.gstExempt && provinceData.gst > 0) {
    exemptionDetails.push({
      type: 'GST',
      reason: 'essential_service',
      description: 'GST exemption for essential services',
      savedAmount: subtotal * provinceData.gst,
    });
  }

  return {
    subtotal,
    taxes: {
      gst: {
        rate: provinceData.gst,
        amount: gstAmount,
        applicable: gstApplicable,
      },
      pst: {
        rate: provinceData.pst,
        amount: pstAmount,
        applicable: pstApplicable,
        name: province === 'QC' ? 'QST' : 'PST',
      },
      hst: {
        rate: provinceData.hst,
        amount: hstAmount,
        applicable: hstApplicable,
      },
    },
    totalTax,
    total,
    breakdown,
    exemptions: exemptionDetails,
  };
}

/**
 * Calculate subtotal from tax-inclusive amount
 */
function calculateSubtotalFromTaxInclusive(
  taxInclusiveAmount: number,
  province: CanadianProvince,
  serviceCategory: ServiceCategory,
  exemptions: TaxExemptionReason[]
): number {
  const provinceData = CANADIAN_PROVINCES[province];
  const serviceRules = SERVICE_TAX_RULES[serviceCategory];
  
  let totalTaxRate = 0;
  
  if (!serviceRules.gstExempt && !exemptions.includes('essential_service')) {
    totalTaxRate += provinceData.gst;
  }
  
  if (!serviceRules.pstExempt && !exemptions.includes('essential_service')) {
    totalTaxRate += provinceData.pst;
  }
  
  if (!serviceRules.hstExempt && !exemptions.includes('essential_service')) {
    totalTaxRate += provinceData.hst;
  }
  
  return taxInclusiveAmount / (1 + totalTaxRate);
}

/**
 * Format tax breakdown for display
 */
export function formatTaxBreakdown(
  result: TaxCalculationResult,
  locale: SupportedLocale = 'en-CA'
): {
  subtotalFormatted: string;
  taxBreakdown: Array<{
    name: string;
    rate: string;
    amount: string;
  }>;
  totalTaxFormatted: string;
  totalFormatted: string;
} {
  const subtotalFormatted = formatCanadianCurrency(result.subtotal, { locale });
  const totalTaxFormatted = formatCanadianCurrency(result.totalTax, { locale });
  const totalFormatted = formatCanadianCurrency(result.total, { locale });
  
  const taxBreakdown = result.breakdown.map(item => ({
    name: item.name,
    rate: `${(item.rate * 100).toFixed(item.rate < 0.1 ? 2 : 1)}%`,
    amount: formatCanadianCurrency(item.amount, { locale }),
  }));
  
  return {
    subtotalFormatted,
    taxBreakdown,
    totalTaxFormatted,
    totalFormatted,
  };
}

/**
 * Get effective tax rate for a province and service category
 */
export function getEffectiveTaxRate(
  province: CanadianProvince,
  serviceCategory: ServiceCategory = 'general'
): number {
  const provinceData = CANADIAN_PROVINCES[province];
  const serviceRules = SERVICE_TAX_RULES[serviceCategory];
  
  let effectiveRate = 0;
  
  if (!serviceRules.gstExempt) {
    effectiveRate += provinceData.gst;
  }
  
  if (!serviceRules.pstExempt) {
    effectiveRate += provinceData.pst;
  }
  
  if (!serviceRules.hstExempt) {
    effectiveRate += provinceData.hst;
  }
  
  return effectiveRate;
}

/**
 * Check if a service is tax-exempt
 */
export function isServiceTaxExempt(
  serviceCategory: ServiceCategory,
  taxType?: TaxType
): boolean {
  const rules = SERVICE_TAX_RULES[serviceCategory];
  
  if (!taxType) {
    return rules.gstExempt && rules.pstExempt && rules.hstExempt;
  }
  
  switch (taxType) {
    case 'GST':
      return rules.gstExempt;
    case 'PST':
    case 'QST':
      return rules.pstExempt;
    case 'HST':
      return rules.hstExempt;
    default:
      return false;
  }
}

/**
 * Get tax information for a province
 */
export function getProvinceTaxInfo(province: CanadianProvince): {
  province: string;
  gst: { rate: number; applicable: boolean };
  pst: { rate: number; applicable: boolean; name: string };
  hst: { rate: number; applicable: boolean };
  effectiveRate: number;
  taxSystem: 'GST+PST' | 'HST' | 'GST only';
} {
  const data = CANADIAN_PROVINCES[province];
  
  let taxSystem: 'GST+PST' | 'HST' | 'GST only';
  if (data.hst > 0) {
    taxSystem = 'HST';
  } else if (data.pst > 0) {
    taxSystem = 'GST+PST';
  } else {
    taxSystem = 'GST only';
  }
  
  return {
    province: data.name,
    gst: {
      rate: data.gst,
      applicable: data.gst > 0,
    },
    pst: {
      rate: data.pst,
      applicable: data.pst > 0,
      name: province === 'QC' ? 'QST' : 'PST',
    },
    hst: {
      rate: data.hst,
      applicable: data.hst > 0,
    },
    effectiveRate: data.gst + data.pst + data.hst,
    taxSystem,
  };
}

/**
 * Validate tax calculation
 */
export function validateTaxCalculation(result: TaxCalculationResult): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  // Check if subtotal + taxes = total
  const calculatedTotal = result.subtotal + result.totalTax;
  if (Math.abs(calculatedTotal - result.total) > 0.01) {
    errors.push('Total calculation mismatch');
  }
  
  // Check if individual tax amounts sum to total tax
  const summedTaxes = result.taxes.gst.amount + result.taxes.pst.amount + result.taxes.hst.amount;
  if (Math.abs(summedTaxes - result.totalTax) > 0.01) {
    errors.push('Tax breakdown mismatch');
  }
  
  // Check for negative values
  if (result.subtotal < 0 || result.totalTax < 0 || result.total < 0) {
    errors.push('Negative values detected');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Export commonly used functions
export default {
  calculateCanadianTaxes,
  formatTaxBreakdown,
  getEffectiveTaxRate,
  isServiceTaxExempt,
  getProvinceTaxInfo,
  validateTaxCalculation,
  SERVICE_TAX_RULES,
};
