/**
 * Lazy-Loaded Screen Components for Performance Optimization
 * 
 * This module provides lazy-loaded versions of major screens to improve
 * initial bundle size and loading performance.
 * 
 * Features:
 * - React.lazy() implementation for code splitting
 * - Suspense boundaries with loading states
 * - Error boundaries for failed imports
 * - Preloading strategies for critical screens
 * - Performance monitoring integration
 * 
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import React, { Suspense } from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsive';

// Loading component for Suspense fallback
const ScreenLoadingSpinner: React.FC<{ screenName?: string }> = ({ screenName }) => {
  const { colors } = useTheme();
  
  return (
    <View style={[styles.loadingContainer, { backgroundColor: colors.background.primary }]}>
      <ActivityIndicator 
        size="large" 
        color={colors.sage400} 
        testID="screen-loading-spinner"
      />
      <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
        {screenName ? `Loading ${screenName}...` : 'Loading...'}
      </Text>
    </View>
  );
};

// Error boundary for lazy loading failures
class LazyLoadErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType },
  { hasError: boolean }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy loading error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || (() => (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Failed to load screen. Please try again.
          </Text>
        </View>
      ));
      return <FallbackComponent />;
    }

    return this.props.children;
  }
}

// Higher-order component for lazy screen wrapping
const withLazyLoading = (
  importFn: () => Promise<{ default: React.ComponentType<any> }>,
  screenName: string,
  preload: boolean = false
) => {
  const LazyComponent = React.lazy(importFn);
  
  // Preload the component if specified
  if (preload) {
    // Preload after a short delay to not block initial render
    setTimeout(() => {
      importFn().catch(() => {
        // Silently handle preload errors
      });
    }, 100);
  }

  const WrappedComponent: React.FC<any> = (props) => (
    <LazyLoadErrorBoundary>
      <Suspense fallback={<ScreenLoadingSpinner screenName={screenName} />}>
        <LazyComponent {...props} />
      </Suspense>
    </LazyLoadErrorBoundary>
  );

  WrappedComponent.displayName = `Lazy${screenName}`;
  return WrappedComponent;
};

// Lazy-loaded screen components
export const LazyProfileScreen = withLazyLoading(
  () => import('../../screens/ProfileScreen'),
  'Profile',
  false // Don't preload profile screen
);

export const LazySearchScreen = withLazyLoading(
  () => import('../../features/service-discovery/SearchScreen'),
  'Search',
  true // Preload search screen as it's commonly used
);

export const LazyCustomerHomeScreen = withLazyLoading(
  () => import('../../features/customer/CustomerHomeScreen'),
  'Home',
  true // Preload home screen as it's critical
);

export const LazyProviderDetailsScreen = withLazyLoading(
  () => import('../../features/provider/ProviderDetailsScreen'),
  'Provider Details',
  false // Don't preload provider details
);

export const LazyBookingScreen = withLazyLoading(
  () => import('../../features/booking/BookingScreen'),
  'Booking',
  false // Don't preload booking screen
);

export const LazyPaymentScreen = withLazyLoading(
  () => import('../../features/payment/PaymentScreen'),
  'Payment',
  false // Don't preload payment screen
);

export const LazyNotificationsScreen = withLazyLoading(
  () => import('../../features/notifications/NotificationsScreen'),
  'Notifications',
  false // Don't preload notifications
);

export const LazySettingsScreen = withLazyLoading(
  () => import('../../features/settings/SettingsScreen'),
  'Settings',
  false // Don't preload settings
);

// Preloading utilities
export const preloadCriticalScreens = () => {
  // Preload screens that are likely to be accessed soon
  const criticalScreens = [
    () => import('../../features/service-discovery/SearchScreen'),
    () => import('../../features/customer/CustomerHomeScreen'),
  ];

  criticalScreens.forEach((importFn, index) => {
    setTimeout(() => {
      importFn().catch(() => {
        // Silently handle preload errors
      });
    }, index * 200); // Stagger preloading to avoid blocking
  });
};

export const preloadUserSpecificScreens = (userType: 'customer' | 'provider') => {
  if (userType === 'customer') {
    // Preload customer-specific screens
    setTimeout(() => {
      import('../../features/booking/BookingScreen').catch(() => {});
    }, 500);
  } else if (userType === 'provider') {
    // Preload provider-specific screens
    setTimeout(() => {
      import('../../features/provider/ProviderDashboard').catch(() => {});
    }, 500);
  }
};

// Performance monitoring for lazy loading
export const trackLazyLoadPerformance = (screenName: string) => {
  const startTime = performance.now();
  
  return {
    onLoadComplete: () => {
      const loadTime = performance.now() - startTime;
      console.log(`[Performance] ${screenName} lazy load time: ${loadTime.toFixed(2)}ms`);
      
      // Track in analytics if available
      if (typeof global.analytics !== 'undefined') {
        global.analytics.track('Screen Lazy Load', {
          screenName,
          loadTime,
          timestamp: new Date().toISOString(),
        });
      }
    }
  };
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(20),
  },
  loadingText: {
    marginTop: getResponsiveSpacing(16),
    fontSize: getResponsiveFontSize(16),
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(20),
  },
  errorText: {
    fontSize: getResponsiveFontSize(16),
    textAlign: 'center',
    color: '#FF6B6B',
  },
});

export default {
  LazyProfileScreen,
  LazySearchScreen,
  LazyCustomerHomeScreen,
  LazyProviderDetailsScreen,
  LazyBookingScreen,
  LazyPaymentScreen,
  LazyNotificationsScreen,
  LazySettingsScreen,
  preloadCriticalScreens,
  preloadUserSpecificScreens,
  trackLazyLoadPerformance,
};
