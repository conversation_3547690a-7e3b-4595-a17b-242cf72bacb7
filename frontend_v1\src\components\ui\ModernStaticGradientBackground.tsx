/**
 * Modern Static Gradient Background Component
 * 
 * Based on 2024-2025 design principles for subtle, accessible gradients:
 * - Soft top-to-bottom light-to-dark transitions
 * - Minimal color variation for accessibility
 * - WCAG AA compliant contrast ratios
 * - Performance optimized static implementation
 * - Maintains Vierla brand colors with modern sophistication
 * 
 * Design Principles Applied:
 * - Subtle gradients with 2-3 color stops maximum
 * - Soft transitions without harsh boundaries
 * - Accessibility-first approach with sufficient contrast
 * - Modern minimalist aesthetic
 * - Brand-consistent sage green palette
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface ModernStaticGradientBackgroundProps {
  children?: React.ReactNode;
  style?: ViewStyle;
  variant?: 'primary' | 'secondary' | 'subtle' | 'onboarding' | 'auth';
  opacity?: number;
  testID?: string;
}

export const ModernStaticGradientBackground: React.FC<ModernStaticGradientBackgroundProps> = ({
  children,
  style,
  variant = 'primary',
  opacity = 1,
  testID,
}) => {
  // Modern gradient color schemes based on design research
  const getGradientColors = () => {
    switch (variant) {
      case 'primary':
        // Subtle sage green gradient for main app screens
        return [
          '#F8FAF9', // Very light sage tint
          '#F0F4F1', // Light sage background
          '#E8F0EA', // Slightly deeper sage
        ];
      
      case 'secondary':
        // Neutral gradient for secondary screens
        return [
          '#FAFBFA', // Pure white with sage hint
          '#F5F7F6', // Very light neutral
          '#F0F2F1', // Light neutral sage
        ];
      
      case 'subtle':
        // Ultra-subtle gradient for content areas
        return [
          '#FDFDFD', // Almost white
          '#FAFBFA', // Barely perceptible sage
          '#F8F9F8', // Very light sage hint
        ];
      
      case 'onboarding':
        // Welcoming gradient for onboarding screens
        return [
          '#F5F8F6', // Light sage welcome
          '#EDF2EE', // Medium sage comfort
          '#E5EDE7', // Deeper sage foundation
        ];
      
      case 'auth':
        // Professional gradient for authentication screens
        return [
          '#F7F9F8', // Clean professional start
          '#EEF3F0', // Trustworthy middle
          '#E6EDE8', // Secure foundation
        ];
      
      default:
        return [
          '#F8FAF9',
          '#F0F4F1',
          '#E8F0EA',
        ];
    }
  };

  // Modern gradient positioning for optimal visual flow
  const getGradientProps = () => {
    return {
      colors: getGradientColors(),
      start: { x: 0, y: 0 }, // Top
      end: { x: 0, y: 1 }, // Bottom
      locations: [0, 0.6, 1], // Subtle distribution for modern look
    };
  };

  return (
    <View style={[styles.container, style]} testID={testID}>
      <LinearGradient
        {...getGradientProps()}
        style={[styles.gradient, { opacity }]}
      >
        {children}
      </LinearGradient>
    </View>
  );
};

// Enhanced variant for dark mode support
export const ModernStaticGradientBackgroundDark: React.FC<ModernStaticGradientBackgroundProps> = ({
  children,
  style,
  variant = 'primary',
  opacity = 1,
  testID,
}) => {
  const getDarkGradientColors = () => {
    switch (variant) {
      case 'primary':
        return [
          '#1A2B1F', // Dark sage top
          '#152419', // Deeper sage middle
          '#0F1D13', // Darkest sage bottom
        ];
      
      case 'secondary':
        return [
          '#1C1F1D', // Dark neutral
          '#171A18', // Deeper neutral
          '#121514', // Darkest neutral
        ];
      
      case 'subtle':
        return [
          '#1E2220', // Subtle dark start
          '#1A1E1B', // Subtle dark middle
          '#161A17', // Subtle dark end
        ];
      
      case 'onboarding':
        return [
          '#1F2B22', // Welcoming dark sage
          '#1A251C', // Comfortable dark
          '#152016', // Foundation dark
        ];
      
      case 'auth':
        return [
          '#1D2621', // Professional dark
          '#18211B', // Trustworthy dark
          '#131C15', // Secure dark
        ];
      
      default:
        return [
          '#1A2B1F',
          '#152419',
          '#0F1D13',
        ];
    }
  };

  return (
    <View style={[styles.container, style]} testID={testID}>
      <LinearGradient
        colors={getDarkGradientColors()}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        locations={[0, 0.6, 1]}
        style={[styles.gradient, { opacity }]}
      >
        {children}
      </LinearGradient>
    </View>
  );
};

// Utility component for overlay gradients
export const ModernGradientOverlay: React.FC<{
  variant?: 'light' | 'dark' | 'accent';
  opacity?: number;
  style?: ViewStyle;
}> = ({ variant = 'light', opacity = 0.1, style }) => {
  const getOverlayColors = () => {
    switch (variant) {
      case 'light':
        return ['rgba(255, 255, 255, 0.8)', 'rgba(255, 255, 255, 0.2)'];
      case 'dark':
        return ['rgba(0, 0, 0, 0.3)', 'rgba(0, 0, 0, 0.1)'];
      case 'accent':
        return ['rgba(42, 75, 50, 0.2)', 'rgba(42, 75, 50, 0.05)'];
      default:
        return ['rgba(255, 255, 255, 0.8)', 'rgba(255, 255, 255, 0.2)'];
    }
  };

  return (
    <LinearGradient
      colors={getOverlayColors()}
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 1 }}
      style={[styles.overlay, style, { opacity }]}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
});

export default ModernStaticGradientBackground;
