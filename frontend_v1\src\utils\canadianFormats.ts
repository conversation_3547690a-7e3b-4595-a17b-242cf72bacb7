/**
 * Canadian Technical Formats
 * 
 * Comprehensive formatting utilities for Canadian market standards
 * including dates, currency, postal codes, phone numbers, and addresses.
 * 
 * Features:
 * - Canadian date formats (DD/MM/YYYY, DD-MM-YYYY)
 * - Canadian currency (CAD) with proper symbols
 * - Canadian postal code validation and formatting
 * - Canadian phone number formatting
 * - Provincial tax calculations
 * - Address formatting
 * - Bilingual support
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { SupportedLocale } from './i18n';

// Canadian provinces and territories with tax rates
export const CANADIAN_PROVINCES = {
  AB: { name: 'Alberta', gst: 0.05, pst: 0.00, hst: 0.00 },
  BC: { name: 'British Columbia', gst: 0.05, pst: 0.07, hst: 0.00 },
  MB: { name: 'Manitoba', gst: 0.05, pst: 0.07, hst: 0.00 },
  NB: { name: 'New Brunswick', gst: 0.00, pst: 0.00, hst: 0.15 },
  NL: { name: 'Newfoundland and Labrador', gst: 0.00, pst: 0.00, hst: 0.15 },
  NS: { name: 'Nova Scotia', gst: 0.00, pst: 0.00, hst: 0.15 },
  ON: { name: 'Ontario', gst: 0.00, pst: 0.00, hst: 0.13 },
  PE: { name: 'Prince Edward Island', gst: 0.00, pst: 0.00, hst: 0.15 },
  QC: { name: 'Quebec', gst: 0.05, pst: 0.09975, hst: 0.00 }, // QST instead of PST
  SK: { name: 'Saskatchewan', gst: 0.05, pst: 0.06, hst: 0.00 },
  NT: { name: 'Northwest Territories', gst: 0.05, pst: 0.00, hst: 0.00 },
  NU: { name: 'Nunavut', gst: 0.05, pst: 0.00, hst: 0.00 },
  YT: { name: 'Yukon', gst: 0.05, pst: 0.00, hst: 0.00 },
} as const;

export type CanadianProvince = keyof typeof CANADIAN_PROVINCES;

// Date format preferences
export const CANADIAN_DATE_FORMATS = {
  'en-CA': {
    short: 'DD/MM/YYYY',
    medium: 'MMM DD, YYYY',
    long: 'MMMM DD, YYYY',
    full: 'dddd, MMMM DD, YYYY',
  },
  'fr-CA': {
    short: 'DD/MM/YYYY',
    medium: 'DD MMM YYYY',
    long: 'DD MMMM YYYY',
    full: 'dddd DD MMMM YYYY',
  },
} as const;

/**
 * Format currency in Canadian dollars
 */
export function formatCanadianCurrency(
  amount: number,
  options: {
    locale?: SupportedLocale;
    showCents?: boolean;
    showSymbol?: boolean;
    compact?: boolean;
  } = {}
): string {
  const {
    locale = 'en-CA',
    showCents = true,
    showSymbol = true,
    compact = false,
  } = options;

  if (compact && Math.abs(amount) >= 1000) {
    const formatter = new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: 'CAD',
      notation: 'compact',
      compactDisplay: 'short',
    });
    return formatter.format(amount);
  }

  const formatter = new Intl.NumberFormat(locale, {
    style: showSymbol ? 'currency' : 'decimal',
    currency: 'CAD',
    minimumFractionDigits: showCents ? 2 : 0,
    maximumFractionDigits: showCents ? 2 : 0,
  });

  let formatted = formatter.format(amount);

  // Add currency symbol manually if not using currency style
  if (!showSymbol && showCents) {
    formatted = `$${formatted}`;
  }

  return formatted;
}

/**
 * Format date in Canadian format
 */
export function formatCanadianDate(
  date: Date,
  options: {
    locale?: SupportedLocale;
    format?: 'short' | 'medium' | 'long' | 'full';
    separator?: '/' | '-' | '.';
  } = {}
): string {
  const {
    locale = 'en-CA',
    format = 'medium',
    separator = '/',
  } = options;

  if (format === 'short') {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}${separator}${month}${separator}${year}`;
  }

  const formatOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: format === 'medium' ? 'short' : 'long',
    day: 'numeric',
  };

  if (format === 'full') {
    formatOptions.weekday = 'long';
  }

  return new Intl.DateTimeFormat(locale, formatOptions).format(date);
}

/**
 * Format time in Canadian format (12-hour with AM/PM)
 */
export function formatCanadianTime(
  date: Date,
  options: {
    locale?: SupportedLocale;
    format?: '12h' | '24h';
    showSeconds?: boolean;
  } = {}
): string {
  const {
    locale = 'en-CA',
    format = '12h',
    showSeconds = false,
  } = options;

  const formatOptions: Intl.DateTimeFormatOptions = {
    hour: 'numeric',
    minute: '2-digit',
    hour12: format === '12h',
  };

  if (showSeconds) {
    formatOptions.second = '2-digit';
  }

  return new Intl.DateTimeFormat(locale, formatOptions).format(date);
}

/**
 * Validate Canadian postal code
 */
export function validateCanadianPostalCode(postalCode: string): boolean {
  // Remove spaces and convert to uppercase
  const cleaned = postalCode.replace(/\s/g, '').toUpperCase();
  
  // Canadian postal code pattern: A1A1A1
  const pattern = /^[A-Z]\d[A-Z]\d[A-Z]\d$/;
  
  return pattern.test(cleaned);
}

/**
 * Format Canadian postal code
 */
export function formatCanadianPostalCode(postalCode: string): string {
  if (!validateCanadianPostalCode(postalCode)) {
    return postalCode; // Return original if invalid
  }

  const cleaned = postalCode.replace(/\s/g, '').toUpperCase();
  return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
}

/**
 * Validate Canadian phone number
 */
export function validateCanadianPhoneNumber(phoneNumber: string): boolean {
  const digits = phoneNumber.replace(/\D/g, '');
  
  // Canadian phone numbers: 10 digits or 11 digits starting with 1
  return (digits.length === 10) || (digits.length === 11 && digits.startsWith('1'));
}

/**
 * Format Canadian phone number
 */
export function formatCanadianPhoneNumber(phoneNumber: string): string {
  if (!validateCanadianPhoneNumber(phoneNumber)) {
    return phoneNumber; // Return original if invalid
  }

  const digits = phoneNumber.replace(/\D/g, '');

  if (digits.length === 10) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
  } else if (digits.length === 11 && digits.startsWith('1')) {
    return `+1 (${digits.slice(1, 4)}) ${digits.slice(4, 7)}-${digits.slice(7)}`;
  }

  return phoneNumber;
}

/**
 * Calculate Canadian taxes
 */
export function calculateCanadianTaxes(
  amount: number,
  province: CanadianProvince
): {
  subtotal: number;
  gst: number;
  pst: number;
  hst: number;
  qst: number;
  total: number;
  breakdown: string[];
} {
  const provinceData = CANADIAN_PROVINCES[province];
  
  const gst = amount * provinceData.gst;
  const pst = amount * provinceData.pst;
  const hst = amount * provinceData.hst;
  const qst = province === 'QC' ? pst : 0; // QST for Quebec
  
  const total = amount + gst + pst + hst;
  
  const breakdown: string[] = [];
  if (gst > 0) breakdown.push(`GST (${(provinceData.gst * 100).toFixed(1)}%)`);
  if (pst > 0 && province !== 'QC') breakdown.push(`PST (${(provinceData.pst * 100).toFixed(1)}%)`);
  if (qst > 0) breakdown.push(`QST (${(provinceData.pst * 100).toFixed(2)}%)`);
  if (hst > 0) breakdown.push(`HST (${(provinceData.hst * 100).toFixed(1)}%)`);
  
  return {
    subtotal: amount,
    gst,
    pst: province === 'QC' ? 0 : pst,
    hst,
    qst,
    total,
    breakdown,
  };
}

/**
 * Format Canadian address
 */
export function formatCanadianAddress(address: {
  street: string;
  city: string;
  province: CanadianProvince;
  postalCode: string;
  country?: string;
}): string {
  const formattedPostalCode = formatCanadianPostalCode(address.postalCode);
  const provinceName = CANADIAN_PROVINCES[address.province].name;
  
  return [
    address.street,
    `${address.city}, ${address.province} ${formattedPostalCode}`,
    address.country || 'Canada',
  ].join('\n');
}

/**
 * Get Canadian business hours format
 */
export function formatCanadianBusinessHours(
  hours: { open: string; close: string },
  locale: SupportedLocale = 'en-CA'
): string {
  const openTime = new Date(`2000-01-01T${hours.open}`);
  const closeTime = new Date(`2000-01-01T${hours.close}`);
  
  const formattedOpen = formatCanadianTime(openTime, { locale });
  const formattedClose = formatCanadianTime(closeTime, { locale });
  
  return `${formattedOpen} - ${formattedClose}`;
}

/**
 * Format Canadian distance (metric system)
 */
export function formatCanadianDistance(
  distanceInKm: number,
  options: {
    locale?: SupportedLocale;
    unit?: 'km' | 'm';
    precision?: number;
  } = {}
): string {
  const {
    locale = 'en-CA',
    unit = 'km',
    precision = 1,
  } = options;

  if (unit === 'm' || distanceInKm < 1) {
    const meters = distanceInKm * 1000;
    return `${Math.round(meters)} m`;
  }

  const formatter = new Intl.NumberFormat(locale, {
    minimumFractionDigits: 0,
    maximumFractionDigits: precision,
  });

  return `${formatter.format(distanceInKm)} km`;
}

/**
 * Format Canadian temperature (Celsius)
 */
export function formatCanadianTemperature(
  celsius: number,
  options: {
    locale?: SupportedLocale;
    showUnit?: boolean;
    precision?: number;
  } = {}
): string {
  const {
    locale = 'en-CA',
    showUnit = true,
    precision = 0,
  } = options;

  const formatter = new Intl.NumberFormat(locale, {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision,
  });

  const formatted = formatter.format(celsius);
  return showUnit ? `${formatted}°C` : formatted;
}

/**
 * Validate Canadian SIN (Social Insurance Number)
 */
export function validateCanadianSIN(sin: string): boolean {
  const digits = sin.replace(/\D/g, '');
  
  if (digits.length !== 9) return false;
  
  // Luhn algorithm for SIN validation
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    let digit = parseInt(digits[i]);
    
    if (i % 2 === 1) {
      digit *= 2;
      if (digit > 9) {
        digit = Math.floor(digit / 10) + (digit % 10);
      }
    }
    
    sum += digit;
  }
  
  return sum % 10 === 0;
}

/**
 * Format Canadian SIN
 */
export function formatCanadianSIN(sin: string): string {
  const digits = sin.replace(/\D/g, '');
  
  if (digits.length === 9) {
    return `${digits.slice(0, 3)}-${digits.slice(3, 6)}-${digits.slice(6)}`;
  }
  
  return sin; // Return original if not 9 digits
}

/**
 * Get Canadian holiday dates for a given year
 */
export function getCanadianHolidays(year: number): Array<{
  name: string;
  date: Date;
  provinces: CanadianProvince[];
}> {
  // This is a simplified version - in production, use a proper holiday library
  return [
    {
      name: "New Year's Day",
      date: new Date(year, 0, 1),
      provinces: Object.keys(CANADIAN_PROVINCES) as CanadianProvince[],
    },
    {
      name: "Canada Day",
      date: new Date(year, 6, 1),
      provinces: Object.keys(CANADIAN_PROVINCES) as CanadianProvince[],
    },
    // Add more holidays as needed
  ];
}

// Export all formatting functions as a default object
export default {
  formatCanadianCurrency,
  formatCanadianDate,
  formatCanadianTime,
  validateCanadianPostalCode,
  formatCanadianPostalCode,
  validateCanadianPhoneNumber,
  formatCanadianPhoneNumber,
  calculateCanadianTaxes,
  formatCanadianAddress,
  formatCanadianBusinessHours,
  formatCanadianDistance,
  formatCanadianTemperature,
  validateCanadianSIN,
  formatCanadianSIN,
  getCanadianHolidays,
  CANADIAN_PROVINCES,
};
