/**
 * Runtime Error Handler - Comprehensive Error Recovery System
 *
 * This module provides comprehensive runtime error handling and recovery
 * mechanisms to prevent app crashes and provide graceful fallbacks.
 *
 * Addresses persistent runtime errors including:
 * - Theme property access errors
 * - Module loading failures
 * - Component rendering errors
 * - Network and API errors
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Alert, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Error types
export enum ErrorType {
  THEME_ACCESS = 'THEME_ACCESS',
  MODULE_LOADING = 'MODULE_LOADING',
  COMPONENT_RENDER = 'COMPONENT_RENDER',
  NETWORK = 'NETWORK',
  STORAGE = 'STORAGE',
  UNKNOWN = 'UNKNOWN',
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

// Error context interface
export interface ErrorContext {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  stack?: string;
  component?: string;
  props?: any;
  timestamp: number;
  userAgent?: string;
  appVersion?: string;
}

// Error recovery strategies
export interface RecoveryStrategy {
  canRecover: boolean;
  action: () => void;
  fallback?: any;
  retryCount?: number;
  maxRetries?: number;
}

class RuntimeErrorHandler {
  private errorLog: ErrorContext[] = [];
  private maxLogSize = 100;
  private recoveryStrategies: Map<ErrorType, RecoveryStrategy> = new Map();

  constructor() {
    this.initializeRecoveryStrategies();
    this.setupGlobalErrorHandlers();
  }

  /**
   * Initialize recovery strategies for different error types
   */
  private initializeRecoveryStrategies(): void {
    // Theme access error recovery
    this.recoveryStrategies.set(ErrorType.THEME_ACCESS, {
      canRecover: true,
      action: () => {
        console.log('[ErrorHandler] Attempting theme access recovery...');
        // Force re-initialization of theme safety system
        try {
          require('../utils/globalThemeSafety').initializeGlobalThemeSafety();
        } catch (error) {
          console.error('[ErrorHandler] Theme recovery failed:', error);
        }
      },
      fallback: {
        colors: { primary: '#4A6B52', text: '#000000', background: '#FFFFFF' },
        spacing: { sm: 4, md: 8, lg: 16 },
        typography: { fontSize: { base: 14 } },
      },
      maxRetries: 3,
    });

    // Module loading error recovery
    this.recoveryStrategies.set(ErrorType.MODULE_LOADING, {
      canRecover: true,
      action: () => {
        console.log('[ErrorHandler] Attempting module loading recovery...');
        // Clear module cache and retry
        if (Platform.OS === 'android') {
          // Android-specific recovery
          console.log('[ErrorHandler] Clearing Android module cache...');
        }
      },
      maxRetries: 2,
    });

    // Component render error recovery
    this.recoveryStrategies.set(ErrorType.COMPONENT_RENDER, {
      canRecover: true,
      action: () => {
        console.log('[ErrorHandler] Attempting component render recovery...');
        // Force component re-render with safe props
      },
      fallback: null, // Will render error boundary
      maxRetries: 1,
    });

    // Network error recovery
    this.recoveryStrategies.set(ErrorType.NETWORK, {
      canRecover: true,
      action: () => {
        console.log('[ErrorHandler] Attempting network recovery...');
        // Retry network request with exponential backoff
      },
      maxRetries: 3,
    });
  }

  /**
   * Setup global error handlers
   */
  private setupGlobalErrorHandlers(): void {
    // Global error handler
    const originalHandler = global.ErrorUtils?.getGlobalHandler();
    
    global.ErrorUtils?.setGlobalHandler((error: Error, isFatal: boolean) => {
      this.handleError(error, {
        type: this.categorizeError(error),
        severity: isFatal ? ErrorSeverity.CRITICAL : ErrorSeverity.HIGH,
        message: error.message,
        stack: error.stack,
        timestamp: Date.now(),
      });

      // Call original handler if it exists
      if (originalHandler) {
        originalHandler(error, isFatal);
      }
    });

    // Unhandled promise rejection handler
    const originalRejectionHandler = global.onunhandledrejection;
    
    global.onunhandledrejection = (event: any) => {
      this.handleError(new Error(event.reason), {
        type: ErrorType.UNKNOWN,
        severity: ErrorSeverity.MEDIUM,
        message: `Unhandled promise rejection: ${event.reason}`,
        timestamp: Date.now(),
      });

      if (originalRejectionHandler) {
        originalRejectionHandler(event);
      }
    };
  }

  /**
   * Categorize error based on message and stack trace
   */
  private categorizeError(error: Error): ErrorType {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    if (message.includes('cannot read property') && (message.includes('undefined') || message.includes('null'))) {
      if (stack.includes('theme') || message.includes('colors') || message.includes('spacing')) {
        return ErrorType.THEME_ACCESS;
      }
    }

    if (message.includes('module') || message.includes('require') || stack.includes('metrorequire')) {
      return ErrorType.MODULE_LOADING;
    }

    if (message.includes('render') || stack.includes('react') || stack.includes('component')) {
      return ErrorType.COMPONENT_RENDER;
    }

    if (message.includes('network') || message.includes('fetch') || message.includes('xhr')) {
      return ErrorType.NETWORK;
    }

    if (message.includes('storage') || message.includes('asyncstorage')) {
      return ErrorType.STORAGE;
    }

    return ErrorType.UNKNOWN;
  }

  /**
   * Handle error with recovery strategies
   */
  public handleError(error: Error, context: Partial<ErrorContext> = {}): void {
    const errorContext: ErrorContext = {
      type: context.type || this.categorizeError(error),
      severity: context.severity || ErrorSeverity.MEDIUM,
      message: context.message || error.message,
      stack: context.stack || error.stack,
      component: context.component,
      props: context.props,
      timestamp: context.timestamp || Date.now(),
      userAgent: Platform.OS,
      appVersion: '1.0.0',
    };

    // Log error
    this.logError(errorContext);

    // Attempt recovery
    this.attemptRecovery(errorContext);

    // Report error if critical
    if (errorContext.severity === ErrorSeverity.CRITICAL) {
      this.reportCriticalError(errorContext);
    }
  }

  /**
   * Log error to local storage and console
   */
  private logError(context: ErrorContext): void {
    console.error(`[ErrorHandler] ${context.type}:`, context.message);
    console.error('[ErrorHandler] Stack:', context.stack);

    // Add to error log
    this.errorLog.unshift(context);

    // Maintain log size
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }

    // Persist to storage (non-blocking)
    this.persistErrorLog().catch(err => {
      console.warn('[ErrorHandler] Failed to persist error log:', err);
    });
  }

  /**
   * Attempt error recovery
   */
  private attemptRecovery(context: ErrorContext): void {
    const strategy = this.recoveryStrategies.get(context.type);
    
    if (!strategy || !strategy.canRecover) {
      console.log(`[ErrorHandler] No recovery strategy for ${context.type}`);
      return;
    }

    try {
      console.log(`[ErrorHandler] Attempting recovery for ${context.type}...`);
      strategy.action();
      console.log(`[ErrorHandler] Recovery attempt completed for ${context.type}`);
    } catch (recoveryError) {
      console.error(`[ErrorHandler] Recovery failed for ${context.type}:`, recoveryError);
    }
  }

  /**
   * Report critical errors to user
   */
  private reportCriticalError(context: ErrorContext): void {
    const userMessage = this.getUserFriendlyMessage(context);
    
    Alert.alert(
      'We\'re Fixing This',
      userMessage,
      [
        {
          text: 'Restart App',
          onPress: () => {
            // In a real app, you would restart the app
            console.log('[ErrorHandler] User requested app restart');
          },
        },
        {
          text: 'Continue',
          style: 'cancel',
          onPress: () => {
            console.log('[ErrorHandler] User chose to continue');
          },
        },
      ]
    );
  }

  /**
   * Get user-friendly error message
   */
  private getUserFriendlyMessage(context: ErrorContext): string {
    switch (context.type) {
      case ErrorType.THEME_ACCESS:
        return 'The app\'s appearance isn\'t loading correctly. We\'re fixing this automatically - you can continue using the app normally.';

      case ErrorType.MODULE_LOADING:
        return 'Some features couldn\'t load properly. Please restart the app to get everything working again.';

      case ErrorType.COMPONENT_RENDER:
        return 'This page isn\'t displaying correctly. We\'re fixing it automatically - please try refreshing or going back.';

      case ErrorType.NETWORK:
        return 'We couldn\'t connect to our servers. Please check your internet connection and try again.';

      case ErrorType.STORAGE:
        return 'We couldn\'t save your changes. Please check your device storage space and try again.';
      
      default:
        return 'Something unexpected happened. We\'re working to fix it automatically - you can continue using the app.';
    }
  }

  /**
   * Persist error log to storage
   */
  private async persistErrorLog(): Promise<void> {
    try {
      const logData = JSON.stringify(this.errorLog.slice(0, 50)); // Store last 50 errors
      await AsyncStorage.setItem('error_log', logData);
    } catch (error) {
      console.warn('[ErrorHandler] Failed to persist error log:', error);
    }
  }

  /**
   * Get error statistics
   */
  public getErrorStats(): { [key in ErrorType]: number } {
    const stats = Object.values(ErrorType).reduce((acc, type) => {
      acc[type] = 0;
      return acc;
    }, {} as { [key in ErrorType]: number });

    this.errorLog.forEach(error => {
      stats[error.type]++;
    });

    return stats;
  }

  /**
   * Clear error log
   */
  public clearErrorLog(): void {
    this.errorLog = [];
    AsyncStorage.removeItem('error_log').catch(err => {
      console.warn('[ErrorHandler] Failed to clear error log:', err);
    });
  }

  /**
   * Get safe fallback value for theme properties
   */
  public getSafeFallback(path: string, defaultValue: any = undefined): any {
    const strategy = this.recoveryStrategies.get(ErrorType.THEME_ACCESS);
    
    if (!strategy?.fallback) {
      return defaultValue;
    }

    try {
      const pathArray = path.split('.');
      let value = strategy.fallback;
      
      for (const key of pathArray) {
        if (value && typeof value === 'object' && key in value) {
          value = value[key];
        } else {
          return defaultValue;
        }
      }
      
      return value !== undefined ? value : defaultValue;
    } catch (error) {
      return defaultValue;
    }
  }
}

// Export singleton instance
export const runtimeErrorHandler = new RuntimeErrorHandler();

// Export utility functions
export const handleError = (error: Error, context?: Partial<ErrorContext>) => {
  runtimeErrorHandler.handleError(error, context);
};

export const getSafeFallback = (path: string, defaultValue?: any) => {
  return runtimeErrorHandler.getSafeFallback(path, defaultValue);
};

// Initialize error handler
console.log('[RuntimeErrorHandler] Initialized comprehensive error handling system');
