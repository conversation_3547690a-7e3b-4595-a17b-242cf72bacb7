/**
 * Performance Monitoring Dashboard
 * 
 * This component provides a comprehensive performance monitoring dashboard
 * for development and debugging purposes.
 * 
 * Features:
 * - Real-time performance metrics
 * - Bundle size analysis
 * - Memory usage monitoring
 * - Network request statistics
 * - Component render performance
 * 
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsive';
import { getMemoizationStats, logMemoizationReport } from '../../utils/memoizationUtils';
import { getRequestStats } from '../../utils/requestOptimization';
import { getBundleMetrics } from '../../utils/bundleOptimization';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface PerformanceDashboardProps {
  visible: boolean;
  onClose: () => void;
}

export const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({
  visible,
  onClose,
}) => {
  const { colors } = useTheme();
  const [activeTab, setActiveTab] = useState<'overview' | 'network' | 'components' | 'bundle'>('overview');
  const [refreshKey, setRefreshKey] = useState(0);

  // Performance data
  const [performanceData, setPerformanceData] = useState({
    memory: { used: 0, total: 0 },
    network: getRequestStats(),
    components: getMemoizationStats(),
    bundle: getBundleMetrics(),
  });

  // Refresh data periodically
  useEffect(() => {
    if (!visible) return;

    const interval = setInterval(() => {
      setPerformanceData({
        memory: getMemoryInfo(),
        network: getRequestStats(),
        components: getMemoizationStats(),
        bundle: getBundleMetrics(),
      });
      setRefreshKey(prev => prev + 1);
    }, 2000);

    return () => clearInterval(interval);
  }, [visible]);

  // Get memory information
  const getMemoryInfo = () => {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      const memory = (performance as any).memory;
      return {
        used: memory.usedJSHeapSize / 1024 / 1024, // MB
        total: memory.totalJSHeapSize / 1024 / 1024, // MB
      };
    }
    return { used: 0, total: 0 };
  };

  // Tab navigation
  const tabs = [
    { key: 'overview', label: 'Overview' },
    { key: 'network', label: 'Network' },
    { key: 'components', label: 'Components' },
    { key: 'bundle', label: 'Bundle' },
  ] as const;

  // Render metric card
  const renderMetricCard = (title: string, value: string | number, subtitle?: string, color?: string) => (
    <View style={[styles.metricCard, { backgroundColor: colors.background.secondary }]}>
      <Text style={[styles.metricTitle, { color: colors.text.secondary }]}>{title}</Text>
      <Text style={[styles.metricValue, { color: color || colors.text.primary }]}>
        {typeof value === 'number' ? value.toFixed(1) : value}
      </Text>
      {subtitle && (
        <Text style={[styles.metricSubtitle, { color: colors.text.tertiary }]}>{subtitle}</Text>
      )}
    </View>
  );

  // Render overview tab
  const renderOverview = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>System Performance</Text>
      
      <View style={styles.metricsGrid}>
        {renderMetricCard(
          'Memory Usage',
          `${performanceData.memory.used.toFixed(1)} MB`,
          `of ${performanceData.memory.total.toFixed(1)} MB`,
          performanceData.memory.used > performanceData.memory.total * 0.8 ? colors.status.error : colors.sage400
        )}
        
        {renderMetricCard(
          'Cache Hit Rate',
          `${performanceData.network.cacheHitRate.toFixed(1)}%`,
          'Network requests',
          performanceData.network.cacheHitRate > 70 ? colors.sage400 : colors.status.warning
        )}
        
        {renderMetricCard(
          'Avg Response Time',
          `${performanceData.network.averageResponseTime.toFixed(0)}ms`,
          'API requests',
          performanceData.network.averageResponseTime < 500 ? colors.sage400 : colors.status.warning
        )}
        
        {renderMetricCard(
          'Components Tracked',
          performanceData.components.length,
          'Memoized components'
        )}
      </View>

      <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>Quick Actions</Text>
      
      <TouchableOpacity
        style={[styles.actionButton, { backgroundColor: colors.sage400 }]}
        onPress={() => {
          logMemoizationReport();
          console.log('Performance report logged to console');
        }}
      >
        <Text style={[styles.actionButtonText, { color: colors.text.onPrimary }]}>
          Log Performance Report
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );

  // Render network tab
  const renderNetwork = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>Network Statistics</Text>
      
      <View style={styles.metricsGrid}>
        {renderMetricCard('Total Requests', performanceData.network.totalRequests)}
        {renderMetricCard('Cache Hit Rate', `${performanceData.network.cacheHitRate.toFixed(1)}%`)}
        {renderMetricCard('Deduplication Rate', `${performanceData.network.deduplicationRate.toFixed(1)}%`)}
        {renderMetricCard('Slow Requests', performanceData.network.slowRequests)}
      </View>
    </ScrollView>
  );

  // Render components tab
  const renderComponents = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>Component Performance</Text>
      
      {performanceData.components.map((component, index) => {
        const hitRate = component.memoHits / (component.memoHits + component.memoMisses) * 100;
        return (
          <View key={index} style={[styles.componentCard, { backgroundColor: colors.background.secondary }]}>
            <Text style={[styles.componentName, { color: colors.text.primary }]}>
              {component.componentName}
            </Text>
            <View style={styles.componentStats}>
              <Text style={[styles.componentStat, { color: colors.text.secondary }]}>
                Renders: {component.renderCount}
              </Text>
              <Text style={[styles.componentStat, { color: colors.text.secondary }]}>
                Hit Rate: {hitRate.toFixed(1)}%
              </Text>
              <Text style={[styles.componentStat, { color: colors.text.secondary }]}>
                Avg Time: {component.averageRenderTime.toFixed(2)}ms
              </Text>
            </View>
          </View>
        );
      })}
    </ScrollView>
  );

  // Render bundle tab
  const renderBundle = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>Bundle Analysis</Text>
      
      <View style={styles.metricsGrid}>
        {renderMetricCard('Total Chunks', performanceData.bundle.registry.totalChunks)}
        {renderMetricCard('Cached Chunks', performanceData.bundle.registry.cachedChunks)}
        {renderMetricCard('Avg Load Time', `${performanceData.bundle.registry.averageLoadTime.toFixed(0)}ms`)}
      </View>

      {performanceData.bundle.recommendations.length > 0 && (
        <>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>Recommendations</Text>
          {performanceData.bundle.recommendations.map((rec, index) => (
            <View key={index} style={[styles.recommendationCard, { backgroundColor: colors.background.tertiary }]}>
              <Text style={[styles.recommendationText, { color: colors.text.secondary }]}>
                {rec}
              </Text>
            </View>
          ))}
        </>
      )}
    </ScrollView>
  );

  // Render tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview();
      case 'network':
        return renderNetwork();
      case 'components':
        return renderComponents();
      case 'bundle':
        return renderBundle();
      default:
        return renderOverview();
    }
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: colors.border.light }]}>
          <Text style={[styles.title, { color: colors.text.primary }]}>
            Performance Dashboard
          </Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={[styles.closeButtonText, { color: colors.sage400 }]}>Done</Text>
          </TouchableOpacity>
        </View>

        {/* Tab Navigation */}
        <View style={[styles.tabBar, { borderBottomColor: colors.border.light }]}>
          {tabs.map(tab => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.tab,
                activeTab === tab.key && { borderBottomColor: colors.sage400 }
              ]}
              onPress={() => setActiveTab(tab.key)}
            >
              <Text
                style={[
                  styles.tabText,
                  { color: activeTab === tab.key ? colors.sage400 : colors.text.secondary }
                ]}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Tab Content */}
        {renderTabContent()}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(20),
    paddingVertical: getResponsiveSpacing(16),
    borderBottomWidth: 1,
  },
  title: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '600',
  },
  closeButton: {
    padding: getResponsiveSpacing(8),
  },
  closeButtonText: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '500',
  },
  tabBar: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  tab: {
    flex: 1,
    paddingVertical: getResponsiveSpacing(12),
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
  },
  tabContent: {
    flex: 1,
    padding: getResponsiveSpacing(20),
  },
  sectionTitle: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    marginBottom: getResponsiveSpacing(16),
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -getResponsiveSpacing(8),
    marginBottom: getResponsiveSpacing(24),
  },
  metricCard: {
    width: (SCREEN_WIDTH - getResponsiveSpacing(56)) / 2,
    margin: getResponsiveSpacing(8),
    padding: getResponsiveSpacing(16),
    borderRadius: 8,
  },
  metricTitle: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '500',
    marginBottom: getResponsiveSpacing(4),
  },
  metricValue: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: '700',
    marginBottom: getResponsiveSpacing(2),
  },
  metricSubtitle: {
    fontSize: getResponsiveFontSize(10),
  },
  actionButton: {
    padding: getResponsiveSpacing(16),
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(16),
  },
  actionButtonText: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
  },
  componentCard: {
    padding: getResponsiveSpacing(16),
    borderRadius: 8,
    marginBottom: getResponsiveSpacing(12),
  },
  componentName: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    marginBottom: getResponsiveSpacing(8),
  },
  componentStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  componentStat: {
    fontSize: getResponsiveFontSize(12),
  },
  recommendationCard: {
    padding: getResponsiveSpacing(12),
    borderRadius: 6,
    marginBottom: getResponsiveSpacing(8),
  },
  recommendationText: {
    fontSize: getResponsiveFontSize(14),
  },
});

export default PerformanceDashboard;
