/**
 * Advanced Testing Utilities
 *
 * Provides comprehensive testing utilities for React Native components,
 * including custom render functions, mock factories, and testing helpers.
 *
 * Features:
 * - Custom render with providers
 * - Mock data factories
 * - Async testing utilities
 * - Performance testing helpers
 * - Accessibility testing utilities
 * - Integration testing helpers
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { ReactElement } from 'react';
import { render, RenderOptions, renderHook, RenderHookOptions } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
// SafeAreaProvider removed - using custom safe area handling
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { ThemeProvider } from '../contexts/ThemeContext';
import { AccessibilityProvider } from '../contexts/AccessibilityContext';

// Mock store configuration
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      // Add your reducers here
      auth: (state = { user: null, isAuthenticated: false }, action) => state,
      services: (state = { services: [], loading: false }, action) => state,
      bookings: (state = { bookings: [], loading: false }, action) => state,
    },
    preloadedState: initialState,
  });
};

// Custom render function with all providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialState?: any;
  store?: any;
  navigationOptions?: any;
}

export const renderWithProviders = (
  ui: ReactElement,
  {
    initialState = {},
    store = createMockStore(initialState),
    navigationOptions = {},
    ...renderOptions
  }: CustomRenderOptions = {}
) => {
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <Provider store={store}>
      <ThemeProvider>
        <AccessibilityProvider>
          <NavigationContainer {...navigationOptions}>
            {children}
          </NavigationContainer>
        </AccessibilityProvider>
      </ThemeProvider>
    </Provider>
  );

  return {
    store,
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
  };
};

// Custom hook render function with all providers
export const renderHookWithProviders = <TProps, TResult>(
  hook: (props: TProps) => TResult,
  options: RenderHookOptions<TProps> & CustomRenderOptions = {}
) => {
  const {
    initialState = {},
    store = createMockStore(initialState),
    navigationOptions = {},
    ...hookOptions
  } = options;

  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <Provider store={store}>
      <NavigationContainer {...navigationOptions}>
        <ThemeProvider>
          <AccessibilityProvider>
            {children}
          </AccessibilityProvider>
        </ThemeProvider>
      </NavigationContainer>
    </Provider>
  );

  return renderHook(hook, { wrapper: Wrapper, ...hookOptions });
};

// Mock data factories
export const mockFactories = {
  user: (overrides = {}) => ({
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    avatar: 'https://example.com/avatar.jpg',
    role: 'customer',
    ...overrides,
  }),

  service: (overrides = {}) => ({
    id: '1',
    name: 'Test Service',
    description: 'A test service',
    price: 50,
    duration: 60,
    category: 'beauty',
    providerId: '1',
    isActive: true,
    ...overrides,
  }),

  booking: (overrides = {}) => ({
    id: '1',
    serviceId: '1',
    customerId: '1',
    providerId: '1',
    date: new Date().toISOString(),
    status: 'confirmed',
    totalPrice: 50,
    ...overrides,
  }),

  provider: (overrides = {}) => ({
    id: '1',
    name: 'Test Provider',
    email: '<EMAIL>',
    businessName: 'Test Business',
    location: 'Test Location',
    rating: 4.5,
    reviewCount: 100,
    ...overrides,
  }),
};

// Async testing utilities
export const waitForAsync = (ms: number = 0) => 
  new Promise(resolve => setTimeout(resolve, ms));

export const waitForCondition = async (
  condition: () => boolean,
  timeout: number = 5000,
  interval: number = 100
): Promise<void> => {
  const startTime = Date.now();
  
  while (!condition() && Date.now() - startTime < timeout) {
    await waitForAsync(interval);
  }
  
  if (!condition()) {
    throw new Error(`Condition not met within ${timeout}ms`);
  }
};

// Performance testing utilities
export const measurePerformance = async (fn: () => Promise<void> | void) => {
  const startTime = performance.now();
  await fn();
  const endTime = performance.now();
  return endTime - startTime;
};

export const expectPerformance = async (
  fn: () => Promise<void> | void,
  maxTime: number
) => {
  const duration = await measurePerformance(fn);
  expect(duration).toBeLessThan(maxTime);
  return duration;
};

// Performance test utilities object for backward compatibility
export const performanceTestUtils = {
  measureRenderTime: measurePerformance,
  expectRenderTimeBelow: (time: number, maxTime: number) => {
    expect(time).toBeLessThan(maxTime);
  },
  measureReRenderTime: async (renderResult: any, action: () => void) => {
    return await measurePerformance(action);
  },
};

// Memory testing utilities
export const measureMemoryUsage = () => {
  if (typeof performance !== 'undefined' && performance.memory) {
    return {
      used: performance.memory.usedJSHeapSize,
      total: performance.memory.totalJSHeapSize,
      limit: performance.memory.jsHeapSizeLimit,
    };
  }
  return null;
};

// Accessibility testing utilities
export const expectAccessibleElement = (element: any) => {
  expect(element).toHaveAccessibilityRole();
  expect(element).toHaveAccessibilityLabel();
  
  // Check for minimum touch target size
  const style = element.props.style;
  if (style && (style.width || style.height)) {
    const width = style.width || 44;
    const height = style.height || 44;
    expect(width).toBeGreaterThanOrEqual(44);
    expect(height).toBeGreaterThanOrEqual(44);
  }
};

// Network testing utilities
export const mockNetworkResponse = (data: any, status: number = 200) => {
  return Promise.resolve({
    ok: status >= 200 && status < 300,
    status,
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data)),
  });
};

export const mockNetworkError = (message: string = 'Network Error') => {
  return Promise.reject(new Error(message));
};

// Component testing utilities
export const getByTestIdSafe = (getByTestId: any, testId: string) => {
  try {
    return getByTestId(testId);
  } catch {
    return null;
  }
};

export const queryByTestIdSafe = (queryByTestId: any, testId: string) => {
  return queryByTestId(testId);
};

// Form testing utilities
export const fillForm = async (
  getByTestId: any,
  formData: Record<string, string>
) => {
  for (const [fieldTestId, value] of Object.entries(formData)) {
    const field = getByTestId(fieldTestId);
    expect(field).toBeTruthy();
    // Simulate text input
    field.props.onChangeText?.(value);
  }
};

// Navigation testing utilities
export const mockNavigation = {
  navigate: jest.fn(),
  goBack: jest.fn(),
  dispatch: jest.fn(),
  reset: jest.fn(),
  canGoBack: jest.fn(() => true),
  isFocused: jest.fn(() => true),
  addListener: jest.fn(),
  removeListener: jest.fn(),
  setOptions: jest.fn(),
  setParams: jest.fn(),
  getState: jest.fn(),
  getParent: jest.fn(),
};

export const mockRoute = {
  key: 'test-key',
  name: 'TestScreen',
  params: {},
};

// Error testing utilities
export const expectError = async (fn: () => Promise<void> | void, errorMessage?: string) => {
  try {
    await fn();
    throw new Error('Expected function to throw an error');
  } catch (error) {
    if (errorMessage) {
      expect(error.message).toContain(errorMessage);
    }
    return error;
  }
};

// State testing utilities
export const expectStateChange = (
  initialState: any,
  action: any,
  expectedState: any,
  reducer: (state: any, action: any) => any
) => {
  const newState = reducer(initialState, action);
  expect(newState).toEqual(expectedState);
};

// Animation testing utilities
export const mockAnimatedValue = (initialValue: number = 0) => ({
  setValue: jest.fn(),
  addListener: jest.fn(),
  removeListener: jest.fn(),
  removeAllListeners: jest.fn(),
  stopAnimation: jest.fn(),
  resetAnimation: jest.fn(),
  interpolate: jest.fn(() => mockAnimatedValue()),
  animate: jest.fn(),
  _value: initialValue,
});

// Snapshot testing utilities
export const createSnapshot = (component: ReactElement, options?: CustomRenderOptions) => {
  const { container } = renderWithProviders(component, options);
  expect(container).toMatchSnapshot();
};

// Integration testing utilities
export const createIntegrationTest = (
  testName: string,
  testFn: () => Promise<void> | void
) => {
  it(`Integration: ${testName}`, async () => {
    // Setup integration test environment
    jest.setTimeout(10000); // Longer timeout for integration tests
    
    try {
      await testFn();
    } finally {
      // Cleanup
      jest.clearAllMocks();
    }
  });
};

// Test data cleanup utilities
export const cleanupTestData = () => {
  // Clear any test data, reset mocks, etc.
  jest.clearAllMocks();
  
  // Reset fetch mock
  if (global.fetch) {
    (global.fetch as jest.Mock).mockClear();
  }
};

// Custom matchers
expect.extend({
  toBeWithinRange(received: number, floor: number, ceiling: number) {
    const pass = received >= floor && received <= ceiling;
    if (pass) {
      return {
        message: () => `expected ${received} not to be within range ${floor} - ${ceiling}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be within range ${floor} - ${ceiling}`,
        pass: false,
      };
    }
  },
});

// Export everything
export * from '@testing-library/react-native';
export { renderWithProviders as render };

export default {
  renderWithProviders,
  mockFactories,
  waitForAsync,
  waitForCondition,
  measurePerformance,
  expectPerformance,
  measureMemoryUsage,
  expectAccessibleElement,
  mockNetworkResponse,
  mockNetworkError,
  getByTestIdSafe,
  queryByTestIdSafe,
  fillForm,
  mockNavigation,
  mockRoute,
  expectError,
  expectStateChange,
  mockAnimatedValue,
  createSnapshot,
  createIntegrationTest,
  cleanupTestData,
};
